"""
Intelligent Agriculture - Crop Recommendation System
===================================================

A comprehensive machine learning project implementing multiple algorithms
for intelligent agriculture and crop recommendation.

Based on: "Leveraging Machine Learning for Intelligent Agriculture"
(Springer, 2025)

Modules:
    - data_preprocessing: Data cleaning and preparation
    - feature_engineering: Feature creation and selection
    - models: ML model implementations
    - evaluation: Model evaluation and metrics
    - visualization: Plotting and visualization functions
"""

__version__ = "1.0.0"
__author__ = "AIML Engineering Team"
__license__ = "MIT"

# Import main modules for easy access
from . import data_preprocessing
from . import feature_engineering
from . import evaluation
from . import visualization

