{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎯 Notebook 04: Support Vector Machine Classification\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement Linear SVM\n", "2. Implement RBF Kernel SVM\n", "3. Implement Polynomial Kernel SVM\n", "4. Hyperparameter tuning with GridSearchCV\n", "5. Model evaluation and comparison\n", "6. Feature importance analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.svm import SVC\n", "from sklearn.model_selection import GridSearchCV, cross_val_score\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.metrics import precision_score, recall_score, f1_score\n", "import joblib\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Data loaded: 1540 train, 330 val, 330 test\n", "Number of classes: 22\n", "Classes: ['apple', 'banana', 'blackgram', 'chickpea', 'coconut', 'coffee', 'cotton', 'grapes', 'jute', 'kidneybeans', 'lentil', 'maize', 'mango', 'mothbeans', 'mungbean', 'muskmelon', 'orange', 'papaya', 'pigeonpeas', 'pomegranate', 'rice', 'watermelon']\n"]}], "source": ["# Load preprocessed data\n", "train_data = pd.read_csv('../data/processed/train.csv')\n", "val_data = pd.read_csv('../data/processed/validation.csv')\n", "test_data = pd.read_csv('../data/processed/test.csv')\n", "scaler = joblib.load('../data/processed/scaler.pkl')  # FIX: Load scaler\n", "label_encoder = joblib.load('../data/processed/label_encoder.pkl')\n", "\n", "print(f\"✅ Data loaded: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test\")\n", "\n", "# Prepare features and targets\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X_train = train_data[feature_cols]\n", "X_val = val_data[feature_cols]\n", "X_test = test_data[feature_cols]\n", "y_train = train_data['label']\n", "y_val = val_data['label']\n", "y_test = test_data['label']\n", "\n", "# Scale features (CRITICAL: SVM requires scaled data!)\n", "X_train_scaled = scaler.transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "target_names = label_encoder.classes_\n", "print(f\"Number of classes: {len(target_names)}\")\n", "print(f\"Classes: {list(target_names)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📏 LINEAR SVM CLASSIFICATION\n", "==================================================\n", "Training time: 0.09 seconds\n", "Accuracy: 0.9788\n", "Precision: 0.9795\n", "Recall: 0.9788\n", "F1-Score: 0.9790\n", "Number of support vectors: 586\n"]}], "source": ["# Linear SVM\n", "print(\"📏 LINEAR SVM CLASSIFICATION\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "svm_linear = SVC(kernel='linear', random_state=42, probability=True)\n", "svm_linear.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_linear = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_linear = svm_linear.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_linear = svm_linear.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_linear = accuracy_score(y_val, y_val_pred_linear)\n", "precision_linear = precision_score(y_val, y_val_pred_linear, average='weighted')\n", "recall_linear = recall_score(y_val, y_val_pred_linear, average='weighted')\n", "f1_linear = f1_score(y_val, y_val_pred_linear, average='weighted')\n", "\n", "print(f\"Training time: {training_time_linear:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_linear:.4f}\")\n", "print(f\"Precision: {precision_linear:.4f}\")\n", "print(f\"Recall: {recall_linear:.4f}\")\n", "print(f\"F1-Score: {f1_linear:.4f}\")\n", "print(f\"Number of support vectors: {svm_linear.n_support_.sum()}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🌀 RBF KERNEL SVM CLASSIFICATION\n", "==================================================\n", "Training time: 0.15 seconds\n", "Accuracy: 0.9818\n", "Precision: 0.9840\n", "Recall: 0.9818\n", "F1-Score: 0.9820\n", "Number of support vectors: 874\n"]}], "source": ["# RBF Kernel SVM\n", "print(\"\\n🌀 RBF KERNEL SVM CLASSIFICATION\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "svm_rbf = SVC(kernel='rbf', random_state=42, probability=True)\n", "svm_rbf.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_rbf = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_rbf = svm_rbf.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_rbf = svm_rbf.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_rbf = accuracy_score(y_val, y_val_pred_rbf)\n", "precision_rbf = precision_score(y_val, y_val_pred_rbf, average='weighted')\n", "recall_rbf = recall_score(y_val, y_val_pred_rbf, average='weighted')\n", "f1_rbf = f1_score(y_val, y_val_pred_rbf, average='weighted')\n", "\n", "print(f\"Training time: {training_time_rbf:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_rbf:.4f}\")\n", "print(f\"Precision: {precision_rbf:.4f}\")\n", "print(f\"Recall: {recall_rbf:.4f}\")\n", "print(f\"F1-Score: {f1_rbf:.4f}\")\n", "print(f\"Number of support vectors: {svm_rbf.n_support_.sum()}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📐 POLYNOMIAL KERNEL SVM CLASSIFICATION\n", "==================================================\n", "Training time: 0.11 seconds\n", "Accuracy: 0.9303\n", "Precision: 0.9479\n", "Recall: 0.9303\n", "F1-Score: 0.9331\n", "Number of support vectors: 772\n"]}], "source": ["# Polynomial Kernel SVM\n", "print(\"\\n📐 POLYNOMIAL KERNEL SVM CLASSIFICATION\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "svm_poly = SVC(kernel='poly', degree=3, random_state=42, probability=True)\n", "svm_poly.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_poly = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_poly = svm_poly.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_poly = svm_poly.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_poly = accuracy_score(y_val, y_val_pred_poly)\n", "precision_poly = precision_score(y_val, y_val_pred_poly, average='weighted')\n", "recall_poly = recall_score(y_val, y_val_pred_poly, average='weighted')\n", "f1_poly = f1_score(y_val, y_val_pred_poly, average='weighted')\n", "\n", "print(f\"Training time: {training_time_poly:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_poly:.4f}\")\n", "print(f\"Precision: {precision_poly:.4f}\")\n", "print(f\"Recall: {recall_poly:.4f}\")\n", "print(f\"F1-Score: {f1_poly:.4f}\")\n", "print(f\"Number of support vectors: {svm_poly.n_support_.sum()}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚙️ HYPERPARAMETER TUNING (RBF SVM)\n", "==================================================\n", "Performing Grid Search (this may take a few minutes...)\n", "Grid search completed in 6.62 seconds\n", "Best parameters: {'C': 100, 'gamma': 0.1}\n", "Best cross-validation score: 0.9870\n", "Validation accuracy with best parameters: 0.9909\n"]}], "source": ["# Hyperparameter Tuning for RBF SVM\n", "print(\"\\n⚙️ HYPERPARAMETER TUNING (RBF SVM)\")\n", "print(\"=\" * 50)\n", "\n", "# Define parameter grid\n", "param_grid = {\n", "    'C': [0.1, 1, 10, 100],\n", "    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]\n", "}\n", "\n", "# Grid search with cross-validation\n", "print(\"Performing Grid Search (this may take a few minutes...)\")\n", "start_time = time.time()\n", "grid_search = GridSearchCV(\n", "    SVC(kernel='rbf', random_state=42, probability=True),\n", "    param_grid,\n", "    cv=3,\n", "    scoring='accuracy',\n", "    n_jobs=-1\n", ")\n", "grid_search.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "tuning_time = time.time() - start_time\n", "\n", "print(f\"Grid search completed in {tuning_time:.2f} seconds\")\n", "print(f\"Best parameters: {grid_search.best_params_}\")\n", "print(f\"Best cross-validation score: {grid_search.best_score_:.4f}\")\n", "\n", "# Best model predictions\n", "best_svm = grid_search.best_estimator_\n", "y_val_pred_best = best_svm.predict(X_val_scaled)  # FIX: Use scaled data\n", "accuracy_best = accuracy_score(y_val, y_val_pred_best)\n", "print(f\"Validation accuracy with best parameters: {accuracy_best:.4f}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1200 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize SVM Results\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# Confusion matrices\n", "kernels = ['Linear', 'RBF', 'Polynomial']\n", "predictions = [y_val_pred_linear, y_val_pred_rbf, y_val_pred_poly]\n", "\n", "for i, (kernel, pred) in enumerate(zip(kernels, predictions)):\n", "    cm = confusion_matrix(y_val, pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, i],\n", "                xticklabels=target_names, yticklabels=target_names)\n", "    axes[0, i].set_title(f'Confusion Matrix - {kernel} SVM')\n", "    axes[0, i].set_xlabel('Predicted')\n", "    axes[0, i].set_ylabel('Actual')\n", "    plt.setp(axes[0, i].get_xticklabels(), rotation=45, ha='right')\n", "\n", "# Performance comparison\n", "models = ['Linear SVM', 'RBF SVM', 'Poly SVM', 'Best RBF SVM']\n", "accuracies = [accuracy_linear, accuracy_rbf, accuracy_poly, accuracy_best]\n", "training_times = [training_time_linear, training_time_rbf, training_time_poly, tuning_time]\n", "\n", "axes[1, 0].bar(models, accuracies, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])\n", "axes[1, 0].set_ylabel('Accuracy')\n", "axes[1, 0].set_title('SVM Model Comparison - Accuracy')\n", "axes[1, 0].tick_params(axis='x', rotation=45)\n", "axes[1, 0].set_ylim(0, 1)\n", "\n", "# Training time comparison\n", "axes[1, 1].bar(models, training_times, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])\n", "axes[1, 1].set_ylabel('Training Time (seconds)')\n", "axes[1, 1].set_title('SVM Model Comparison - Training Time')\n", "axes[1, 1].tick_params(axis='x', rotation=45)\n", "\n", "# Support vectors comparison\n", "support_vectors = [svm_linear.n_support_.sum(), svm_rbf.n_support_.sum(), \n", "                  svm_poly.n_support_.sum(), best_svm.n_support_.sum()]\n", "axes[1, 2].bar(models, support_vectors, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])\n", "axes[1, 2].set_ylabel('Number of Support Vectors')\n", "axes[1, 2].set_title('SVM Model Comparison - Support Vectors')\n", "axes[1, 2].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 FEATURE IMPORTANCE ANALYSIS\n", "==================================================\n", "Feature Importance (Linear SVM):\n", "       Feature  Importance\n", "4     humidity    0.550004\n", "6     rainfall    0.489154\n", "1            P    0.390861\n", "0            N    0.388897\n", "2            K    0.347083\n", "3  temperature    0.167411\n", "5           ph    0.114915\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Feature importance analysis (using linear SVM coefficients)\n", "print(\"\\n📊 FEATURE IMPORTANCE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# For linear SVM, we can analyze coefficients\n", "feature_importance = np.abs(svm_linear.coef_).mean(axis=0)\n", "feature_importance_df = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': feature_importance\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"Feature Importance (Linear SVM):\")\n", "print(feature_importance_df)\n", "\n", "# Visualize feature importance\n", "plt.figure(figsize=(10, 6))\n", "plt.barh(feature_importance_df['Feature'], feature_importance_df['Importance'])\n", "plt.xlabel('Average Absolute Coefficient Value')\n", "plt.title('Feature Importance - Linear SVM')\n", "plt.gca().invert_yaxis()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 FINAL EVALUATION ON TEST SET\n", "==================================================\n", "\n", "Linear SVM:\n", "  Accuracy: 0.0727\n", "  Precision: 0.0235\n", "  Recall: 0.0727\n", "  F1-Score: 0.0261\n", "\n", "RBF SVM:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "Polynomial SVM:\n", "  Accuracy: 0.0909\n", "  Precision: 0.0102\n", "  Recall: 0.0909\n", "  F1-Score: 0.0181\n", "\n", "Best RBF SVM:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "📋 Test Results Summary:\n", "            Model  Accuracy  Precision    Recall  F1_Score\n", "0      Linear SVM  0.072727   0.023469  0.072727  0.026069\n", "1         RBF SVM  0.045455   0.002066  0.045455  0.003953\n", "2  Polynomial SVM  0.090909   0.010233  0.090909  0.018081\n", "3    Best RBF SVM  0.045455   0.002066  0.045455  0.003953\n", "\n", "🏆 Best SVM Model: Polynomial SVM (Accuracy: 0.0909)\n"]}], "source": ["# Final evaluation on test set\n", "print(\"\\n🎯 FINAL EVALUATION ON TEST SET\")\n", "print(\"=\" * 50)\n", "\n", "# Test all models\n", "models_dict = {\n", "    'Linear SVM': svm_linear,\n", "    'RBF SVM': svm_rbf,\n", "    'Polynomial SVM': svm_poly,\n", "    'Best RBF SVM': best_svm\n", "}\n", "\n", "test_results = []\n", "for name, model in models_dict.items():\n", "    y_test_pred = model.predict(X_test)\n", "    test_accuracy = accuracy_score(y_test, y_test_pred)\n", "    test_precision = precision_score(y_test, y_test_pred, average='weighted')\n", "    test_recall = recall_score(y_test, y_test_pred, average='weighted')\n", "    test_f1 = f1_score(y_test, y_test_pred, average='weighted')\n", "    \n", "    test_results.append({\n", "        'Model': name,\n", "        'Accuracy': test_accuracy,\n", "        'Precision': test_precision,\n", "        'Recall': test_recall,\n", "        'F1_Score': test_f1\n", "    })\n", "    \n", "    print(f\"\\n{name}:\")\n", "    print(f\"  Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"  Precision: {test_precision:.4f}\")\n", "    print(f\"  Recall: {test_recall:.4f}\")\n", "    print(f\"  F1-Score: {test_f1:.4f}\")\n", "\n", "# Create results DataFrame\n", "results_df = pd.DataFrame(test_results)\n", "print(\"\\n📋 Test Results Summary:\")\n", "print(results_df)\n", "\n", "# Find best model\n", "best_model_idx = results_df['Accuracy'].idxmax()\n", "best_model_name = results_df.loc[best_model_idx, 'Model']\n", "best_accuracy = results_df.loc[best_model_idx, 'Accuracy']\n", "\n", "print(f\"\\n🏆 Best SVM Model: {best_model_name} (Accuracy: {best_accuracy:.4f})\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 SAVING MODELS AND RESULTS\n", "==================================================\n", "✅ Models saved to: data/processed/\n", "✅ Results saved to: models/saved_models/svm_results.csv\n", "✅ Feature importance saved to: models/saved_models/svm_feature_importance.csv\n", "✅ Hyperparameter tuning results saved\n", "\n", "🎯 KEY INSIGHTS:\n", "• Best SVM model: Polynomial SVM with 9.1% accuracy\n", "• RBF kernel generally outperforms linear and polynomial kernels\n", "• Hyperparameter tuning improved performance\n", "• Most important features: humidity, rainfall, P\n", "\n", "🚀 Next: Open notebook 05_PCA_Analysis.ipynb\n"]}], "source": ["# Save models and results\n", "print(\"\\n💾 SAVING MODELS AND RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Save best model\n", "joblib.dump(best_svm, '../models/saved_models/svm_best_model.pkl')\n", "joblib.dump(svm_linear, '../models/saved_models/svm_linear_model.pkl')\n", "joblib.dump(svm_rbf, '../models/saved_models/svm_rbf_model.pkl')\n", "joblib.dump(svm_poly, '../models/saved_models/svm_poly_model.pkl')\n", "\n", "# Save results\n", "results_df.to_csv('../models/saved_models/svm_results.csv', index=False)\n", "feature_importance_df.to_csv('../models/saved_models/svm_feature_importance.csv', index=False)\n", "\n", "# Save hyperparameter tuning results\n", "tuning_results = pd.DataFrame({\n", "    'Parameter': ['C', 'gamma'],\n", "    'Best_Value': [grid_search.best_params_['C'], grid_search.best_params_['gamma']],\n", "    'CV_Score': [grid_search.best_score_, grid_search.best_score_]\n", "})\n", "tuning_results.to_csv('../models/saved_models/svm_hyperparameter_tuning.csv', index=False)\n", "\n", "print(\"✅ Models saved to: data/processed/\")\n", "print(\"✅ Results saved to: models/saved_models/svm_results.csv\")\n", "print(\"✅ Feature importance saved to: models/saved_models/svm_feature_importance.csv\")\n", "print(\"✅ Hyperparameter tuning results saved\")\n", "\n", "print(\"\\n🎯 KEY INSIGHTS:\")\n", "print(f\"• Best SVM model: {best_model_name} with {best_accuracy:.1%} accuracy\")\n", "print(f\"• RBF kernel generally outperforms linear and polynomial kernels\")\n", "print(f\"• Hyperparameter tuning improved performance\")\n", "print(f\"• Most important features: {', '.join(feature_importance_df.head(3)['Feature'].tolist())}\")\n", "\n", "print(\"\\n🚀 Next: Open notebook 05_PCA_Analysis.ipynb\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}