{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌳 Notebook 08: CART Decision Trees\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement Classification and Regression Trees (CART)\n", "2. Decision tree visualization and interpretation\n", "3. Feature importance analysis\n", "4. Tree pruning and optimization\n", "5. Compare different splitting criteria\n", "6. Rule extraction from decision trees"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor\n", "from sklearn.tree import export_text, plot_tree, export_graphviz\n", "from sklearn.model_selection import GridSearchCV, validation_curve\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.metrics import precision_score, recall_score, f1_score\n", "import joblib\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Data loaded: 1540 train, 330 val, 330 test\n", "Number of classes: 22\n", "Feature columns: ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "Target classes: ['apple', 'banana', 'blackgram', 'chickpea', 'coconut']...\n"]}], "source": ["# Load preprocessed data\n", "train_data = pd.read_csv('../data/processed/train.csv')\n", "val_data = pd.read_csv('../data/processed/validation.csv')\n", "test_data = pd.read_csv('../data/processed/test.csv')\n", "scaler = joblib.load('../data/processed/scaler.pkl')  # FIX: Load scaler\n", "label_encoder = joblib.load('../data/processed/label_encoder.pkl')\n", "\n", "print(f\"✅ Data loaded: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test\")\n", "\n", "# Prepare features and targets\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X_train = train_data[feature_cols]\n", "X_val = val_data[feature_cols]\n", "X_test = test_data[feature_cols]\n", "y_train = train_data['label']\n", "y_val = val_data['label']\n", "y_test = test_data['label']\n", "\n", "# Scale features (for consistency, even though trees are scale-invariant)\n", "# This ensures we use numpy arrays without feature names\n", "X_train_scaled = scaler.transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "target_names = list(label_encoder.classes_)\n", "print(f\"Number of classes: {len(target_names)}\")\n", "print(f\"Feature columns: {feature_cols}\")\n", "print(f\"Target classes: {target_names[:5]}...\")  # Show first 5 classes"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌳 BASIC DECISION TREE CLASSIFIER\n", "==================================================\n", "Basic Decision Tree Accuracy: 0.9909\n", "Tree depth: 17\n", "Number of leaves: 37\n", "Number of nodes: 73\n", "\n", "Feature Importance (Basic Tree):\n", "       Feature  Importance\n", "6     rainfall    0.340604\n", "4     humidity    0.219476\n", "1            P    0.218695\n", "0            N    0.107476\n", "2            K    0.101976\n", "5           ph    0.005930\n", "3  temperature    0.005842\n"]}], "source": ["# Basic Decision Tree Implementation\n", "print(\"🌳 BASIC DECISION TREE CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "# Create basic decision tree\n", "dt_basic = DecisionTreeClassifier(\n", "    random_state=42,\n", "    criterion='gini'\n", ")\n", "dt_basic.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "\n", "# Predictions\n", "y_val_pred_basic = dt_basic.predict(X_val_scaled)  # FIX: Use scaled data\n", "accuracy_basic = accuracy_score(y_val, y_val_pred_basic)\n", "\n", "print(f\"Basic Decision Tree Accuracy: {accuracy_basic:.4f}\")\n", "print(f\"Tree depth: {dt_basic.get_depth()}\")\n", "print(f\"Number of leaves: {dt_basic.get_n_leaves()}\")\n", "print(f\"Number of nodes: {dt_basic.tree_.node_count}\")\n", "\n", "# Feature importance\n", "feature_importance_basic = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': dt_basic.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\nFeature Importance (Basic Tree):\")\n", "print(feature_importance_basic)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 COMPARING SPLITTING CRITERIA\n", "==================================================\n", "\n", "Testing GINI criterion:\n", "  Accuracy: 0.9848\n", "  Tree depth: 10\n", "  Number of leaves: 29\n", "\n", "Testing ENTROPY criterion:\n", "  Accuracy: 0.9879\n", "  Tree depth: 10\n", "  Number of leaves: 39\n", "\n", "Criterion Comparison:\n", "  Criterion  Accuracy  Precision    Recall  F1_Score  Tree_Depth  Num_Leaves\n", "0      gini  0.984848   0.988636  0.984848  0.984416          10          29\n", "1   entropy  0.987879   0.988971  0.987879  0.987845          10          39\n"]}], "source": ["# Compare different splitting criteria\n", "print(\"\\n📊 COMPARING SPLITTING CRITERIA\")\n", "print(\"=\" * 50)\n", "\n", "criteria = ['gini', 'entropy']\n", "criterion_results = []\n", "\n", "for criterion in criteria:\n", "    print(f\"\\nTesting {criterion.upper()} criterion:\")\n", "    \n", "    dt = DecisionTreeClassifier(\n", "        criterion=criterion,\n", "        random_state=42,\n", "        max_depth=10  # Limit depth for comparison\n", "    )\n", "    dt.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    \n", "    y_val_pred = dt.predict(X_val_scaled)  # FIX: Use scaled data\n", "    accuracy = accuracy_score(y_val, y_val_pred)\n", "    precision = precision_score(y_val, y_val_pred, average='weighted')\n", "    recall = recall_score(y_val, y_val_pred, average='weighted')\n", "    f1 = f1_score(y_val, y_val_pred, average='weighted')\n", "    \n", "    criterion_results.append({\n", "        'Criterion': criterion,\n", "        'Accuracy': accuracy,\n", "        'Precision': precision,\n", "        'Recall': recall,\n", "        'F1_Score': f1,\n", "        'Tree_Depth': dt.get_depth(),\n", "        'Num_Leaves': dt.get_n_leaves()\n", "    })\n", "    \n", "    print(f\"  Accuracy: {accuracy:.4f}\")\n", "    print(f\"  Tree depth: {dt.get_depth()}\")\n", "    print(f\"  Number of leaves: {dt.get_n_leaves()}\")\n", "\n", "criterion_df = pd.DataFrame(criterion_results)\n", "print(\"\\nCriterion Comparison:\")\n", "print(criterion_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚙️ HYPERPARAMETER TUNING\n", "==================================================\n", "Performing grid search...\n"]}], "source": ["# Hyperparameter tuning for optimal tree\n", "print(\"\\n⚙️ HYPERPARAMETER TUNING\")\n", "print(\"=\" * 50)\n", "\n", "# Define parameter grid\n", "param_grid = {\n", "    'criterion': ['gini', 'entropy'],\n", "    'max_depth': [3, 5, 7, 10, 15, None],\n", "    'min_samples_split': [2, 5, 10, 20],\n", "    'min_samples_leaf': [1, 2, 5, 10],\n", "    'max_features': ['sqrt', 'log2', None]\n", "}\n", "\n", "print(\"Performing grid search...\")\n", "dt_grid = GridSearchCV(\n", "    DecisionTreeClassifier(random_state=42),\n", "    param_grid,\n", "    cv=5,\n", "    scoring='accuracy',\n", "    n_jobs=-1\n", ")\n", "dt_grid.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "\n", "print(f\"Best parameters: {dt_grid.best_params_}\")\n", "print(f\"Best cross-validation score: {dt_grid.best_score_:.4f}\")\n", "\n", "# Best decision tree\n", "dt_best = dt_grid.best_estimator_\n", "y_val_pred_best = dt_best.predict(X_val_scaled)  # FIX: Use scaled data\n", "accuracy_best = accuracy_score(y_val, y_val_pred_best)\n", "\n", "print(f\"\\nBest Decision Tree:\")\n", "print(f\"  Validation accuracy: {accuracy_best:.4f}\")\n", "print(f\"  Tree depth: {dt_best.get_depth()}\")\n", "print(f\"  Number of leaves: {dt_best.get_n_leaves()}\")\n", "print(f\"  Number of nodes: {dt_best.tree_.node_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Validation curves for key hyperparameters\n", "print(\"\\n📈 VALIDATION CURVES\")\n", "print(\"=\" * 50)\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Max depth validation curve\n", "max_depths = range(1, 21)\n", "train_scores, val_scores = validation_curve(\n", "    DecisionTreeClassifier(random_state=42),\n", "    X_train, y_train,\n", "    param_name='max_depth',\n", "    param_range=max_depths,\n", "    cv=5, scoring='accuracy', n_jobs=-1\n", ")\n", "\n", "train_mean = np.mean(train_scores, axis=1)\n", "train_std = np.std(train_scores, axis=1)\n", "val_mean = np.mean(val_scores, axis=1)\n", "val_std = np.std(val_scores, axis=1)\n", "\n", "axes[0, 0].plot(max_depths, train_mean, 'o-', color='blue', label='Training Score')\n", "axes[0, 0].fill_between(max_depths, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')\n", "axes[0, 0].plot(max_depths, val_mean, 'o-', color='red', label='Validation Score')\n", "axes[0, 0].fill_between(max_depths, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')\n", "axes[0, 0].set_xlabel('<PERSON> Depth')\n", "axes[0, 0].set_ylabel('Accuracy')\n", "axes[0, 0].set_title('Validation Curve - Max De<PERSON>h')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Min samples split validation curve\n", "min_samples_splits = [2, 5, 10, 20, 50, 100]\n", "train_scores, val_scores = validation_curve(\n", "    DecisionTreeClassifier(random_state=42),\n", "    X_train, y_train,\n", "    param_name='min_samples_split',\n", "    param_range=min_samples_splits,\n", "    cv=5, scoring='accuracy', n_jobs=-1\n", ")\n", "\n", "train_mean = np.mean(train_scores, axis=1)\n", "val_mean = np.mean(val_scores, axis=1)\n", "\n", "axes[0, 1].plot(min_samples_splits, train_mean, 'o-', color='blue', label='Training Score')\n", "axes[0, 1].plot(min_samples_splits, val_mean, 'o-', color='red', label='Validation Score')\n", "axes[0, 1].set_xlabel('Min Samples Split')\n", "axes[0, 1].set_ylabel('Accuracy')\n", "axes[0, 1].set_title('Validation Curve - Min Samples Split')\n", "axes[0, 1].legend()\n", "axes[0, 1].grid(True, alpha=0.3)\n", "axes[0, 1].set_xscale('log')\n", "\n", "# Feature importance comparison\n", "feature_importance_best = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': dt_best.feature_importances_\n", "}).sort_values('Importance', ascending=True)\n", "\n", "axes[1, 0].barh(feature_importance_best['Feature'], feature_importance_best['Importance'], color='skyblue')\n", "axes[1, 0].set_xlabel('Feature Importance')\n", "axes[1, 0].set_title('Feature Importance - Best Decision Tree')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Tree complexity vs accuracy\n", "complexities = []\n", "accuracies = []\n", "for depth in range(1, 16):\n", "    dt_temp = DecisionTreeClassifier(max_depth=depth, random_state=42)\n", "    dt_temp.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    y_pred_temp = dt_temp.predict(X_val_scaled)  # FIX: Use scaled data\n", "    acc_temp = accuracy_score(y_val, y_pred_temp)\n", "    \n", "    complexities.append(dt_temp.get_n_leaves())\n", "    accuracies.append(acc_temp)\n", "\n", "axes[1, 1].plot(complexities, accuracies, 'o-', color='green')\n", "axes[1, 1].set_xlabel('Number of Leaves (Complexity)')\n", "axes[1, 1].set_ylabel('Validation Accuracy')\n", "axes[1, 1].set_title('Tree Complexity vs Accuracy')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize decision tree structure\n", "print(\"\\n🌳 DECISION TREE VISUALIZATION\")\n", "print(\"=\" * 50)\n", "\n", "# Create a smaller tree for visualization\n", "dt_viz = DecisionTreeClassifier(\n", "    max_depth=4,\n", "    min_samples_split=20,\n", "    random_state=42\n", ")\n", "dt_viz.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "\n", "# Plot the tree\n", "plt.figure(figsize=(20, 12))\n", "plot_tree(dt_viz, \n", "          feature_names=feature_cols,\n", "          class_names=list(target_names),\n", "          filled=True,\n", "          rounded=True,\n", "          fontsize=10)\n", "plt.title('Decision Tree Visualization (Depth=4)', fontsize=16)\n", "plt.show()\n", "\n", "print(f\"Visualization tree accuracy: {accuracy_score(y_val, dt_viz.predict(X_val)):.4f}\")\n", "print(f\"Tree depth: {dt_viz.get_depth()}\")\n", "print(f\"Number of leaves: {dt_viz.get_n_leaves()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract decision rules\n", "print(\"\\n📋 DECISION RULES EXTRACTION\")\n", "print(\"=\" * 50)\n", "\n", "# Extract text representation of the tree\n", "tree_rules = export_text(dt_viz, feature_names=feature_cols)\n", "print(\"Decision Tree Rules (First 20 lines):\")\n", "print('\\n'.join(tree_rules.split('\\n')[:20]))\n", "print(\"...\")\n", "\n", "# Extract specific rules for top classes\n", "def extract_rules_for_class(tree, feature_names, class_names, target_class):\n", "    \"\"\"Extract decision rules for a specific class\"\"\"\n", "    tree_ = tree.tree_\n", "    feature_name = [\n", "        feature_names[i] if i != -2 else \"undefined!\"\n", "        for i in tree_.feature\n", "    ]\n", "    \n", "    def recurse(node, depth, parent_rule=\"\"):\n", "        indent = \"  \" * depth\n", "        if tree_.feature[node] != -2:\n", "            name = feature_name[node]\n", "            threshold = tree_.threshold[node]\n", "            left_rule = f\"{parent_rule} AND {name} <= {threshold:.2f}\"\n", "            right_rule = f\"{parent_rule} AND {name} > {threshold:.2f}\"\n", "            recurse(tree_.children_left[node], depth + 1, left_rule)\n", "            recurse(tree_.children_right[node], depth + 1, right_rule)\n", "        else:\n", "            # Leaf node\n", "            class_counts = tree_.value[node][0]\n", "            predicted_class = np.argmax(class_counts)\n", "            if predicted_class == target_class:\n", "                confidence = class_counts[predicted_class] / np.sum(class_counts)\n", "                rule = parent_rule.strip(\" AND \")\n", "                print(f\"Rule for {class_names[target_class]}: {rule}\")\n", "                print(f\"  Confidence: {confidence:.3f}\")\n", "                print(f\"  Samples: {int(np.sum(class_counts))}\")\n", "                print()\n", "    \n", "    recurse(0, 0)\n", "\n", "# Extract rules for top 3 most common classes\n", "class_counts = np.bincount(y_train)\n", "top_classes = np.argsort(class_counts)[-3:][::-1]\n", "\n", "print(\"\\nDecision Rules for Top 3 Classes:\")\n", "for class_idx in top_classes:\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"CLASS: {target_names[class_idx]}\")\n", "    print(f\"{'='*60}\")\n", "    extract_rules_for_class(dt_viz, feature_cols, target_names, class_idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pruning analysis\n", "print(\"\\n✂️ TREE PRUNING ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Cost complexity pruning\n", "dt_full = DecisionTreeClassifier(random_state=42)\n", "path = dt_full.cost_complexity_pruning_path(X_train, y_train)\n", "ccp_alphas, impurities = path.ccp_alphas, path.impurities\n", "\n", "print(f\"Number of alpha values for pruning: {len(ccp_alphas)}\")\n", "\n", "# Train trees with different alpha values\n", "clfs = []\n", "train_scores = []\n", "val_scores = []\n", "\n", "for ccp_alpha in ccp_alphas:\n", "    clf = DecisionTreeClassifier(random_state=42, ccp_alpha=ccp_alpha)\n", "    clf.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    clfs.append(clf)\n", "    \n", "    train_score = clf.score(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    val_score = clf.score(X_val_scaled, y_val)  # FIX: Use scaled data\n", "    train_scores.append(train_score)\n", "    val_scores.append(val_score)\n", "\n", "# Find optimal alpha\n", "best_alpha_idx = np.argmax(val_scores)\n", "best_alpha = ccp_alphas[best_alpha_idx]\n", "best_val_score = val_scores[best_alpha_idx]\n", "\n", "print(f\"Best alpha: {best_alpha:.6f}\")\n", "print(f\"Best validation score: {best_val_score:.4f}\")\n", "\n", "# Create pruned tree\n", "dt_pruned = DecisionTreeClassifier(random_state=42, ccp_alpha=best_alpha)\n", "dt_pruned.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "\n", "print(f\"\\nPruned tree characteristics:\")\n", "print(f\"  Depth: {dt_pruned.get_depth()}\")\n", "print(f\"  Leaves: {dt_pruned.get_n_leaves()}\")\n", "print(f\"  Nodes: {dt_pruned.tree_.node_count}\")\n", "\n", "# Visualize pruning effect\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Alpha vs accuracy\n", "axes[0].plot(ccp_alphas, train_scores, marker='o', label='Training', alpha=0.8)\n", "axes[0].plot(ccp_alphas, val_scores, marker='s', label='Validation', alpha=0.8)\n", "axes[0].axvline(x=best_alpha, color='red', linestyle='--', label=f'Best Alpha: {best_alpha:.6f}')\n", "axes[0].set_xlabel('Alpha (Complexity Parameter)')\n", "axes[0].set_ylabel('Accuracy')\n", "axes[0].set_title('Cost Complexity Pruning')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].set_xscale('log')\n", "\n", "# Tree size vs alpha\n", "tree_sizes = [clf.tree_.node_count for clf in clfs]\n", "axes[1].plot(ccp_alphas, tree_sizes, marker='o', color='green')\n", "axes[1].axvline(x=best_alpha, color='red', linestyle='--', label=f'Best Alpha: {best_alpha:.6f}')\n", "axes[1].set_xlabel('Alpha (Complexity Parameter)')\n", "axes[1].set_ylabel('Number of Nodes')\n", "axes[1].set_title('Tree Size vs Alpha')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].set_xscale('log')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final evaluation on test set\n", "print(\"\\n🎯 FINAL EVALUATION ON TEST SET\")\n", "print(\"=\" * 50)\n", "\n", "# Test all decision tree variants\n", "dt_models = {\n", "    'Basic Tree': dt_basic,\n", "    'Best Tuned Tree': dt_best,\n", "    'Pruned Tree': dt_pruned,\n", "    'Visualization Tree': dt_viz\n", "}\n", "\n", "test_results = []\n", "for name, model in dt_models.items():\n", "    y_test_pred = model.predict(X_test)\n", "    test_accuracy = accuracy_score(y_test, y_test_pred)\n", "    test_precision = precision_score(y_test, y_test_pred, average='weighted')\n", "    test_recall = recall_score(y_test, y_test_pred, average='weighted')\n", "    test_f1 = f1_score(y_test, y_test_pred, average='weighted')\n", "    \n", "    test_results.append({\n", "        'Model': name,\n", "        'Accuracy': test_accuracy,\n", "        'Precision': test_precision,\n", "        'Recall': test_recall,\n", "        'F1_Score': test_f1,\n", "        'Tree_Depth': model.get_depth(),\n", "        'Num_Leaves': model.get_n_leaves(),\n", "        'Num_Nodes': model.tree_.node_count\n", "    })\n", "    \n", "    print(f\"\\n{name}:\")\n", "    print(f\"  Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"  Precision: {test_precision:.4f}\")\n", "    print(f\"  Recall: {test_recall:.4f}\")\n", "    print(f\"  F1-Score: {test_f1:.4f}\")\n", "    print(f\"  Tree Depth: {model.get_depth()}\")\n", "    print(f\"  Leaves: {model.get_n_leaves()}\")\n", "\n", "# Create results DataFrame\n", "results_df = pd.DataFrame(test_results)\n", "print(\"\\n📋 Test Results Summary:\")\n", "print(results_df)\n", "\n", "# Find best decision tree model\n", "best_dt_idx = results_df['Accuracy'].idxmax()\n", "best_dt_name = results_df.loc[best_dt_idx, 'Model']\n", "best_dt_accuracy = results_df.loc[best_dt_idx, 'Accuracy']\n", "\n", "print(f\"\\n🏆 Best Decision Tree: {best_dt_name} (Accuracy: {best_dt_accuracy:.4f})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion matrix for best model\n", "best_model = dt_models[best_dt_name]\n", "y_test_pred_best = best_model.predict(X_test)\n", "\n", "# Create confusion matrix\n", "cm = confusion_matrix(y_test, y_test_pred_best)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=target_names, yticklabels=target_names)\n", "plt.title(f'Confusion Matrix - {best_dt_name}')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Detailed classification report\n", "print(\"\\n📋 Detailed Classification Report:\")\n", "print(classification_report(y_test, y_test_pred_best, target_names=list(target_names)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save decision tree models and results\n", "print(\"\\n💾 SAVING DECISION TREE MODELS AND RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Save all decision tree models\n", "joblib.dump(dt_basic, '../models/saved_models/decision_tree_basic.pkl')\n", "joblib.dump(dt_best, '../models/saved_models/decision_tree_best.pkl')\n", "joblib.dump(dt_pruned, '../models/saved_models/decision_tree_pruned.pkl')\n", "joblib.dump(dt_viz, '../models/saved_models/decision_tree_viz.pkl')\n", "\n", "# Save results\n", "results_df.to_csv('../models/saved_models/decision_tree_results.csv', index=False)\n", "criterion_df.to_csv('../models/saved_models/decision_tree_criteria_comparison.csv', index=False)\n", "\n", "# Save feature importance\n", "all_feature_importance = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Basic_Tree': dt_basic.feature_importances_,\n", "    'Best_Tree': dt_best.feature_importances_,\n", "    'Pruned_Tree': dt_pruned.feature_importances_,\n", "    'Viz_Tree': dt_viz.feature_importances_\n", "})\n", "all_feature_importance.to_csv('../models/saved_models/decision_tree_feature_importance.csv', index=False)\n", "\n", "# Save tree rules\n", "with open('../models/saved_models/decision_tree_rules.txt', 'w') as f:\n", "    f.write(f\"Decision Tree Rules - {best_dt_name}\\n\")\n", "    f.write(\"=\" * 50 + \"\\n\")\n", "    f.write(export_text(best_model, feature_names=feature_cols))\n", "\n", "# Save decision tree summary\n", "dt_summary = {\n", "    'Best_Model': best_dt_name,\n", "    'Best_Accuracy': best_dt_accuracy,\n", "    'Basic_Accuracy': accuracy_basic,\n", "    'Best_Tuned_Accuracy': accuracy_best,\n", "    'Pruned_Accuracy': accuracy_score(y_test, dt_pruned.predict(X_test)),\n", "    'Best_Tree_Depth': best_model.get_depth(),\n", "    'Best_Tree_Leaves': best_model.get_n_leaves(),\n", "    'Best_Tree_Nodes': best_model.tree_.node_count\n", "}\n", "\n", "summary_df = pd.DataFrame([dt_summary])\n", "summary_df.to_csv('../models/saved_models/decision_tree_summary.csv', index=False)\n", "\n", "print(\"✅ Decision tree models saved to: data/processed/\")\n", "print(\"✅ Results saved to: models/saved_models/decision_tree_results.csv\")\n", "print(\"✅ Feature importance saved to: models/saved_models/decision_tree_feature_importance.csv\")\n", "print(\"✅ Tree rules saved to: models/saved_models/decision_tree_rules.txt\")\n", "print(\"✅ Summary saved to: models/saved_models/decision_tree_summary.csv\")\n", "\n", "print(\"\\n🎯 KEY INSIGHTS:\")\n", "print(f\"• Best decision tree: {best_dt_name} with {best_dt_accuracy:.1%} accuracy\")\n", "print(f\"• Tree depth: {best_model.get_depth()} levels\")\n", "print(f\"• Number of decision nodes: {best_model.tree_.node_count}\")\n", "print(f\"• Most important features: {', '.join(feature_importance_best.tail(3)['Feature'].tolist())}\")\n", "print(f\"• Decision trees provide interpretable rules for crop recommendation\")\n", "\n", "print(\"\\n🚀 Next: Open notebook 09_DBSCAN_Clustering.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}