{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌳 Notebook 07: Ensemble Methods\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement Random Forest (Bagging)\n", "2. <PERSON><PERSON><PERSON> Gradient <PERSON>\n", "3. Implement AdaBoost\n", "4. Implement XGBoost\n", "5. Compare ensemble methods performance\n", "6. Feature importance analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n", "✅ XGBoost is available\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import GridSearchCV, cross_val_score, learning_curve\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.metrics import precision_score, recall_score, f1_score\n", "import joblib\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")\n", "\n", "# Check if XGBoost is available\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "    print(\"✅ XGBoost is available\")\n", "except ImportError:\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"⚠️  XGBoost not installed. Install with: pip install xgboost\")\n", "    print(\"   XGBoost sections will be skipped.\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Data loaded: 1540 train, 330 val, 330 test\n", "Number of classes: 22\n", "Feature columns: ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n"]}], "source": ["# Load preprocessed data\n", "train_data = pd.read_csv('../data/processed/train.csv')\n", "val_data = pd.read_csv('../data/processed/validation.csv')\n", "test_data = pd.read_csv('../data/processed/test.csv')\n", "scaler = joblib.load('../data/processed/scaler.pkl')\n", "label_encoder = joblib.load('../data/processed/label_encoder.pkl')\n", "\n", "print(f\"✅ Data loaded: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test\")\n", "\n", "# Prepare features and targets\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X_train = train_data[feature_cols]\n", "X_val = val_data[feature_cols]\n", "X_test = test_data[feature_cols]\n", "y_train = train_data['label']\n", "y_val = val_data['label']\n", "y_test = test_data['label']\n", "\n", "# Scale features (important for some ensemble methods)\n", "X_train_scaled = scaler.transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "target_names = label_encoder.classes_\n", "print(f\"Number of classes: {len(target_names)}\")\n", "print(f\"Feature columns: {feature_cols}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌲 RANDOM FOREST CLASSIFIER\n", "==================================================\n", "Training time: 0.26 seconds\n", "Accuracy: 0.9970\n", "Precision: 0.9972\n", "Recall: 0.9970\n", "F1-Score: 0.9970\n", "Number of trees: 100\n", "\n", "Top 5 Important Features (Random Forest):\n", "    Feature  Importance\n", "6  rainfall    0.234365\n", "4  humidity    0.208780\n", "2         K    0.168565\n", "1         P    0.148198\n", "0         N    0.113355\n"]}], "source": ["# Random Forest Implementation\n", "print(\"🌲 RANDOM FOREST CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "rf_model = RandomForestClassifier(\n", "    n_estimators=100,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "rf_model.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_rf = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_rf = rf_model.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_rf = rf_model.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_rf = accuracy_score(y_val, y_val_pred_rf)\n", "precision_rf = precision_score(y_val, y_val_pred_rf, average='weighted')\n", "recall_rf = recall_score(y_val, y_val_pred_rf, average='weighted')\n", "f1_rf = f1_score(y_val, y_val_pred_rf, average='weighted')\n", "\n", "print(f\"Training time: {training_time_rf:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_rf:.4f}\")\n", "print(f\"Precision: {precision_rf:.4f}\")\n", "print(f\"Recall: {recall_rf:.4f}\")\n", "print(f\"F1-Score: {f1_rf:.4f}\")\n", "print(f\"Number of trees: {rf_model.n_estimators}\")\n", "\n", "# Feature importance\n", "feature_importance_rf = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': rf_model.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\nTop 5 Important Features (Random Forest):\")\n", "print(feature_importance_rf.head())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📈 GRADIENT BOOSTING CLASSIFIER\n", "==================================================\n", "Training time: 19.29 seconds\n", "Accuracy: 0.9818\n", "Precision: 0.9836\n", "Recall: 0.9818\n", "F1-Score: 0.9818\n", "\n", "Top 5 Important Features (Gradient Boosting):\n", "    Feature  Importance\n", "6  rainfall    0.247724\n", "4  humidity    0.240985\n", "2         K    0.184765\n", "0         N    0.116478\n", "1         P    0.115522\n"]}], "source": ["# Gradient Boosting Implementation\n", "print(\"\\n📈 GRADIENT BOOSTING CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "gb_model = GradientBoostingClassifier(\n", "    n_estimators=100,\n", "    learning_rate=0.1,\n", "    max_depth=3,\n", "    random_state=42\n", ")\n", "gb_model.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_gb = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_gb = gb_model.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_gb = gb_model.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_gb = accuracy_score(y_val, y_val_pred_gb)\n", "precision_gb = precision_score(y_val, y_val_pred_gb, average='weighted')\n", "recall_gb = recall_score(y_val, y_val_pred_gb, average='weighted')\n", "f1_gb = f1_score(y_val, y_val_pred_gb, average='weighted')\n", "\n", "print(f\"Training time: {training_time_gb:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_gb:.4f}\")\n", "print(f\"Precision: {precision_gb:.4f}\")\n", "print(f\"Recall: {recall_gb:.4f}\")\n", "print(f\"F1-Score: {f1_gb:.4f}\")\n", "\n", "# Feature importance\n", "feature_importance_gb = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': gb_model.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\nTop 5 Important Features (Gradient Boosting):\")\n", "print(feature_importance_gb.head())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 ADABOOST CLASSIFIER\n", "==================================================\n", "Training time: 0.59 seconds\n", "Accuracy: 0.1818\n", "Precision: 0.0936\n", "Recall: 0.1818\n", "F1-Score: 0.1111\n", "\n", "Top 5 Important Features (AdaBoost):\n", "    Feature  Importance\n", "4  humidity        0.50\n", "6  rainfall        0.49\n", "1         P        0.01\n", "0         N        0.00\n", "2         K        0.00\n"]}], "source": ["# AdaBoost Implementation\n", "print(\"\\n🚀 ADABOOST CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "start_time = time.time()\n", "ada_model = AdaBoostClassifier(\n", "    base_estimator=DecisionTreeClassifier(max_depth=1),\n", "    n_estimators=100,\n", "    learning_rate=1.0,\n", "    random_state=42\n", ")\n", "ada_model.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "training_time_ada = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_ada = ada_model.predict(X_val_scaled)  # FIX: Use scaled data\n", "y_val_proba_ada = ada_model.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "# Metrics\n", "accuracy_ada = accuracy_score(y_val, y_val_pred_ada)\n", "precision_ada = precision_score(y_val, y_val_pred_ada, average='weighted')\n", "recall_ada = recall_score(y_val, y_val_pred_ada, average='weighted')\n", "f1_ada = f1_score(y_val, y_val_pred_ada, average='weighted')\n", "\n", "print(f\"Training time: {training_time_ada:.2f} seconds\")\n", "print(f\"Accuracy: {accuracy_ada:.4f}\")\n", "print(f\"Precision: {precision_ada:.4f}\")\n", "print(f\"Recall: {recall_ada:.4f}\")\n", "print(f\"F1-Score: {f1_ada:.4f}\")\n", "\n", "# Feature importance\n", "feature_importance_ada = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': ada_model.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\nTop 5 Important Features (AdaBoost):\")\n", "print(feature_importance_ada.head())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚡ XGBOOST CLASSIFIER\n", "==================================================\n", "Training time: 0.46 seconds\n", "Accuracy: 0.9909\n", "Precision: 0.9915\n", "Recall: 0.9909\n", "F1-Score: 0.9909\n", "\n", "Top 5 Important Features (XGBoost):\n", "    Feature  Importance\n", "6  rainfall    0.179621\n", "1         P    0.171137\n", "4  humidity    0.168555\n", "2         K    0.163529\n", "0         N    0.156234\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # XGBoost Implementation\n", "    print(\"\\n⚡ XGBOOST CLASSIFIER\")\n", "    print(\"=\" * 50)\n", "\n", "    start_time = time.time()\n", "    xgb_model = xgb.XGBClassifier(\n", "        n_estimators=100,\n", "        learning_rate=0.1,\n", "        max_depth=3,\n", "        random_state=42,\n", "        eval_metric='mlogloss'\n", "    )\n", "    xgb_model.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    training_time_xgb = time.time() - start_time\n", "\n", "    # Predictions\n", "    y_val_pred_xgb = xgb_model.predict(X_val_scaled)  # FIX: Use scaled data\n", "    y_val_proba_xgb = xgb_model.predict_proba(X_val_scaled)  # FIX: Use scaled data\n", "\n", "    # Metrics\n", "    accuracy_xgb = accuracy_score(y_val, y_val_pred_xgb)\n", "    precision_xgb = precision_score(y_val, y_val_pred_xgb, average='weighted')\n", "    recall_xgb = recall_score(y_val, y_val_pred_xgb, average='weighted')\n", "    f1_xgb = f1_score(y_val, y_val_pred_xgb, average='weighted')\n", "\n", "    print(f\"Training time: {training_time_xgb:.2f} seconds\")\n", "    print(f\"Accuracy: {accuracy_xgb:.4f}\")\n", "    print(f\"Precision: {precision_xgb:.4f}\")\n", "    print(f\"Recall: {recall_xgb:.4f}\")\n", "    print(f\"F1-Score: {f1_xgb:.4f}\")\n", "\n", "    # Feature importance\n", "    feature_importance_xgb = pd.DataFrame({\n", "        'Feature': feature_cols,\n", "        'Importance': xgb_model.feature_importances_\n", "    }).sort_values('Importance', ascending=False)\n", "\n", "    print(\"\\nTop 5 Important Features (XGBoost):\")\n", "    print(feature_importance_xgb.head())\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚙️ HYPERPARAMETER TUNING\n", "==================================================\n", "Best performing model: <PERSON> Forest (Accuracy: 0.9970)\n", "\n", "Tuning Random Forest hyperparameters...\n", "Grid search completed in 31.05 seconds\n", "Best parameters: {'max_depth': None, 'min_samples_leaf': 1, 'min_samples_split': 5, 'n_estimators': 50}\n", "Best cross-validation score: 0.9935\n", "Validation accuracy with best RF parameters: 1.0000\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Hyperparameter Tuning for Best Model\n", "    print(\"\\n⚙️ HYPERPARAMETER TUNING\")\n", "    print(\"=\" * 50)\n", "\n", "    # Find best performing model first\n", "    models_performance = {\n", "        'Random Forest': accuracy_rf,\n", "        'Gradient Boosting': accuracy_gb,\n", "        'AdaBoost': accuracy_ada,\n", "        'XGBoost': accuracy_xgb\n", "    }\n", "\n", "    best_model_name = max(models_performance, key=models_performance.get)\n", "    print(f\"Best performing model: {best_model_name} (Accuracy: {models_performance[best_model_name]:.4f})\")\n", "\n", "    # Hyperparameter tuning for <PERSON> Forest (typically best performer)\n", "    print(\"\\nTuning Random Forest hyperparameters...\")\n", "    rf_param_grid = {\n", "        'n_estimators': [50, 100, 200],\n", "        'max_depth': [None, 10, 20],\n", "        'min_samples_split': [2, 5, 10],\n", "        'min_samples_leaf': [1, 2, 4]\n", "    }\n", "\n", "    start_time = time.time()\n", "    rf_grid_search = GridSearchCV(\n", "        RandomForestClassifier(random_state=42, n_jobs=-1),\n", "        rf_param_grid,\n", "        cv=3,\n", "        scoring='accuracy',\n", "        n_jobs=-1\n", "    )\n", "    rf_grid_search.fit(X_train_scaled, y_train)  # FIX: Use scaled data\n", "    tuning_time = time.time() - start_time\n", "\n", "    print(f\"Grid search completed in {tuning_time:.2f} seconds\")\n", "    print(f\"Best parameters: {rf_grid_search.best_params_}\")\n", "    print(f\"Best cross-validation score: {rf_grid_search.best_score_:.4f}\")\n", "\n", "    # Best Random Forest model\n", "    best_rf = rf_grid_search.best_estimator_\n", "    y_val_pred_best_rf = best_rf.predict(X_val_scaled)  # FIX: Use scaled data\n", "    accuracy_best_rf = accuracy_score(y_val, y_val_pred_best_rf)\n", "    print(f\"Validation accuracy with best RF parameters: {accuracy_best_rf:.4f}\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 2000x1200 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Visualize Ensemble Methods Comparison\n", "    fig, axes = plt.subplots(2, 3, figsize=(20, 12))\n", "\n", "    # Performance comparison\n", "    models = ['Random Forest', 'Gradient Boosting', 'AdaBoost', 'XGBoost', 'Best RF']\n", "    accuracies = [accuracy_rf, accuracy_gb, accuracy_ada, accuracy_xgb, accuracy_best_rf]\n", "    training_times = [training_time_rf, training_time_gb, training_time_ada, training_time_xgb, tuning_time]\n", "    colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold', 'purple']\n", "\n", "    # Accuracy comparison\n", "    bars1 = axes[0, 0].bar(models, accuracies, color=colors)\n", "    axes[0, 0].set_ylabel('Accuracy')\n", "    axes[0, 0].set_title('Ensemble Methods - Accuracy Comparison')\n", "    axes[0, 0].tick_params(axis='x', rotation=45)\n", "    axes[0, 0].set_ylim(0, 1)\n", "\n", "    # Add value labels on bars\n", "    for bar, acc in zip(bars1, accuracies):\n", "        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                       f'{acc:.3f}', ha='center', va='bottom')\n", "\n", "    # Training time comparison\n", "    axes[0, 1].bar(models, training_times, color=colors)\n", "    axes[0, 1].set_ylabel('Training Time (seconds)')\n", "    axes[0, 1].set_title('Ensemble Methods - Training Time')\n", "    axes[0, 1].tick_params(axis='x', rotation=45)\n", "\n", "    # Feature importance comparison (top 3 features)\n", "    importance_data = {\n", "        'Random Forest': feature_importance_rf.set_index('Feature')['Importance'],\n", "        'Gradient Boosting': feature_importance_gb.set_index('Feature')['Importance'],\n", "        'AdaBoost': feature_importance_ada.set_index('Feature')['Importance'],\n", "        'XGBoost': feature_importance_xgb.set_index('Feature')['Importance']\n", "    }\n", "\n", "    importance_df = pd.DataFrame(importance_data)\n", "    sns.heatmap(importance_df.T, annot=True, cmap='YlOrRd', ax=axes[0, 2])\n", "    axes[0, 2].set_title('Feature Importance Heatmap')\n", "    axes[0, 2].set_xlabel('Features')\n", "    axes[0, 2].set_ylabel('Models')\n", "\n", "    # Confusion matrices for best models\n", "    best_models = [('Random Forest', y_val_pred_rf), ('XGBoost', y_val_pred_xgb), ('Best RF', y_val_pred_best_rf)]\n", "\n", "    for i, (name, predictions) in enumerate(best_models):\n", "        cm = confusion_matrix(y_val, predictions)\n", "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, i],\n", "                    xticklabels=target_names, yticklabels=target_names)\n", "        axes[1, i].set_title(f'Confusion Matrix - {name}')\n", "        axes[1, i].set_xlabel('Predicted')\n", "        axes[1, i].set_ylabel('Actual')\n", "        plt.setp(axes[1, i].get_xticklabels(), rotation=45, ha='right')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📈 LEARNING CURVES ANALYSIS\n", "==================================================\n", "Computing learning curve for Random Forest...\n", "Computing learning curve for XGBoost...\n"]}, {"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Learning curves for ensemble methods\n", "    print(\"\\n📈 LEARNING CURVES ANALYSIS\")\n", "    print(\"=\" * 50)\n", "\n", "    # Define models for learning curves\n", "    models_for_curves = {\n", "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),\n", "        'XGBoost': xgb.XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')\n", "    }\n", "\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    for i, (name, model) in enumerate(models_for_curves.items()):\n", "        print(f\"Computing learning curve for {name}...\")\n", "    \n", "        train_sizes, train_scores, val_scores = learning_curve(\n", "            model, X_train, y_train, cv=3, n_jobs=-1,\n", "            train_sizes=np.linspace(0.1, 1.0, 10),\n", "            scoring='accuracy'\n", "        )\n", "    \n", "        train_mean = np.mean(train_scores, axis=1)\n", "        train_std = np.std(train_scores, axis=1)\n", "        val_mean = np.mean(val_scores, axis=1)\n", "        val_std = np.std(val_scores, axis=1)\n", "    \n", "        axes[i].plot(train_sizes, train_mean, 'o-', color='blue', label='Training Score')\n", "        axes[i].fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')\n", "    \n", "        axes[i].plot(train_sizes, val_mean, 'o-', color='red', label='Validation Score')\n", "        axes[i].fill_between(train_sizes, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')\n", "    \n", "        axes[i].set_xlabel('Training Set Size')\n", "        axes[i].set_ylabel('Accuracy Score')\n", "        axes[i].set_title(f'Learning Curve - {name}')\n", "        axes[i].legend()\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 FINAL EVALUATION ON TEST SET\n", "==================================================\n", "\n", "Random Forest:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "Gradient Boosting:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "AdaBoost:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "XGBoost:\n", "  Accuracy: 0.0455\n", "  Precision: 0.0021\n", "  Recall: 0.0455\n", "  F1-Score: 0.0040\n", "\n", "Best Random Forest:\n", "  Accuracy: 0.9939\n", "  Precision: 0.9943\n", "  Recall: 0.9939\n", "  F1-Score: 0.9939\n", "\n", "📋 Test Results Summary:\n", "                Model  Accuracy  Precision    Recall  F1_Score\n", "0       Random Forest  0.045455   0.002066  0.045455  0.003953\n", "1   Gradient Boosting  0.045455   0.002066  0.045455  0.003953\n", "2            AdaBoost  0.045455   0.002066  0.045455  0.003953\n", "3             XGBoost  0.045455   0.002066  0.045455  0.003953\n", "4  Best Random Forest  0.993939   0.994318  0.993939  0.993933\n", "\n", "🏆 Best Ensemble Model: Best Random Forest (Accuracy: 0.9939)\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Final evaluation on test set\n", "    print(\"\\n🎯 FINAL EVALUATION ON TEST SET\")\n", "    print(\"=\" * 50)\n", "\n", "    # Test all ensemble models\n", "    ensemble_models = {\n", "        'Random Forest': rf_model,\n", "        'Gradient Boosting': gb_model,\n", "        'AdaBoost': ada_model,\n", "        'XGBoost': xgb_model,\n", "        'Best Random Forest': best_rf\n", "    }\n", "\n", "    test_results = []\n", "    for name, model in ensemble_models.items():\n", "        y_test_pred = model.predict(X_test_scaled)  # FIX: Use scaled data\n", "        test_accuracy = accuracy_score(y_test, y_test_pred)\n", "        test_precision = precision_score(y_test, y_test_pred, average='weighted')\n", "        test_recall = recall_score(y_test, y_test_pred, average='weighted')\n", "        test_f1 = f1_score(y_test, y_test_pred, average='weighted')\n", "    \n", "        test_results.append({\n", "            'Model': name,\n", "            'Accuracy': test_accuracy,\n", "            'Precision': test_precision,\n", "            'Recall': test_recall,\n", "            'F1_Score': test_f1\n", "        })\n", "    \n", "        print(f\"\\n{name}:\")\n", "        print(f\"  Accuracy: {test_accuracy:.4f}\")\n", "        print(f\"  Precision: {test_precision:.4f}\")\n", "        print(f\"  Recall: {test_recall:.4f}\")\n", "        print(f\"  F1-Score: {test_f1:.4f}\")\n", "\n", "    # Create results DataFrame\n", "    results_df = pd.DataFrame(test_results)\n", "    print(\"\\n📋 Test Results Summary:\")\n", "    print(results_df)\n", "\n", "    # Find best ensemble model\n", "    best_ensemble_idx = results_df['Accuracy'].idxmax()\n", "    best_ensemble_name = results_df.loc[best_ensemble_idx, 'Model']\n", "    best_ensemble_accuracy = results_df.loc[best_ensemble_idx, 'Accuracy']\n", "\n", "    print(f\"\\n🏆 Best Ensemble Model: {best_ensemble_name} (Accuracy: {best_ensemble_accuracy:.4f})\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 SAVING ENSEMBLE MODELS AND RESULTS\n", "==================================================\n", "✅ Ensemble models saved to: data/processed/\n", "✅ Results saved to: data/processed/ensemble_results.csv\n", "✅ Feature importance saved to: data/processed/ensemble_feature_importance.csv\n", "✅ Summary saved to: data/processed/ensemble_summary.csv\n", "\n", "🎯 KEY INSIGHTS:\n", "• Best ensemble model: Best Random Forest with 99.4% accuracy\n", "• Random Forest achieved 99.7% accuracy\n", "• XGBoost achieved 99.1% accuracy\n", "• Ensemble methods show strong performance for crop recommendation\n", "• Most important features: rainfall, humidity, K\n", "\n", "🚀 Next: Open notebook 08_CART_Decision_Trees.ipynb\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Save ensemble models and results\n", "    print(\"\\n💾 SAVING ENSEMBLE MODELS AND RESULTS\")\n", "    print(\"=\" * 50)\n", "\n", "    # Save all ensemble models\n", "    joblib.dump(rf_model, '../models/saved_models/random_forest_model.pkl')\n", "    joblib.dump(gb_model, '../models/saved_models/gradient_boosting_model.pkl')\n", "    joblib.dump(ada_model, '../models/saved_models/adaboost_model.pkl')\n", "    joblib.dump(xgb_model, '../models/saved_models/xgboost_model.pkl')\n", "    joblib.dump(best_rf, '../models/saved_models/best_random_forest_model.pkl')\n", "\n", "    # Save results\n", "    results_df.to_csv('../data/processed/ensemble_results.csv', index=False)\n", "\n", "    # Save feature importance for all models\n", "    all_feature_importance = pd.DataFrame({\n", "        'Feature': feature_cols,\n", "        'Random_Forest': rf_model.feature_importances_,\n", "        'Gradient_Boosting': gb_model.feature_importances_,\n", "        'AdaBoost': ada_model.feature_importances_,\n", "        'XGBoost': xgb_model.feature_importances_,\n", "        'Best_Random_Forest': best_rf.feature_importances_\n", "    })\n", "    all_feature_importance.to_csv('../data/processed/ensemble_feature_importance.csv', index=False)\n", "\n", "    # Save ensemble summary\n", "    ensemble_summary = {\n", "        'Best_Model': best_ensemble_name,\n", "        'Best_Accuracy': best_ensemble_accuracy,\n", "        'RF_Accuracy': accuracy_rf,\n", "        'GB_Accuracy': accuracy_gb,\n", "        'Ada_Accuracy': accuracy_ada,\n", "        'XGB_Accuracy': accuracy_xgb,\n", "        'Best_RF_Accuracy': accuracy_best_rf\n", "    }\n", "\n", "    summary_df = pd.DataFrame([ensemble_summary])\n", "    summary_df.to_csv('../data/processed/ensemble_summary.csv', index=False)\n", "\n", "    print(\"✅ Ensemble models saved to: data/processed/\")\n", "    print(\"✅ Results saved to: data/processed/ensemble_results.csv\")\n", "    print(\"✅ Feature importance saved to: data/processed/ensemble_feature_importance.csv\")\n", "    print(\"✅ Summary saved to: data/processed/ensemble_summary.csv\")\n", "\n", "    print(\"\\n🎯 KEY INSIGHTS:\")\n", "    print(f\"• Best ensemble model: {best_ensemble_name} with {best_ensemble_accuracy:.1%} accuracy\")\n", "    print(f\"• Random Forest achieved {accuracy_rf:.1%} accuracy\")\n", "    print(f\"• XGBoost achieved {accuracy_xgb:.1%} accuracy\")\n", "    print(f\"• Ensemble methods show strong performance for crop recommendation\")\n", "    print(f\"• Most important features: {', '.join(feature_importance_rf.head(3)['Feature'].tolist())}\")\n", "\n", "    print(\"\\n🚀 Next: Open notebook 08_CART_Decision_Trees.ipynb\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}