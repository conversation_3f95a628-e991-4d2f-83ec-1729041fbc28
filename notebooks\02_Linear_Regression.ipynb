{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📈 Notebook 02: Linear Regression Analysis\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement Simple Linear Regression\n", "2. Implement Multivariate Linear Regression\n", "3. Feature importance analysis\n", "4. Model evaluation and visualization\n", "5. Regression for continuous target prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import joblib\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load preprocessed data\n", "train_data = pd.read_csv('../data/processed/train.csv')\n", "val_data = pd.read_csv('../data/processed/validation.csv')\n", "test_data = pd.read_csv('../data/processed/test.csv')\n", "\n", "# Load preprocessing objects\n", "scaler = joblib.load('../data/processed/scaler.pkl')\n", "label_encoder = joblib.load('../data/processed/label_encoder.pkl')\n", "\n", "print(f\"✅ Data loaded: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test\")\n", "\n", "# Prepare features\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X_train = train_data[feature_cols]\n", "X_val = val_data[feature_cols]\n", "X_test = test_data[feature_cols]\n", "\n", "# Scale features\n", "X_train_scaled = scaler.transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create regression targets (predict individual nutrients)\n", "print(\"🎯 LINEAR REGRESSION FOR NUTRIENT PREDICTION\")\n", "print(\"=\" * 50)\n", "\n", "# We'll predict N, P, K values based on other features\n", "regression_results = {}\n", "\n", "# Simple Linear Regression: Predict N based on temperature\n", "print(\"\\n1️⃣ SIMPLE LINEAR REGRESSION: N ~ Temperature\")\n", "lr_simple = LinearRegression()\n", "X_temp_train = X_train[['temperature']]\n", "X_temp_val = X_val[['temperature']]\n", "y_N_train = X_train['N']\n", "y_N_val = X_val['N']\n", "\n", "lr_simple.fit(X_temp_train, y_N_train)\n", "y_N_pred = lr_simple.predict(X_temp_val)\n", "\n", "# Metrics\n", "mse = mean_squared_error(y_N_val, y_N_pred)\n", "r2 = r2_score(y_N_val, y_N_pred)\n", "mae = mean_absolute_error(y_N_val, y_N_pred)\n", "\n", "print(f\"MSE: {mse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")\n", "print(f\"MAE: {mae:.4f}\")\n", "print(f\"Coefficient: {lr_simple.coef_[0]:.4f}\")\n", "print(f\"Intercept: {lr_simple.intercept_:.4f}\")\n", "\n", "regression_results['Simple_LR'] = {'MSE': mse, 'R2': r2, 'MAE': mae}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize Simple Linear Regression\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Scatter plot with regression line\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(X_temp_val, y_N_val, alpha=0.6, label='Actual')\n", "plt.scatter(X_temp_val, y_N_pred, alpha=0.6, label='Predicted', color='red')\n", "plt.plot(X_temp_val, y_N_pred, color='red', linewidth=2)\n", "plt.xlabel('Temperature')\n", "plt.ylabel('Nitrogen (N)')\n", "plt.title('Simple Linear Regression: N ~ Temperature')\n", "plt.legend()\n", "\n", "# Residual plot\n", "plt.subplot(1, 2, 2)\n", "residuals = y_N_val - y_N_pred\n", "plt.scatter(y_N_pred, residuals, alpha=0.6)\n", "plt.axhline(y=0, color='red', linestyle='--')\n", "plt.xlabel('Predicted N')\n", "plt.ylabel('Residuals')\n", "plt.title('Residual Plot')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Multivariate Linear Regression: Predict N based on all other features\n", "print(\"\\n2️⃣ MULTIVARIATE LINEAR REGRESSION: N ~ All Features\")\n", "lr_multi = LinearRegression()\n", "X_multi_train = X_train[['P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']]\n", "X_multi_val = X_val[['P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']]\n", "\n", "lr_multi.fit(X_multi_train, y_N_train)\n", "y_N_multi_pred = lr_multi.predict(X_multi_val)\n", "\n", "# Metrics\n", "mse_multi = mean_squared_error(y_N_val, y_N_multi_pred)\n", "r2_multi = r2_score(y_N_val, y_N_multi_pred)\n", "mae_multi = mean_absolute_error(y_N_val, y_N_multi_pred)\n", "\n", "print(f\"MSE: {mse_multi:.4f}\")\n", "print(f\"R²: {r2_multi:.4f}\")\n", "print(f\"MAE: {mae_multi:.4f}\")\n", "\n", "regression_results['Multivariate_LR'] = {'MSE': mse_multi, 'R2': r2_multi, 'MAE': mae_multi}\n", "\n", "# Feature importance (coefficients)\n", "feature_importance = pd.DataFrame({\n", "    'Feature': X_multi_train.columns,\n", "    'Coefficient': lr_multi.coef_,\n", "    'Abs_Coefficient': np.abs(lr_multi.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(\"\\n📊 Feature Importance (Coefficients):\")\n", "print(feature_importance)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize Multivariate Linear Regression\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Actual vs Predicted\n", "axes[0,0].scatter(y_N_val, y_N_multi_pred, alpha=0.6)\n", "axes[0,0].plot([y_N_val.min(), y_N_val.max()], [y_N_val.min(), y_N_val.max()], 'r--', lw=2)\n", "axes[0,0].set_xlabel('Actual N')\n", "axes[0,0].set_ylabel('Predicted N')\n", "axes[0,0].set_title(f'Actual vs Predicted (R² = {r2_multi:.3f})')\n", "\n", "# Residuals\n", "residuals_multi = y_N_val - y_N_multi_pred\n", "axes[0,1].scatter(y_N_multi_pred, residuals_multi, alpha=0.6)\n", "axes[0,1].axhline(y=0, color='red', linestyle='--')\n", "axes[0,1].set_xlabel('Predicted N')\n", "axes[0,1].set_ylabel('Residuals')\n", "axes[0,1].set_title('Residual Plot')\n", "\n", "# Feature importance\n", "axes[1,0].barh(feature_importance['Feature'], feature_importance['Abs_Coefficient'])\n", "axes[1,0].set_xlabel('Absolute Coefficient Value')\n", "axes[1,0].set_title('Feature Importance')\n", "\n", "# Model comparison\n", "models = ['Simple LR', 'Multivariate LR']\n", "r2_scores = [r2, r2_multi]\n", "axes[1,1].bar(models, r2_scores, color=['skyblue', 'lightcoral'])\n", "axes[1,1].set_ylabel('R² Score')\n", "axes[1,1].set_title('Model Comparison')\n", "axes[1,1].set_ylim(0, 1)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Multiple target regression (predict P and K as well)\n", "print(\"\\n3️⃣ MULTIPLE TARGET REGRESSION\")\n", "print(\"=\" * 50)\n", "\n", "targets = ['N', 'P', 'K']\n", "multi_target_results = {}\n", "\n", "for target in targets:\n", "    print(f\"\\n🎯 Predicting {target}:\")\n", "    \n", "    # Features (all except the target)\n", "    feature_cols_target = [col for col in feature_cols if col != target]\n", "    X_target_train = X_train[feature_cols_target]\n", "    X_target_val = X_val[feature_cols_target]\n", "    y_target_train = X_train[target]\n", "    y_target_val = X_val[target]\n", "    \n", "    # Train model\n", "    lr_target = LinearRegression()\n", "    lr_target.fit(X_target_train, y_target_train)\n", "    y_target_pred = lr_target.predict(X_target_val)\n", "    \n", "    # Metrics\n", "    mse_target = mean_squared_error(y_target_val, y_target_pred)\n", "    r2_target = r2_score(y_target_val, y_target_pred)\n", "    mae_target = mean_absolute_error(y_target_val, y_target_pred)\n", "    \n", "    print(f\"MSE: {mse_target:.4f}\")\n", "    print(f\"R²: {r2_target:.4f}\")\n", "    print(f\"MAE: {mae_target:.4f}\")\n", "    \n", "    multi_target_results[target] = {\n", "        'MSE': mse_target, 'R2': r2_target, 'MAE': mae_target,\n", "        'model': lr_target\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize multiple target results\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "for i, target in enumerate(targets):\n", "    r2_score_target = multi_target_results[target]['R2']\n", "    \n", "    # Get predictions for visualization\n", "    feature_cols_target = [col for col in feature_cols if col != target]\n", "    X_target_val = X_val[feature_cols_target]\n", "    y_target_val = X_val[target]\n", "    y_target_pred = multi_target_results[target]['model'].predict(X_target_val)\n", "    \n", "    axes[i].scatter(y_target_val, y_target_pred, alpha=0.6)\n", "    axes[i].plot([y_target_val.min(), y_target_val.max()], \n", "                [y_target_val.min(), y_target_val.max()], 'r--', lw=2)\n", "    axes[i].set_xlabel(f'Actual {target}')\n", "    axes[i].set_ylabel(f'Predicted {target}')\n", "    axes[i].set_title(f'{target} Prediction (R² = {r2_score_target:.3f})')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary and save results\n", "print(\"📋 LINEAR REGRESSION SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "# Create summary DataFrame\n", "summary_data = []\n", "summary_data.append(['Simple LR (N~Temp)', r2, mse, mae])\n", "summary_data.append(['Multivariate LR (N)', r2_multi, mse_multi, mae_multi])\n", "\n", "for target in targets:\n", "    results = multi_target_results[target]\n", "    summary_data.append([f'LR ({target})', results['R2'], results['MSE'], results['MAE']])\n", "\n", "summary_df = pd.DataFrame(summary_data, \n", "                         columns=['Model', 'R²', 'MSE', 'MAE'])\n", "\n", "print(summary_df)\n", "\n", "# Save results\n", "summary_df.to_csv('../data/processed/linear_regression_results.csv', index=False)\n", "\n", "# Save best models\n", "joblib.dump(lr_multi, '../models/saved_models/linear_regression_model.pkl')\n", "\n", "print(\"\\n✅ Results saved to: data/processed/linear_regression_results.csv\")\n", "print(\"✅ Best model saved to: models/saved_models/linear_regression_model.pkl\")\n", "\n", "print(\"\\n🎯 KEY INSIGHTS:\")\n", "print(f\"• Best R² score: {summary_df['R²'].max():.3f}\")\n", "print(f\"• Multivariate regression outperforms simple regression\")\n", "print(f\"• Feature importance varies across different targets\")\n", "\n", "print(\"\\n🚀 Next: Open notebook 03_Logistic_Regression.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}