{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Notebook 10: Advanced Machine Learning Techniques\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement advanced ensemble methods (Random Forest, Grad<PERSON>, XGBoost)\n", "2. Feature selection and engineering techniques\n", "3. Hyperparameter optimization (Grid Search, Random Search, Bayesian Optimization)\n", "4. Advanced cross-validation strategies\n", "5. Model stacking and blending\n", "6. Feature importance analysis\n", "7. Model interpretability and explainability"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n", "✅ XGBoost is available\n"]}], "source": ["# Memory-optimized parameters to prevent kernel crash\n", "# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold\n", "from sklearn.model_selection import GridSearchCV, RandomizedSearchCV\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.ensemble import VotingClassifier, StackingClassifier, BaggingClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.svm import SVC\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from sklearn.feature_selection import SelectKBest, f_classif, RFE, RFECV\n", "from sklearn.feature_selection import mutual_info_classif, chi2\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.metrics import precision_score, recall_score, f1_score, roc_auc_score\n", "import joblib\n", "import warnings\n", "import time\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Libraries imported successfully!\")\n", "\n", "# Check if XGBoost is available\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "    print(\"✅ XGBoost is available\")\n", "except ImportError:\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"⚠️  XGBoost not installed. Install with: pip install xgboost\")\n", "    print(\"   XGBoost sections will be skipped.\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 LOADING AND PREPARING DATA\n", "==================================================\n", "Dataset shape: (2200, 8)\n", "\n", "📈 Data Split:\n", "Training set: 1540 samples\n", "Validation set: 330 samples\n", "Test set: 330 samples\n", "Number of classes: 22\n", "Classes: ['apple', 'banana', 'blackgram', 'chickpea', 'coconut', 'coffee', 'cotton', 'grapes', 'jute', 'kidneybeans', 'lentil', 'maize', 'mango', 'mothbeans', 'mungbean', 'muskmelon', 'orange', 'papaya', 'pigeonpeas', 'pomegranate', 'rice', 'watermelon']\n", "\n", "✅ Data prepared and standardized\n"]}], "source": ["# Load and prepare data\n", "print(\"📊 LOADING AND PREPARING DATA\")\n", "print(\"=\" * 50)\n", "\n", "# Load raw data\n", "data = pd.read_csv('../data/raw/Crop_recommendation.csv')\n", "print(f\"Dataset shape: {data.shape}\")\n", "\n", "# Prepare features and target\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X = data[feature_cols]\n", "y = data['label']\n", "\n", "# Encode target labels\n", "le = LabelEncoder()\n", "y_encoded = le.fit_transform(y)\n", "\n", "# Split data\n", "X_train, X_temp, y_train, y_temp = train_test_split(X, y_encoded, test_size=0.3, random_state=42, stratify=y_encoded)\n", "X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp)\n", "\n", "print(f\"\\n📈 Data Split:\")\n", "print(f\"Training set: {X_train.shape[0]} samples\")\n", "print(f\"Validation set: {X_val.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")\n", "print(f\"Number of classes: {len(le.classes_)}\")\n", "print(f\"Classes: {list(le.classes_)}\")\n", "\n", "# Standardize features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"\\n✅ Data prepared and standardized\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 FEATURE SELECTION ANALYSIS\n", "==================================================\n", "\n", "1️⃣ Univariate Feature Selection (ANOVA F-test)\n", "       Feature       F-Score        P-Value\n", "2            K  18967.058496   0.000000e+00\n", "4     humidity   2173.550342   0.000000e+00\n", "1            P   1296.896976   0.000000e+00\n", "0            N    632.717236   0.000000e+00\n", "6     rainfall    415.569295   0.000000e+00\n", "3  temperature     72.683739  1.274357e-211\n", "5           ph     43.619326  4.792799e-139\n", "\n", "2️⃣ Mutual Information\n", "       Feature  MI_Score\n", "4     humidity  1.726867\n", "2            K  1.631946\n", "6     rainfall  1.626265\n", "1            P  1.309564\n", "3  temperature  0.996146\n", "0            N  0.967410\n", "5           ph  0.701227\n"]}], "source": ["# Feature Selection Analysis\n", "print(\"\\n🔍 FEATURE SELECTION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Method 1: Univariate Feature Selection (ANOVA F-test)\n", "print(\"\\n1️⃣ Univariate Feature Selection (ANOVA F-test)\")\n", "selector_anova = SelectKBest(score_func=f_classif, k='all')\n", "selector_anova.fit(X_train, y_train)\n", "\n", "anova_scores = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'F-Score': selector_anova.scores_,\n", "    'P-Value': selector_anova.pvalues_\n", "}).sort_values('F-Score', ascending=False)\n", "\n", "print(anova_scores)\n", "\n", "# Method 2: Mutual Information\n", "print(\"\\n2️⃣ Mutual Information\")\n", "mi_scores = mutual_info_classif(X_train, y_train, random_state=42)\n", "mi_df = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'MI_Score': mi_scores\n", "}).sort_values('MI_Score', ascending=False)\n", "\n", "print(mi_df)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize feature importance from different methods\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# ANOVA F-scores\n", "axes[0].barh(anova_scores['Feature'], anova_scores['F-Score'])\n", "axes[0].set_xlabel('F-Score')\n", "axes[0].set_title('Feature Importance (ANOVA F-test)')\n", "axes[0].invert_yaxis()\n", "\n", "# Mutual Information scores\n", "axes[1].barh(mi_df['Feature'], mi_df['MI_Score'])\n", "axes[1].set_xlabel('MI Score')\n", "axes[1].set_title('Feature Importance (Mutual Information)')\n", "axes[1].invert_yaxis()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🌲 RANDOM FOREST CLASSIFIER\n", "==================================================\n", "\n", "1️⃣ Baseline Random Forest\n", "Training time: 0.18 seconds\n", "Training accuracy: 1.0000\n", "Validation accuracy: 0.9909\n", "Test accuracy: 0.9970\n", "\n", "📊 Feature Importance (Random Forest):\n", "       Feature  Importance\n", "6     rainfall    0.225824\n", "4     humidity    0.213526\n", "2            K    0.170647\n", "1            P    0.153043\n", "0            N    0.108487\n", "3  temperature    0.076468\n", "5           ph    0.052006\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Random Forest - Baseline\n", "print(\"\\n🌲 RANDOM FOREST CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "# Train baseline Random Forest\n", "print(\"\\n1️⃣ Baseline Random Forest\")\n", "rf_baseline = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)\n", "start_time = time.time()\n", "rf_baseline.fit(X_train, y_train)\n", "train_time = time.time() - start_time\n", "\n", "# Predictions\n", "y_train_pred = rf_baseline.predict(X_train)\n", "y_val_pred = rf_baseline.predict(X_val)\n", "y_test_pred = rf_baseline.predict(X_test)\n", "\n", "# Metrics\n", "train_acc = accuracy_score(y_train, y_train_pred)\n", "val_acc = accuracy_score(y_val, y_val_pred)\n", "test_acc = accuracy_score(y_test, y_test_pred)\n", "\n", "print(f\"Training time: {train_time:.2f} seconds\")\n", "print(f\"Training accuracy: {train_acc:.4f}\")\n", "print(f\"Validation accuracy: {val_acc:.4f}\")\n", "print(f\"Test accuracy: {test_acc:.4f}\")\n", "\n", "# Feature importance\n", "rf_feature_importance = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': rf_baseline.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\n📊 Feature Importance (Random Forest):\")\n", "print(rf_feature_importance)\n", "\n", "# Visualize feature importance\n", "plt.figure(figsize=(10, 6))\n", "plt.barh(rf_feature_importance['Feature'], rf_feature_importance['Importance'])\n", "plt.xlabel('Importance')\n", "plt.title('Random Forest Feature Importance')\n", "plt.gca().invert_yaxis()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "2️⃣ Hyperparameter Optimization (Random Forest)\n", "==================================================\n", "\n", "Performing Grid Search (this may take a while)...\n", "Fitting 5 folds for each of 216 candidates, totalling 1080 fits\n", "\n", "✅ Grid Search completed in 234.54 seconds\n", "\n", "🎯 Best Parameters:\n", "{'max_depth': 10, 'max_features': 'sqrt', 'min_samples_leaf': 1, 'min_samples_split': 10, 'n_estimators': 100}\n", "\n", "📊 Best Cross-Validation Score: 0.9961\n", "\n", "Validation accuracy (optimized): 0.9879\n", "Test accuracy (optimized): 0.9970\n", "Improvement over baseline: 0.00%\n"]}], "source": ["# Hyperparameter Optimization for Random Forest\n", "print(\"\\n2️⃣ Hyperparameter Optimization (Random Forest)\")\n", "print(\"=\" * 50)\n", "\n", "# Define parameter grid\n", "param_grid_rf = {\n", "    'n_estimators': [100, 200, 300],\n", "    'max_depth': [10, 20, 30, None],\n", "    'min_samples_split': [2, 5, 10],\n", "    'min_samples_leaf': [1, 2, 4],\n", "    'max_features': ['sqrt', 'log2']\n", "}\n", "\n", "# Grid Search with Cross-Validation\n", "print(\"\\nPerforming Grid Search (this may take a while)...\")\n", "rf_grid = GridSearchCV(\n", "    RandomForestClassifier(random_state=42, n_jobs=-1),\n", "    param_grid_rf,\n", "    cv=5,\n", "    scoring='accuracy',\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "start_time = time.time()\n", "rf_grid.fit(X_train, y_train)\n", "grid_time = time.time() - start_time\n", "\n", "print(f\"\\n✅ Grid Search completed in {grid_time:.2f} seconds\")\n", "print(f\"\\n🎯 Best Parameters:\")\n", "print(rf_grid.best_params_)\n", "print(f\"\\n📊 Best Cross-Validation Score: {rf_grid.best_score_:.4f}\")\n", "\n", "# Evaluate best model\n", "rf_best = rf_grid.best_estimator_\n", "y_val_pred_best = rf_best.predict(X_val)\n", "y_test_pred_best = rf_best.predict(X_test)\n", "\n", "val_acc_best = accuracy_score(y_val, y_val_pred_best)\n", "test_acc_best = accuracy_score(y_test, y_test_pred_best)\n", "\n", "print(f\"\\nValidation accuracy (optimized): {val_acc_best:.4f}\")\n", "print(f\"Test accuracy (optimized): {test_acc_best:.4f}\")\n", "print(f\"Improvement over baseline: {(test_acc_best - test_acc)*100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📈 GRADIENT BOOSTING CLASSIFIER\n", "==================================================\n", "\n", "1️⃣ <PERSON><PERSON>\n", "Training time: 20.15 seconds\n", "Validation accuracy: 0.9848\n", "Test accuracy: 1.0000\n", "\n", "📊 Feature Importance (Gradient Boosting):\n", "       Feature  Importance\n", "4     humidity    0.248057\n", "6     rainfall    0.236381\n", "2            K    0.173103\n", "1            P    0.135370\n", "0            N    0.112871\n", "3  temperature    0.056506\n", "5           ph    0.037712\n"]}], "source": ["# Gradient Boosting Classifier\n", "print(\"\\n📈 GRADIENT BOOSTING CLASSIFIER\")\n", "print(\"=\" * 50)\n", "\n", "# Train baseline Gradient Boosting\n", "print(\"\\n1️⃣ Baseline Gradient Boosting\")\n", "gb_baseline = GradientBoostingClassifier(n_estimators=100, random_state=42)\n", "start_time = time.time()\n", "gb_baseline.fit(X_train, y_train)\n", "train_time = time.time() - start_time\n", "\n", "# Predictions\n", "y_val_pred_gb = gb_baseline.predict(X_val)\n", "y_test_pred_gb = gb_baseline.predict(X_test)\n", "\n", "# Metrics\n", "val_acc_gb = accuracy_score(y_val, y_val_pred_gb)\n", "test_acc_gb = accuracy_score(y_test, y_test_pred_gb)\n", "\n", "print(f\"Training time: {train_time:.2f} seconds\")\n", "print(f\"Validation accuracy: {val_acc_gb:.4f}\")\n", "print(f\"Test accuracy: {test_acc_gb:.4f}\")\n", "\n", "# Feature importance\n", "gb_feature_importance = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Importance': gb_baseline.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"\\n📊 Feature Importance (Gradient Boosting):\")\n", "print(gb_feature_importance)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚡ XGBOOST CLASSIFIER\n", "==================================================\n", "\n", "1️⃣ Baseline XGBoost\n", "Training time: 0.56 seconds\n", "Validation accuracy: 0.9879\n", "Test accuracy: 0.9909\n", "\n", "📊 Feature Importance (XGBoost):\n", "       Feature  Importance\n", "1            P    0.185962\n", "2            K    0.179700\n", "4     humidity    0.176399\n", "6     rainfall    0.160108\n", "0            N    0.153617\n", "3  temperature    0.084421\n", "5           ph    0.059793\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # XGBoost Classifier\n", "    print(\"\\n⚡ XGBOOST CLASSIFIER\")\n", "    print(\"=\" * 50)\n", "\n", "    # Train baseline XGBoost\n", "    print(\"\\n1️⃣ Baseline XGBoost\")\n", "    xgb_baseline = xgb.XGBClassifier(\n", "        n_estimators=100,\n", "        random_state=42,\n", "        n_jobs=-1,\n", "        eval_metric='mlogloss'\n", "    )\n", "\n", "    start_time = time.time()\n", "    xgb_baseline.fit(X_train, y_train)\n", "    train_time = time.time() - start_time\n", "\n", "    # Predictions\n", "    y_val_pred_xgb = xgb_baseline.predict(X_val)\n", "    y_test_pred_xgb = xgb_baseline.predict(X_test)\n", "\n", "    # Metrics\n", "    val_acc_xgb = accuracy_score(y_val, y_val_pred_xgb)\n", "    test_acc_xgb = accuracy_score(y_test, y_test_pred_xgb)\n", "\n", "    print(f\"Training time: {train_time:.2f} seconds\")\n", "    print(f\"Validation accuracy: {val_acc_xgb:.4f}\")\n", "    print(f\"Test accuracy: {test_acc_xgb:.4f}\")\n", "\n", "    # Feature importance\n", "    xgb_feature_importance = pd.DataFrame({\n", "        'Feature': feature_cols,\n", "        'Importance': xgb_baseline.feature_importances_\n", "    }).sort_values('Importance', ascending=False)\n", "\n", "    print(\"\\n📊 Feature Importance (XGBoost):\")\n", "    print(xgb_feature_importance)\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "2️⃣ Hyperparameter Optimization (XGBoost)\n", "==================================================\n", "\n", "Performing Randomized Search...\n", "Fitting 5 folds for each of 20 candidates, totalling 100 fits\n", "\n", "✅ Randomized Search completed in 24.68 seconds\n", "\n", "🎯 Best Parameters:\n", "{'subsample': 0.9, 'n_estimators': 300, 'max_depth': 7, 'learning_rate': 0.01, 'colsample_bytree': 0.8}\n", "\n", "📊 Best Cross-Validation Score: 0.9935\n", "\n", "Validation accuracy (optimized): 0.9879\n", "Test accuracy (optimized): 0.9909\n", "Improvement over baseline: 0.00%\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Hyperparameter Optimization for XGBoost\n", "    print(\"\\n2️⃣ Hyperparameter Optimization (XGBoost)\")\n", "    print(\"=\" * 50)\n", "\n", "    # Define parameter grid\n", "    param_grid_xgb = {\n", "        'n_estimators': [100, 200, 300],\n", "        'max_depth': [3, 5, 7, 10],\n", "        'learning_rate': [0.01, 0.1, 0.3],\n", "        'subsample': [0.8, 0.9, 1.0],\n", "        'colsample_bytree': [0.8, 0.9, 1.0]\n", "    }\n", "\n", "    # Randomized Search (faster than Grid Search)\n", "    print(\"\\nPerforming Randomized Search...\")\n", "    xgb_random = RandomizedSearchCV(\n", "        xgb.XGBClassifier(random_state=42, n_jobs=-1, eval_metric='mlogloss'),\n", "        param_distributions=param_grid_xgb,\n", "        n_iter=20,\n", "        cv=5,\n", "        scoring='accuracy',\n", "        n_jobs=-1,\n", "        random_state=42,\n", "        verbose=1\n", "    )\n", "\n", "    start_time = time.time()\n", "    xgb_random.fit(X_train, y_train)\n", "    random_time = time.time() - start_time\n", "\n", "    print(f\"\\n✅ Randomized Search completed in {random_time:.2f} seconds\")\n", "    print(f\"\\n🎯 Best Parameters:\")\n", "    print(xgb_random.best_params_)\n", "    print(f\"\\n📊 Best Cross-Validation Score: {xgb_random.best_score_:.4f}\")\n", "\n", "    # Evaluate best model\n", "    xgb_best = xgb_random.best_estimator_\n", "    y_val_pred_xgb_best = xgb_best.predict(X_val)\n", "    y_test_pred_xgb_best = xgb_best.predict(X_test)\n", "\n", "    val_acc_xgb_best = accuracy_score(y_val, y_val_pred_xgb_best)\n", "    test_acc_xgb_best = accuracy_score(y_test, y_test_pred_xgb_best)\n", "\n", "    print(f\"\\nValidation accuracy (optimized): {val_acc_xgb_best:.4f}\")\n", "    print(f\"Test accuracy (optimized): {test_acc_xgb_best:.4f}\")\n", "    print(f\"Improvement over baseline: {(test_acc_xgb_best - test_acc_xgb)*100:.2f}%\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 FEATURE IMPORTANCE COMPARISON\n", "==================================================\n", "       Feature  Random_<PERSON>  Gradient_Boosting   XGBoost   Average\n", "6     rainfall       0.219002           0.236381  0.180051  0.211811\n", "4     humidity       0.209953           0.248057  0.168058  0.208689\n", "2            K       0.181427           0.173103  0.183441  0.179323\n", "1            P       0.161012           0.135370  0.164320  0.153567\n", "0            N       0.104354           0.112871  0.144227  0.120484\n", "3  temperature       0.075588           0.056506  0.092371  0.074822\n", "5           ph       0.048664           0.037712  0.067532  0.051303\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Compare Feature Importance across models\n", "    print(\"\\n📊 FEATURE IMPORTANCE COMPARISON\")\n", "    print(\"=\" * 50)\n", "\n", "    # Create comparison dataframe\n", "    importance_comparison = pd.DataFrame({\n", "        'Feature': feature_cols,\n", "        'Random_Forest': rf_best.feature_importances_,\n", "        'Gradient_Boosting': gb_baseline.feature_importances_,\n", "        'XGBoost': xgb_best.feature_importances_\n", "    })\n", "\n", "    # Calculate average importance\n", "    importance_comparison['Average'] = importance_comparison[['Random_Forest', 'Gradient_Boosting', 'XGBoost']].mean(axis=1)\n", "    importance_comparison = importance_comparison.sort_values('Average', ascending=False)\n", "\n", "    print(importance_comparison)\n", "\n", "    # Visualize comparison\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    x = np.arange(len(feature_cols))\n", "    width = 0.25\n", "\n", "    ax.bar(x - width, importance_comparison['Random_Forest'], width, label='Random Forest')\n", "    ax.bar(x, importance_comparison['Gradient_Boosting'], width, label='Gradient Boosting')\n", "    ax.bar(x + width, importance_comparison['XGBoost'], width, label='XGBoost')\n", "\n", "    ax.set_xlabel('Features')\n", "    ax.set_ylabel('Importance')\n", "    ax.set_title('Feature Importance Comparison Across Ensemble Methods')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(importance_comparison['Feature'])\n", "    ax.legend()\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔄 ADVANCED CROSS-VALIDATION ANALYSIS\n", "==================================================\n", "\n", "Performing 10-Fold Stratified Cross-Validation...\n", "\n", "Random Forest:\n", "  Mean Accuracy: 0.9935 (+/- 0.0174)\n", "  Min: 0.9740, Max: 1.0000\n", "\n", "Gradient Boosting:\n", "  Mean Accuracy: 0.9812 (+/- 0.0197)\n", "  Min: 0.9610, Max: 0.9935\n", "\n", "XGBoost:\n", "  Mean Accuracy: 0.9929 (+/- 0.0123)\n", "  Min: 0.9805, Max: 1.0000\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Cross-Validation Analysis\n", "    print(\"\\n🔄 ADVANCED CROSS-VALIDATION ANALYSIS\")\n", "    print(\"=\" * 50)\n", "\n", "    # Define models\n", "    models = {\n", "        'Random Forest': rf_best,\n", "        'Gradient Boosting': gb_baseline,\n", "        'XGBoost': xgb_best\n", "    }\n", "\n", "    # Perform stratified k-fold cross-validation\n", "    cv_results = {}\n", "    skf = StratifiedKFold(n_splits=10, shuffle=True, random_state=42)\n", "\n", "    print(\"\\nPerforming 10-Fold Stratified Cross-Validation...\\n\")\n", "    for name, model in models.items():\n", "        scores = cross_val_score(model, X_train, y_train, cv=skf, scoring='accuracy', n_jobs=-1)\n", "        cv_results[name] = scores\n", "        print(f\"{name}:\")\n", "        print(f\"  Mean Accuracy: {scores.mean():.4f} (+/- {scores.std() * 2:.4f})\")\n", "        print(f\"  Min: {scores.min():.4f}, Max: {scores.max():.4f}\")\n", "        print()\n", "\n", "    # Visualize cross-validation results\n", "    plt.figure(figsize=(12, 6))\n", "    plt.boxplot(cv_results.values(), labels=cv_results.keys())\n", "    plt.ylabel('Accuracy')\n", "    plt.title('10-Fold Cross-Validation Results')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🗳️ VOTING CLASSIFIER (ENSEMBLE)\n", "==================================================\n", "\n", "1️⃣ Hard Voting Classifier\n", "Validation accuracy: 0.9879\n", "Test accuracy: 1.0000\n", "\n", "2️⃣ Soft Voting Classifier\n", "Validation accuracy: 0.9909\n", "Test accuracy: 1.0000\n"]}], "source": ["# Ensemble Methods - Voting Classifier\n", "print(\"\\n🗳️ VOTING CLASSIFIER (ENSEMBLE)\")\n", "print(\"=\" * 50)\n", "\n", "# Create base estimators\n", "estimators = [\n", "    ('rf', rf_best),\n", "    ('gb', gb_baseline),\n", "    ('xgb', xgb_best)\n", "]\n", "\n", "# Hard Voting\n", "print(\"\\n1️⃣ Hard Voting Classifier\")\n", "voting_hard = VotingClassifier(estimators=estimators, voting='hard', n_jobs=-1)\n", "voting_hard.fit(X_train, y_train)\n", "\n", "y_val_pred_voting_hard = voting_hard.predict(X_val)\n", "y_test_pred_voting_hard = voting_hard.predict(X_test)\n", "\n", "val_acc_voting_hard = accuracy_score(y_val, y_val_pred_voting_hard)\n", "test_acc_voting_hard = accuracy_score(y_test, y_test_pred_voting_hard)\n", "\n", "print(f\"Validation accuracy: {val_acc_voting_hard:.4f}\")\n", "print(f\"Test accuracy: {test_acc_voting_hard:.4f}\")\n", "\n", "# Soft Voting\n", "print(\"\\n2️⃣ Soft Voting Classifier\")\n", "voting_soft = VotingClassifier(estimators=estimators, voting='soft', n_jobs=-1)\n", "voting_soft.fit(X_train, y_train)\n", "\n", "y_val_pred_voting_soft = voting_soft.predict(X_val)\n", "y_test_pred_voting_soft = voting_soft.predict(X_test)\n", "\n", "val_acc_voting_soft = accuracy_score(y_val, y_val_pred_voting_soft)\n", "test_acc_voting_soft = accuracy_score(y_test, y_test_pred_voting_soft)\n", "\n", "print(f\"Validation accuracy: {val_acc_voting_soft:.4f}\")\n", "print(f\"Test accuracy: {test_acc_voting_soft:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🏗️ STACKING CLASSIFIER\n", "==================================================\n", "\n", "Training Stacking Classifier...\n"]}], "source": ["if XGBOOST_AVAILABLE:\n", "    # Stacking Classifier\n", "    print(\"\\n🏗️ STACKING CLASSIFIER\")\n", "    print(\"=\" * 50)\n", "\n", "    # Define base learners\n", "    base_learners = [\n", "        ('rf', RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)),\n", "        ('gb', GradientBoostingClassifier(n_estimators=100, random_state=42)),\n", "        ('xgb', xgb.XGBClassifier(n_estimators=100, random_state=42, n_jobs=-1, eval_metric='mlogloss'))\n", "    ]\n", "\n", "    # Define meta-learner\n", "    meta_learner = LogisticRegression(max_iter=1000, random_state=42)\n", "\n", "    # Create stacking classifier\n", "    stacking_clf = StackingClassifier(\n", "        estimators=base_learners,\n", "        final_estimator=meta_learner,\n", "        cv=5,\n", "        n_jobs=-1\n", "    )\n", "\n", "    print(\"\\nTraining Stacking Classifier...\")\n", "    start_time = time.time()\n", "    stacking_clf.fit(X_train, y_train)\n", "    train_time = time.time() - start_time\n", "\n", "    # Predictions\n", "    y_val_pred_stacking = stacking_clf.predict(X_val)\n", "    y_test_pred_stacking = stacking_clf.predict(X_test)\n", "\n", "    # Metrics\n", "    val_acc_stacking = accuracy_score(y_val, y_val_pred_stacking)\n", "    test_acc_stacking = accuracy_score(y_test, y_test_pred_stacking)\n", "\n", "    print(f\"\\nTraining time: {train_time:.2f} seconds\")\n", "    print(f\"Validation accuracy: {val_acc_stacking:.4f}\")\n", "    print(f\"Test accuracy: {test_acc_stacking:.4f}\")\n", "\n", "    # Detailed classification report\n", "    print(\"\\n📊 Classification Report (Stacking):\")\n", "    print(classification_report(y_test, y_test_pred_stacking, target_names=le.classes_))\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if XGBOOST_AVAILABLE:\n", "    # Model Performance Comparison\n", "    print(\"\\n📊 MODEL PERFORMANCE COMPARISON\")\n", "    print(\"=\" * 50)\n", "\n", "    # Compile results\n", "    results_summary = pd.DataFrame({\n", "        'Model': [\n", "            'Random Forest (Baseline)',\n", "            'Random Forest (Optimized)',\n", "            'Gradient Boosting',\n", "            'XGBoost (Baseline)',\n", "            'XGBoost (Optimized)',\n", "            'Voting (Hard)',\n", "            'Voting (Soft)',\n", "            'Stacking'\n", "        ],\n", "        'Validation_Accuracy': [\n", "            val_acc,\n", "            val_acc_best,\n", "            val_acc_gb,\n", "            val_acc_xgb,\n", "            val_acc_xgb_best,\n", "            val_acc_voting_hard,\n", "            val_acc_voting_soft,\n", "            val_acc_stacking\n", "        ],\n", "        'Test_Accuracy': [\n", "            test_acc,\n", "            test_acc_best,\n", "            test_acc_gb,\n", "            test_acc_xgb,\n", "            test_acc_xgb_best,\n", "            test_acc_voting_hard,\n", "            test_acc_voting_soft,\n", "            test_acc_stacking\n", "        ]\n", "    })\n", "\n", "    results_summary = results_summary.sort_values('Test_Accuracy', ascending=False)\n", "    print(\"\\n🏆 Model Rankings:\")\n", "    print(results_summary.to_string(index=False))\n", "\n", "    # Visualize comparison\n", "    fig, ax = plt.subplots(figsize=(14, 8))\n", "    x = np.arange(len(results_summary))\n", "    width = 0.35\n", "\n", "    ax.bar(x - width/2, results_summary['Validation_Accuracy'], width, label='Validation', alpha=0.8)\n", "    ax.bar(x + width/2, results_summary['Test_Accuracy'], width, label='Test', alpha=0.8)\n", "\n", "    ax.set_xlabel('Models')\n", "    ax.set_ylabel('Accuracy')\n", "    ax.set_title('Model Performance Comparison')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(results_summary['Model'], rotation=45, ha='right')\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3, axis='y')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Find best model\n", "    best_model_name = results_summary.iloc[0]['Model']\n", "    best_test_acc = results_summary.iloc[0]['Test_Accuracy']\n", "    print(f\"\\n🥇 Best Model: {best_model_name}\")\n", "    print(f\"   Test Accuracy: {best_test_acc:.4f}\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if XGBOOST_AVAILABLE:\n", "    # Save Models and Results\n", "    print(\"\\n💾 SAVING MODELS AND RESULTS\")\n", "    print(\"=\" * 50)\n", "\n", "    # Save best models\n", "    joblib.dump(rf_best, '../models/saved_models/random_forest_optimized.pkl')\n", "    print(\"✅ Random Forest (Optimized) saved\")\n", "\n", "    joblib.dump(gb_baseline, '../models/saved_models/gradient_boosting.pkl')\n", "    print(\"✅ Gradient Bo<PERSON>ing saved\")\n", "\n", "    joblib.dump(xgb_best, '../models/saved_models/xgboost_optimized.pkl')\n", "    print(\"✅ XGBoost (Optimized) saved\")\n", "\n", "    joblib.dump(voting_soft, '../models/saved_models/voting_soft.pkl')\n", "    print(\"✅ Voting Classifier (Soft) saved\")\n", "\n", "    joblib.dump(stacking_clf, '../models/saved_models/stacking_classifier.pkl')\n", "    print(\"✅ Stacking Classifier saved\")\n", "\n", "    # Save scaler and label encoder\n", "    joblib.dump(scaler, '../models/saved_models/advanced_scaler.pkl')\n", "    joblib.dump(le, '../models/saved_models/advanced_label_encoder.pkl')\n", "    print(\"✅ Scaler and Label Encoder saved\")\n", "\n", "    # Save results summary\n", "    results_summary.to_csv('../data/processed/advanced_techniques_results.csv', index=False)\n", "    print(\"✅ Results summary saved\")\n", "\n", "    # Save feature importance comparison\n", "    importance_comparison.to_csv('../data/processed/feature_importance_comparison.csv', index=False)\n", "    print(\"✅ Feature importance comparison saved\")\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if XGBOOST_AVAILABLE:\n", "    # Summary and Conclusions\n", "    print(\"\\n📝 SUMMARY AND CONCLUSIONS\")\n", "    print(\"=\" * 50)\n", "\n", "    print(f\"\\n🎯 Advanced Techniques Summary:\")\n", "\n", "    print(f\"\\n1️⃣ Feature Selection:\")\n", "    print(f\"   - All 7 features show significant importance\")\n", "    print(f\"   - Top features: {', '.join(importance_comparison.head(3)['Feature'].tolist())}\")\n", "    print(f\"   - Feature selection methods (ANOVA, MI) show consistent rankings\")\n", "\n", "    print(f\"\\n2️⃣ Ensemble Methods Performance:\")\n", "    print(f\"   - Random Forest: {test_acc_best:.4f}\")\n", "    print(f\"   - Gradient Boosting: {test_acc_gb:.4f}\")\n", "    print(f\"   - XGBoost: {test_acc_xgb_best:.4f}\")\n", "    print(f\"   - Voting (Soft): {test_acc_voting_soft:.4f}\")\n", "    print(f\"   - Stacking: {test_acc_stacking:.4f}\")\n", "\n", "    print(f\"\\n3️⃣ Hyperparameter Optimization Impact:\")\n", "    print(f\"   - Random Forest improvement: {(test_acc_best - test_acc)*100:.2f}%\")\n", "    print(f\"   - XGBoost improvement: {(test_acc_xgb_best - test_acc_xgb)*100:.2f}%\")\n", "    print(f\"   - Optimization significantly improved model performance\")\n", "\n", "    print(f\"\\n4️⃣ Best Model: {best_model_name}\")\n", "    print(f\"   - Test Accuracy: {best_test_acc:.4f}\")\n", "    print(f\"   - Robust performance across validation and test sets\")\n", "    print(f\"   - Suitable for crop recommendation deployment\")\n", "\n", "    print(f\"\\n5️⃣ Key Insights:\")\n", "    print(f\"   ✅ Ensemble methods outperform individual models\")\n", "    print(f\"   ✅ Hyperparameter tuning provides measurable improvements\")\n", "    print(f\"   ✅ Stacking and voting combine strengths of multiple models\")\n", "    print(f\"   ✅ Cross-validation ensures robust performance estimates\")\n", "    print(f\"   ✅ Feature importance is consistent across methods\")\n", "\n", "    print(f\"\\n6️⃣ Agricultural Applications:\")\n", "    print(f\"   🌾 Accurate crop recommendations based on soil and climate\")\n", "    print(f\"   🌾 Identify key factors influencing crop suitability\")\n", "    print(f\"   🌾 Robust predictions for diverse agricultural conditions\")\n", "    print(f\"   🌾 Scalable solution for precision agriculture\")\n", "\n", "    print(f\"\\n7️⃣ Recommendations:\")\n", "    print(f\"   💡 Deploy {best_model_name} for production use\")\n", "    print(f\"   💡 Monitor model performance on new data\")\n", "    print(f\"   💡 Retrain periodically with updated agricultural data\")\n", "    print(f\"   💡 Consider ensemble methods for critical decisions\")\n", "    print(f\"   💡 Use feature importance for agricultural insights\")\n", "\n", "    print(\"\\n\" + \"=\" * 50)\n", "    print(\"✅ Advanced Techniques Analysis Complete!\")\n", "    print(\"=\" * 50)\n", "else:\n", "    print(\"⚠️  Skipping XGBoost section (not installed)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}