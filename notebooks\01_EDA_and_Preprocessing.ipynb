{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 Notebook 01: Exploratory Data Analysis and Preprocessing\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Load and explore the crop recommendation dataset\n", "2. Perform comprehensive exploratory data analysis (EDA)\n", "3. Data cleaning and quality checks\n", "4. Feature analysis and visualization\n", "5. Data preprocessing and transformation\n", "6. Train/validation/test split\n", "7. Feature scaling and encoding\n", "8. Save preprocessed data and objects for subsequent notebooks"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "import joblib\n", "import os\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Directories created/verified\n"]}], "source": ["# Create directories if they don't exist\n", "os.makedirs('../data/processed', exist_ok=True)\n", "os.makedirs('../models/saved_models', exist_ok=True)\n", "os.makedirs('../results/figures', exist_ok=True)\n", "os.makedirs('../results/metrics', exist_ok=True)\n", "\n", "print(\"✅ Directories created/verified\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 LOADING DATASET\n", "==================================================\n", "Dataset loaded successfully!\n", "Shape: (2200, 8)\n", "\n", "First few rows:\n", "    N   P   K  temperature   humidity        ph    rainfall label\n", "0  90  42  43    20.879744  82.002744  6.502985  202.935536  rice\n", "1  85  58  41    21.770462  80.319644  7.038096  226.655537  rice\n", "2  60  55  44    23.004459  82.320763  7.840207  263.964248  rice\n", "3  74  35  40    26.491096  80.158363  6.980401  242.864034  rice\n", "4  78  42  42    20.130175  81.604873  7.628473  262.717340  rice\n", "5  69  37  42    23.058049  83.370118  7.073454  251.055000  rice\n", "6  69  55  38    22.708838  82.639414  5.700806  271.324860  rice\n", "7  94  53  40    20.277744  82.894086  5.718627  241.974195  rice\n", "8  89  54  38    24.515881  83.535216  6.685346  230.446236  rice\n", "9  68  58  38    23.223974  83.033227  6.336254  221.209196  rice\n"]}], "source": ["# Load the dataset\n", "print(\"📊 LOADING DATASET\")\n", "print(\"=\" * 50)\n", "\n", "data = pd.read_csv('../data/raw/Crop_recommendation.csv')\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {data.shape}\")\n", "print(f\"\\nFirst few rows:\")\n", "print(data.head(10))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📈 DATASET INFORMATION\n", "==================================================\n", "\n", "1️⃣ Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2200 entries, 0 to 2199\n", "Data columns (total 8 columns):\n", " #   Column       Non-Null Count  Dtype  \n", "---  ------       --------------  -----  \n", " 0   N            2200 non-null   int64  \n", " 1   P            2200 non-null   int64  \n", " 2   K            2200 non-null   int64  \n", " 3   temperature  2200 non-null   float64\n", " 4   humidity     2200 non-null   float64\n", " 5   ph           2200 non-null   float64\n", " 6   rainfall     2200 non-null   float64\n", " 7   label        2200 non-null   object \n", "dtypes: float64(4), int64(3), object(1)\n", "memory usage: 137.6+ KB\n", "None\n", "\n", "2️⃣ Statistical Summary:\n", "                 N            P            K  temperature     humidity  \\\n", "count  2200.000000  2200.000000  2200.000000  2200.000000  2200.000000   \n", "mean     50.551818    53.362727    48.149091    25.616244    71.481779   \n", "std      36.917334    32.985883    50.647931     5.063749    22.263812   \n", "min       0.000000     5.000000     5.000000     8.825675    14.258040   \n", "25%      21.000000    28.000000    20.000000    22.769375    60.261953   \n", "50%      37.000000    51.000000    32.000000    25.598693    80.473146   \n", "75%      84.250000    68.000000    49.000000    28.561654    89.948771   \n", "max     140.000000   145.000000   205.000000    43.675493    99.981876   \n", "\n", "                ph     rainfall  \n", "count  2200.000000  2200.000000  \n", "mean      6.469480   103.463655  \n", "std       0.773938    54.958389  \n", "min       3.504752    20.211267  \n", "25%       5.971693    64.551686  \n", "50%       6.425045    94.867624  \n", "75%       6.923643   124.267508  \n", "max       9.935091   298.560117  \n", "\n", "3️⃣ Column Names and Types:\n", "  N: int64\n", "  P: int64\n", "  K: int64\n", "  temperature: float64\n", "  humidity: float64\n", "  ph: float64\n", "  rainfall: float64\n", "  label: object\n", "\n", "4️⃣ Missing Values:\n", "  ✅ No missing values found!\n", "\n", "5️⃣ Duplicate Rows:\n", "  Number of duplicates: 0\n"]}], "source": ["# Basic dataset information\n", "print(\"\\n📈 DATASET INFORMATION\")\n", "print(\"=\" * 50)\n", "\n", "print(\"\\n1️⃣ Dataset Info:\")\n", "print(data.info())\n", "\n", "print(\"\\n2️⃣ Statistical Summary:\")\n", "print(data.describe())\n", "\n", "print(\"\\n3️⃣ Column Names and Types:\")\n", "for col in data.columns:\n", "    print(f\"  {col}: {data[col].dtype}\")\n", "\n", "print(\"\\n4️⃣ Missing Values:\")\n", "missing = data.isnull().sum()\n", "if missing.sum() == 0:\n", "    print(\"  ✅ No missing values found!\")\n", "else:\n", "    print(missing[missing > 0])\n", "\n", "print(\"\\n5️⃣ Duplicate Rows:\")\n", "duplicates = data.duplicated().sum()\n", "print(f\"  Number of duplicates: {duplicates}\")\n", "if duplicates > 0:\n", "    print(f\"  Removing {duplicates} duplicate rows...\")\n", "    data = data.drop_duplicates()\n", "    print(f\"  ✅ New shape: {data.shape}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 TARGET VARIABLE ANALYSIS\n", "==================================================\n", "\n", "Crop Distribution:\n", "label\n", "rice           100\n", "maize          100\n", "jute           100\n", "cotton         100\n", "coconut        100\n", "papaya         100\n", "orange         100\n", "apple          100\n", "muskmelon      100\n", "watermelon     100\n", "grapes         100\n", "mango          100\n", "banana         100\n", "pomegranate    100\n", "lentil         100\n", "blackgram      100\n", "mungbean       100\n", "mothbeans      100\n", "pigeonpeas     100\n", "kidneybeans    100\n", "chickpea       100\n", "coffee         100\n", "Name: count, dtype: int64\n", "\n", "Number of unique crops: 22\n", "Crops: ['apple', 'banana', 'blackgram', 'chickpea', 'coconut', 'coffee', 'cotton', 'grapes', 'jute', 'kidneybeans', 'lentil', 'maize', 'mango', 'mothbeans', 'mungbean', 'muskmelon', 'orange', 'papaya', 'pigeonpeas', 'pomegranate', 'rice', 'watermelon']\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABRYAAAJOCAYAAADCoVvKAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/OQEPoAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3hT5dsH8O9JmnTvFiir7EIpo0wREAREZYugjBcEVJChAvIDEQUREEWGUkBAURQHGwQFlCEqyIYCZRQ66KS76Uybdd4/agOhu0170vL9XFcuknOe8zz3OQ3tyZ1nCKIoiiAiIiIiIiIiIiIqA5nUARAREREREREREVH1w8QiERERERERERERlRkTi0RERERERERERFRmTCwSERERERERERFRmTGxSERERERERERERGXGxCIRERERERERERGVGROLREREREREREREVGZMLBIREREREREREVGZMbFIRGTBRFF8rNuvjnjNiIiIqCpIfc8hdfuPC15nsnRMLBKRRbt+/Tr+97//oXfv3mjbti369euHDz74AFFRUVKHBh8fH5OHr68vunbtikmTJuHPP/80KRsdHQ0fHx/s3bu31PVv2LABW7ZsKbFcnz598O6775a7naLcvXsXo0ePNtnm4+ODgICACtddFm+88QZ27dplsi0+Ph4rVqzAc889h3bt2qFHjx544403cPHixSqN7VHHjx/HvHnzjK/DwsLQp08fpKenSxgVERFRzcT7RN4nPnyfGBAQUOR1nz59Ou7evVulsZnDo/eWVW337t2YPHmyZO1T9WAldQBEREX58ccf8fHHH6Nr16545513UKtWLURERGDLli34448/8N1336Fly5aSxjhixAiMHDkSAKDVapGYmIg9e/bgjTfewIIFCzB+/HgAQK1atbBjxw40bNiw1HV/8cUXmDFjRonl1q1bBwcHh/KdQDGOHDmCK1eumGzbsWMH6tSpY/a2irJ3717Ex8fjxRdfNG67dOkSpk+fDldXV4wfPx6NGzeGSqXCjh07MG7cOCxfvhzDhg2rshgftnXrVpPXTZo0Qd++fbF06VKsWLFCkpiIiIhqIt4n8j6xsPvE/Djy6fV6xMbGYs2aNRg7dix+++03eHp6VlmMFfXovWVVe/HFF/Hjjz9i9+7dGDFihKSxkOViYpGILNKlS5ewbNkyjB07FgsWLDBu79q1K/r164dhw4bhvffeM8s3rhVRp04dtG/f3mTbgAED8Oabb2LFihXo06cP6tevD6VSWaCcufj6+lZKvYWprHMoTE5ODlauXIlFixZBJsvrYK9SqTBz5kw0atQI3377LWxtbY3ln332WUyePBkLFy5Ejx494OHhUWWxFmfy5Mno3bs3XnnlFbRu3VrqcIiIiKo93ieW3uN0n1hUHB07doSXlxfGjh2Lffv2sQdeGQiCgClTpuCjjz7CoEGDYGNjI3VIZIE4FJqILNKWLVvg6OiI2bNnF9jn5uaGd999F3379kV2djaAvGEeH3/8MV555RW0bdvWeJOZkJCA+fPno1evXmjbti1GjBiB48ePm9Tn4+ODH374AfPmzYO/vz+efPJJLFu2DLm5ueWOf9asWdBqtdi9ezeAgkNPDAYD1qxZgz59+sDPzw99+vTBqlWroNVqjTEBed8y5z8PCAjAM888g3Xr1qFLly7o0aMH0tLSTIa45IuPj8eUKVPQtm1b9OrVC2vXroVerzc550eHquQPH8l/vm7dugJlHz2utNf3xx9/xIIFC9ClSxf4+/vj7bffRlJSUrHXcM+ePcjNzcXTTz9t3LZ//34kJCTgvffeM0kqAoBMJsOcOXMwduxYZGZmGrefPn0aY8aMQceOHY29Gu7fv1/oeT8ad/655v/8Dh8+jLfeegv+/v7o0qUL3n//feN7cNy4cTh//jzOnz8PHx8fnDt3DgDg6emJJ554Aps2bSr2fImIiKh0eJ/I+8TC7hOL4+fnBwCIiYkxbrtz5w6mTJmCDh06oEOHDpg+fbrJMPpz587Bx8cH27dvx9NPP40OHTrg9OnTAIC//voLo0aNQvv27dGjRw8sXLjQZOqb2NhYzJ49G126dEG7du3wyiuv4ObNm8b9Fbm3vH37NmbMmIEnnngCrVu3Rs+ePbF06VLk5OQY68/MzMTChQvRrVs3+Pv7Y9asWdi6dWuBe95jx45h+PDhaNOmDbp3746lS5ca28/39NNPIzc3F3v27CnVtabHDxOLRGRxRFHEqVOn0K1btwLJo3wDBgzA9OnTYWdnZ9z2448/ok2bNtiwYQNGjBiBpKQkjBgxAhcvXsSsWbMQEBCAevXqYfr06Thw4IBJfV988QWSk5Px+eef47XXXsOOHTsqNJ9JkyZNULduXVy6dKnQ/V999RV+/vlnTJ8+Hd988w1Gjx6NLVu24MsvvwTwYAjHiBEjTIZzxMbG4q+//sKaNWswf/58ODs7F1p/QEAA3N3dsX79erz44ovYuHEjPv3001LHP3LkSONwhx07dhiH8TysLNd3zZo1MBgMWL16NebOnYs///wTH3/8cbExHDhwAL1794ZSqTRu++eff+Dh4YG2bdsWekzLli0xb948NGrUCEBeInLSpEnw8vLC6tWrMX/+fFy5cgUvv/wykpOTS3098i1atAj16tXDhg0b8Oqrr2L37t3Gn9miRYvg6+sLX19f7Nixw6R34nPPPYcTJ04gKyurzG0SERHRA7xP5H0iUPh9YnHCw8MBwDjcPDw8HKNGjUJycjI+/fRTLFu2DFFRURg9enSBe8R169Zh3rx5WLhwIfz9/fHnn39iypQpcHd3x+eff445c+bg2LFjmDVrFgAgJSUFo0aNwo0bN/DBBx9g1apVMBgMGDt2LEJDQ03qLuu9ZUJCAsaOHQu1Wo1PPvkEX331FQYOHIht27bh+++/N9Y7bdo0HD58GG+++SbWrFmDrKwsrFq1yqTtgwcPYvr06WjSpAnWr1+PGTNm4MCBA5g2bZrJgjHW1tZ4+umncfDgwVJda3r8cCg0EVmc1NRU5Obmon79+mU6rm7dupgzZ47x9WeffYaUlBT8/vvvqFevHgCgV69emDBhAlasWIFBgwYZh064ublh48aNsLKyQq9evSCTybB8+XK8+eabaNq0abnOw8PDo8hvW8+fPw8/Pz/jnDBdunSBra0tHB0dATwYwvHoEBqdTod58+ahU6dOxbbds2dP4w1Zz549kZmZiZ9++gnTpk2Di4tLibHXqVPHOEdOUcNavv3221Jf3xYtWmD58uXGY69du4YjR44U2X5mZiauX7+O559/3mR7XFycsa2SGAwGrFy5Ej169DC5kerQoQMGDBiALVu2YO7cuaWqK1+vXr2MHyS6deuG06dP4+TJk3jnnXfQrFkz4xxGj16zNm3aQKvV4uLFi+jVq1eZ2iQiIqIHeJ/I+8Si7hPz6XQ64/OcnBzcvn0bH3/8MRwdHTFkyBAAeclCW1tbbN261Xj/1q1bN/Tr1w9ff/21SeJ4zJgxeO6554yvAwIC0KpVK6xbtw6CIAAAlEolvvjiCyQlJWHbtm1QqVT4+eefjef+1FNPYcCAAfjiiy+wdu1aY11lvbcMDAxEq1at8MUXXxj3Pfnkkzh9+jTOnTuHyZMn48yZMzh37hwCAgLQv39/Y/uDBg0yJjZFUcTKlSvRs2dPrFy50hhPo0aNMGHCBPz111/o3bu3cXubNm1w6NAhZGZmVsqcnVS9scciEVkcuVwOACZDMkqjVatWJq/Pnz8Pf3//AomoIUOGIDExEWFhYcZtgwcPhpXVg+9ann32WQDAhQsXyhTDw0RRNN5sPKpr167GIbpff/01QkJC8H//938YOnRoifU+ep6FefRGq3///tBqtbh69Wrpgi+FslzfR28669SpA7VaXWTd9+/fh16vL/ChQS6Xl/p9ER4ejsTERAwaNMhke8OGDeHv74/z58+Xqp6HFXYejw4XKUz+NYqOji5zm0RERPQA7xOL9zjfJ+Zr3bq18dGxY0eMHTsWGo0G69atMy7ccvbsWXTp0gU2NjbQ6XTQ6XRwcHBAp06d8O+//5rU9/A1zcnJwc2bN9GvXz+Tn9+AAQPw+++/w8PDA2fOnEGrVq1Qu3ZtY90ymQxPPfVUgbrLem/Zo0cP/PDDD7C2tkZISAiOHz+OL7/8EikpKdBoNMZzUygU6Nevn/E4mUyGAQMGGF+HhYUhLi4Offr0Mcao0+nQuXNnODg4GId856tXrx70ej3i4uKKjI0eX+yxSEQWx9nZGfb29oiNjS2yTHZ2NrRarckQj4eHuwBAWloaGjRoUODY/EU9Hp4HpXbt2iZl3N3djXWUV1xcHFq0aFHovtdeew329vbYs2cPVq5cic8++wzNmzfH+++/jyeeeKLYeu3t7Uts+9HV7tzc3ABU7HweVZbrW9h8iA8PsXhURkYGgII/07p16+LatWvFxnX//n14eXlBpVKZxPNojA/Pc1NaZT2PR497eO5HIiIiKjveJ/I+saj7xHz5c1cCgEKhgKenp/Fnlk+lUuHQoUM4dOhQgePzr0e+h9tJS0uDKIoF6nu07oiIiCIX7Xs4aVrWc88fMv7jjz8iOzsbXl5eaNu2LaytrY1lUlNT4eLiUmBRm4djzr9PXrx4MRYvXlygnYSEBJPX+dcg/9oTPYyJRSKySD169MC5c+eQm5tr8ocy386dO/Hpp59i9+7dRf7RdnZ2RmJiYoHt+dtcXV2N21JTU03K5A9NefTGorRCQkKQmJiIsWPHFrpfJpNh7NixGDt2LJKTk/HXX39h48aNePPNN3H69OlSzxdTlEdvDPPP5+Ebike/6S9Nz7uHleX6llX+sQ/fdAJ5w3X+/PNPXL9+HW3atClw3K1btzBs2DDMnz8fPXv2BIBChxklJiYa28j/tlmv1xt7QZh7LsT886jINSEiIqI8vE/kfSJQ8D4xX2H3iI9ydHTEk08+iYkTJxbY93Dv1Ec5ODhAEASkpKSYbM/NzcXZs2fRrl07ODo6okuXLkVOuVORn9/mzZuxdetWLF68GP379zcOj8+f8xLIS4SnpqbCYDCYJBcfnjvSyckJADB37lx06dKlQDuPzs+Z/57hvSwVhkOhicgiTZo0CSqVCp9//nmBfYmJifjmm2/QrFmzIm8WAaBz5864cuWKyepvQN5kz56envD29jZuO3HihEmZ33//HYIglPitcFHWrl0LGxsbvPDCC4XuHzVqFJYuXQog7yZu+PDhGDt2LNLT04292h79lrEsTp48afL6t99+g62tLdq1awcg76YoPj7epMzly5dNXpfUflmub1nVrl0bcrm8wHCLIUOGwNPTE8uXLzdZ+Q7IuwFeuXIlFAoFnn/+eTRu3Bienp749ddfTcpFRUUhMDAQHTp0AADjPDEPt1XUZOolKeqa5dddt27dctVLRERED/A+kfeJhd0nlkWXLl0QEhKCVq1aoU2bNmjTpg38/PywdetWHD16tMjj7O3t0apVK/z5558m2//++29MnjwZCQkJ6NKlC8LDw9G4cWNj3W3atMEvv/yC3bt3G7/ILo1Hr/OlS5fQrFkzvPjii8akYnx8PO7cuQODwWA8N51OZ/K+FUURx44dM75u0qQJ3N3dER0dbRJj7dq1sWrVqgIje+Lj4yGXywv03iUC2GORiCxU+/bt8fbbb+Pzzz9HaGgohg0bBldXV9y9exdbtmxBbm5uoTeTD5s4cSIOHDiACRMmYMaMGXBxccH+/ftx9uxZfPzxxyZ/qAMDAzFnzhwMHToUt2/fRkBAAF566aVCh3A8LC4uDoGBgQDyJoqOj4/Hvn37cOrUKXz00UfGia0f1blzZ3zzzTfw8PCAv78/4uPj8e2336JLly7Gb7+dnJxw+fJlXLhwocRJuB/1xx9/oHbt2njyySdx6tQp7NixA2+//bYxida7d2/89ttvaNeuHby9vbF3715ERESY1JH/Teavv/6Kdu3aFbgWZbm+ZWVnZ4cOHTrg0qVLmDBhgnG7o6MjPvnkE8yYMQMjR47E//3f/6FRo0aIi4vDjz/+iGvXrmHVqlXGm57Zs2dj/vz5eOeddzBkyBCkpqZi3bp1cHZ2Nn5D3atXLyxfvhwLFy7Eq6++ivv372P9+vWlGkr0KCcnJ1y5cgVnzpyBr6+v8dveS5cuwdbWtsw/RyIiIiqI94m8TyzsPrEspk2bhlGjRmHKlCkYPXo0rK2tsWPHDhw7dsxkcZXCvPXWW5g6dSpmz56NYcOGISkpCatXr0a/fv3QokULTJgwAb/88gsmTJiASZMmwdXVFYcOHcLOnTsxf/78MsX56L1l27ZtsWHDBmzevBnt27dHREQENm3aBI1GYxxi3blzZ3Tv3h0LFixAUlIS6tati927dyM4ONg4Ukcul2PWrFlYuHAh5HI5nn76aaSnp2PDhg2Ij48vkJS/dOkSOnXqVORK7PR4Y2KRiCzW1KlT4evrix9//BEff/wx0tLS4OXlhd69e+ONN96Al5dXscd7enri559/xqpVq7B06VJotVq0bNkSGzZsQN++fU3KvvLKK4iPj8eMGTPg6uqKN954A1OmTCkxxt27dxvncZHJZHBxcUG7du3w7bffolu3bkUe9/bbb0OpVGLPnj1Yv349HB0d0adPH7zzzjvGMm+88QY2bNiA119/vdD5X4qzYMEC/Pbbb9i6dSs8PT3x3nvvYfz48cb98+fPh06nw6effgorKysMGDAA77zzDt5//31jmf79++OXX37Bu+++ixEjRuDDDz80aaMs17c8nn32WQQEBBQY5tSjRw/s2rUL33zzDTZt2oSkpCS4uLjAz88PO3bsMH7bDgDDhw+Hvb09Nm3ahOnTp8PBwQE9e/bE7NmzjfMLNW7cGJ9++im+/PJLTJ48GU2bNsWSJUuwZMmSMsc8duxYBAUF4fXXX8fy5csxePBgAHnfYvfu3Rs2NjYVvCpEREQE8D6R94mF3yeWVsuWLfHjjz9izZo1mDt3LkRRRIsWLbB+/foS43v66aexceNGrFu3DtOnT4ebmxsGDx6MN998E0Bej8rt27dj1apV+PDDD5Gbm4tGjRph2bJlJkOWS+PRe8spU6YgNTUV33//PdavXw8vLy8MHToUgiBg06ZNSE9Ph5OTE9asWYNPPvkEq1atgk6nQ9++fTF69Gjs37/fWPfIkSNhb2+Pr7/+Gjt27DAmbFeuXGmSKM7NzcW5c+cwc+bMMsVOjw9BLM2s80RENZiPjw9mzJhhvBkgy6BWq9GvXz/873//w7Bhw6QOp9xiYmLwzDPPYPfu3fD19ZU6HCIiIioD3idapppyn1gZYmJiEBgYiL59+5p8qf3WW28hKioK+/btK1N9+/fvx8qVK3Hs2DF+SU6F4hyLRERkkWxtbfHmm29iy5YtBSYQr06++eYbPPfcc0wqEhEREZlJTblPrAwymQzvvvsu3n33XZw6dQpnzpzBihUr8Mcff5j0TC0Ng8GAb775BjNmzGBSkYrExCIREVmsUaNGoU6dOti1a5fUoZRLaGgoTpw4gYULF0odChEREVGNUt3vEyuLl5cXvvrqKyQnJ2PmzJmYOnUqzp07h5UrVxa5YFBR9uzZA09PT4waNaqSoqWagEOhiYiIiIiIiIiIqMzYY5GIiIiIiIiIiIjKjIlFIiIiIiIiIiIiKjMmFomIiIiIiIiIiKjMmFgkIiIiIiIiIiKiMrOSOgApJSZmlOs4pVIOjabql7SXot3HpU2p2n1c2pSqXZ5rzWtTqnYflzalapfnWvPalKrdirTp6elo5miIiIiIaj72WCwjQTD9tya3+7i0KVW7j0ubUrXLc615bUrV7uPSplTt8lxrXptStSvVuRIRERE9zphYJCIiIiIiIiIiojJjYpGIiIiIiIiIiIjKjIlFIiIiIiIiIiIiKjMmFomIiIiIiIiIiKjMmFgkIiIiIiIiIiKiMmNikYiIiIiIiIiIiMqMiUUiIiIiIiIiIiIqMyYWiYiIiIiIiIiIqMyYWCQiIiIiIiIiIqIyY2KRiIiIiIiIiIiIyoyJRSIiIiIiIiIiIiozJhaJiIiIiIiIiIiozJhYJCIiIiIiIiIiojJjYpGIiIiIiIiIiIjKzCISixqNBoMGDcK5c+eM26KiojBhwgS0b98eAwYMwKlTp0yO+ffffzFo0CC0a9cO48ePR1RUVFWHTURERERERERE9NiSPLGYm5uL2bNn4+7du8Ztoihi+vTp8PDwwJ49ezB06FDMmDEDsbGxAIDY2FhMnz4dw4cPx+7du+Hm5oZp06ZBFEWpToOIiIiIiIiIiOixImliMSQkBC+99BIiIyNNtp89exZRUVH46KOP0LRpU0yZMgXt27fHnj17AAC7du2Cn58fJk2ahObNm2P58uWIiYnB+fPnpTgNIiIiIiIiIiKix46kicXz58+ja9eu2LFjh8n2q1evwtfXF3Z2dsZtHTt2RGBgoHF/p06djPtsbW3RunVr434iIiIiIiIiIiKqXFZSNj5mzJhCtycmJqJWrVom29zd3REXF1eq/URERERERERERFS5JE0sFkWtVkOpVJpsUyqV0Gg0pdpfFoJQvvJlPa6ipGj3cWlTqnYflzalapfnWvPalKrdx6VNqdrluda8NqVqV6pzJSIiInqcWWRi0draGiqVymSbRqOBjY2Ncf+jSUSNRgMnJ6cytaNUyouOYfG64mMsZl/uohlliqO0bRbXbnnbLE27ltRmRdrl9S25zYq0W92ub0XarW7nyutrnnYtqc2KtMvrK327ltRmRdp9nK4vERERERXOIhOLtWvXRkhIiMm2pKQk4/Dn2rVrIykpqcD+Vq1alakdjUZf5Lfaxd2Al0Sr1ZfrOCnarEi7UrRZkXZ5fSu33ep2fSvSbnU7V17fym2X17dy261u17ci7Va3c32cri8RERERFU7SxVuK0q5dO9y4cQM5OTnGbZcuXUK7du2M+y9dumTcp1arcfPmTeP+shDFwh8VUVSdJT2kaLMi7UrRZkXalaJNXl+eqyWcK68vr6/UbT5O1/dxOlcp2qzM60tEVB1FR0fDx8cHBw8eRM+ePdGpUycsXboUOp0Ooihi48aN6NOnD/z8/NCjRw+sW/eg1/e4ceOwbt06jB49Gu3atcOYMWMQGhpq3H/p0iXjvvbt2+P1119HQkICAKB///749ttvTWIZPHgwdu3aBQDYtWsXnnvuOfj5+aFr165YvHgx9Hp+wUNUE1lkYrFLly7w8vLC/PnzcffuXWzevBnXrl3DiBEjAAAvvvgiLl++jM2bN+Pu3buYP38+6tevj65du0ocOREREREREVHVWrduHdasWYN169bhjz/+QEBAAPbv34/vvvsOy5Ytw5EjRzB9+nQEBATgxo0bxuM2bdqEZ599Fnv37kXt2rUxefJkaDQaZGRkYMqUKejevTt+/fVXbNmyBZGRkdi8eTMAYODAgfj999+N9YSGhiI8PBz9+/fH+fPnsXTpUsyePRtHjhzB4sWLsXv3bhw/frzKrwsRVT6LTCzK5XJs2LABiYmJGD58OA4cOID169ejbt26AID69esjICAAe/bswYgRI6BSqbB+/XoInK2biIiIiIiIHjP/+9//0KlTJzzxxBN4++23sXPnTnh5eWH58uXo1q0b6tevj9GjR8PT0xN37941HvfUU09hwoQJaNq0KZYsWYKUlBScPn0aOTk5mDZtGqZPn44GDRqgY8eO6N+/v/HYQYMGITAwEHFxcQCAw4cPo0ePHnB2doadnR2WLVuG/v37o379+njuuefg6+tr0i4R1RwWM8dicHCwyWtvb2/88MMPRZbv1asXevXqVdlhEREREREREVm0Dh06GJ/7+fkhJSUFLVq0QFRUFFatWoXQ0FDcunULiYmJMBgMhR7n4OCAxo0bIzQ0FE8//TSGDRuGrVu34tatWwgJCUFwcLCxfNOmTeHj44MjR45gwoQJOHz4MKZMmWJs38bGBmvXrjUeFxERgR49elTR1SCiqmSRPRaJiIiIiIiIqHQUCoXxeX7icPfu3ZgwYQJyc3PRv39/bN26FXXq1DE5zsrKtK+RXq+HTCZDfHw8hgwZgrNnz6J169Z47733MHHiRJOyAwcOxB9//IHQ0FBER0ejb9++AIB//vkHw4cPR1JSEnr27Im1a9eaJDCJqGaxmB6LRERERERERFR2t27dQpcuXQAAQUFBqFWrlnFexddeew0AkJ6ejuTkZIgPrVh1+/Zt4/OMjAxERkbCx8cHR48ehbOzMzZt2mTcv23bNpNjBw0ahC+++AL79+9Hr169YG9vDyBv4ZYXX3wRixYtAgDodDpERkbiiSeeqLwLQBV2+PBhdOnSBe7u7lKHQtUMeywSERERERERVWPLli3D9evX8e+//+KLL77A2LFj4erqijNnziA8PBxBQUGYNWsWtFotNBqN8biDBw9i//79CA0NxYIFC1C3bl107doVLi4uiI2NxZkzZxAVFYXNmzfjjz/+MDm2bt26aNu2Lb777jsMHDjQuN3FxQVXrlxBcHAw7t69i3fffReJiYkmx5JliYmJwcyZM6FWq6UOhaoh9lgkIiIiIiIiqsYGDBiAKVOmwGAwYPTo0Zg8eTKeeeYZvPfeexg6dCjc3d3x/PPPw9bWFrdu3TIeN3jwYGzfvh2LFi1Cp06d8NVXX8HKygrPP/88Lly4gLfeeguCIKBNmzaYN28eAgICoNFooFQqje0GBwejd+/exjpnzJiB+fPn4+WXX4aDgwN69eqF0aNHm7RLluXhnqhEZcXEIhEREREREdV4cXFxWL58Oc6cOQNBEDB48GDMnTsXv/76K3bu3Al3d3ecPXsWixYtQp8+fbBs2TKcPHkSGRkZqF+/PubMmYN+/foBAHx8fLBixQp89dVXuHfvHtq2bYtPP/0UDRo0AJA3HPmjjz7C7du34evri27duuHixYvYtm0bAODo0aNYs2YNYmJi0Lx5c8ydO9c4lLk8Bg4caFw8JV/Tpk2xY8eOYo+rW7culi9fXmC7XC7H4sWLsXjxYpPtEyZMMHmdlJSEZ555BtbW1sZttWrVwpYtW8p4BjVPREQEPvroI1y+fBnOzs6YNGkSxo8fj9DQUHz88ce4cuUK7O3t8fLLL2PatGmQyfIGlP7yyy/48ssvcf/+fbRq1QoLFy6Er68vAGDv3r346quvEBMTg2bNmmH+/Pno3LkzAKBPnz549dVX8csvv+DWrVto0qQJli1bBj8/P+McmMePH0f9+vUBAAEBATh//jy2bdtmnB+zb9++WL58OYYPHy7BFaPqikOhiYiIiIiIqEbTaDR45ZVXoFarsW3bNnz++ec4efIkVqxYAQC4cuUKmjVrhp07d6JHjx5YtmwZwsPD8c033+DXX39Fp06dsGDBApPhvAEBAViwYAH27t2L1NRUfP755wDy5ip87bXX0Lp1a+zfvx+DBg3C5s2bjcfdvn0b8+bNw9SpU3HgwAEMGTIEr7/+OiIiIqr0mlTE7du3sW/fPvz0008YOXKk1OFYnNzcXEyaNAn29vbYuXMnFi5ciDVr1uCXX37BmDFjUKtWLezatQuLFi3CDz/8gO+//x5A3sI3CxYswCuvvIIDBw7Az88PU6ZMgUajwd69e7FkyRJMmTIF+/fvx5NPPonJkycjPj7e2G5AQAAmT56MAwcOwNHREUuXLi1VvLt27TL+O2DAAPNfEKrR2GORiIiIiIiIarR//vkH8fHx2LlzJ5ydnQEACxcuxNSpUzF//nwIgoCpU6fCxsYGANC5c2dMnDgRLVq0AABMmjQJu3btQnJyMry8vAAAEydORLdu3QAAo0ePxo8//ggAOHToEOzs7PD+++9DLpejSZMmuHz5MhITEwEAW7ZswUsvvYTBgwcDAMaPH48LFy7g559/xrvvvlt1F6UCgoKCsHTpUowZMwadOnWSOhyLc+rUKaSkpODjjz+Gg4MDmjdvjvfffx8qlQq2trZYsmQJrKys0LRpUyQmJmL9+vWYMGECduzYgUGDBmH06NEAgLlz50KhUCAtLQ3btm3DuHHjMGzYMADAnDlzcOHCBfzwww945513AAAvvPCCsVftxIkT8fbbb5cqXjc3N+O/+f8HiEqLiUUiIiIiIiKq0UJDQ9GoUSNjUhEAOnToAJ1OB51OB3d3d5OEyrBhw3Ds2DHs3LkTYWFhuHHjBgBAr9cby3h7exufOzg4QKvVAgCCg4PRunVryOVy4/727dvj6NGjxlgOHz5sMkxZq9WiR48eZT6v+vXrIzg4uMzHATAOyy6PESNGYMSIEeU+vqYLDw9H48aN4eDgYNyWv1J269atYWX1IBXj7++PxMREpKenIzw8HKNGjTLuUyqVmDdvHoC898306dNN2mnfvj1CQ0ONrxs1amR8/vB7kqgyMbFIRERERERENdrDcwDmy08SGgyGAvvnzp2LK1euYOjQoRg9ejQ8PT3x8ssvm5RRKBSFtiWXywsshvHwa71ej9dff93Y8ywfe4rVHA8nDh9W2PvQYDAAyHtfFHVcUcfq9Xrj8UDR70lBEAps0+l0RbZFVBacY5GIiIiIiIhqtMaNG+PevXtQqVTGbYGBgbCysjIumpEvMzMTv/76K9asWYO33noLzzzzDNLS0gCUbvXc5s2b49atWyYJn/wej/mxREdHw9vb2/jYsWMH/v777wqeJVmKRo0aISIiAmq12rjt008/xU8//YQbN26Y9CS8cuUK3Nzc4OLiAm9vb9y+fdu4T6/Xo0+fPrh06RIaN26Mq1evmrRz9epVNG7cuMR48hOOWVlZxm3R0dHG54UlHolKi4lFIiIiIiIiqtG6d++OBg0aYO7cuQgODsbZs2exZMkSDBo0CE5OTiZllUolbG1t8ccffyA6Ohr//PMPPvroIwAwWbylKAMHDkRmZiaWL1+O8PBw7Ny5E4cOHTLunzBhAg4dOoTvv/8ekZGR2Lp1K7Zu3WoyjJWqtx49esDDwwMLFy5EaGgojh8/ju3bt+Pzzz+HRqMxbj927BgCAgIwevRoCIKAcePG4cCBA9i3bx8iIiKwfPlyiKKI1q1bY8KECfjhhx+wf/9+hIeHY+XKlbh9+3aphqR7eHjAy8sLW7ZsQVRUFPbu3YuTJ08a99va2gLIW5Tn4eQjUWkwsUhEREREREQ1mlwux4YNGwAAL730EmbPno2+ffsaE4YPUyqV+Oyzz/D7779j4MCB+OSTTzB16lR4enri1q1bJbZlb2+PjRs34sKFCxg8eDD27duHwYMHQ6lUAsibF2/FihX46aefMGDAAOzcuROrVq1C586dzXvSJBkrKyts2LABCQkJeOGFF7Bs2TLMnTsX/fr1w9dff43IyEgMGzYMS5YswSuvvIIZM2YAyFs0aNGiRVi/fj2GDBmCW7duYePGjbCxscGAAQMwa9YsrF27FkOGDMH58+fxzTffoGnTpiXGI5PJsGzZMly7dg0DBgzAkSNH8MYbbxj3u7m5YciQIZg5c6ZxhWii0uIci0RERERERFTjNWjQAJs3by6wffjw4Rg+fLjJtn79+hlX1833cM+wRxdMebiOqKgo6PV67N+/37h/8eLF8PT0NL4eOHAgBg4cWO5zIcvXtGlTfPfddwW2+/r6GlcQL0xxC+OMHz8e48ePL3TfiRMnTF537drV5H3avXt3HDlyxKTM66+/bnz+2Wef4bPPPisyLqKisMciERERERERkZlkZmZi4sSJOHLkCGJiYvDHH3/gl19+wXPPPSd1aEREZscei0RERERERERm0qpVKyxcuBCrV6/G/fv3UbduXcyfPx+9e/eWOjQiIrNjYpGIiIiIiIjIjEaOHImRI0dKHQYRUaXjUGgiIiIiIiIiIiIqM/ZYJCIiIiIiIqIKMeSIELNEGLJEiGpA1IuAAYABEP/7F+J/hWV5D0F46LlcgGAHyOwFyBwECEpBojMhorJgYpGIiIiIiIiITBiyROiTDNAnizBk/JcwzBJhyALEbBGGTBGGbBFiVl5Z6M0cgAKQ2QkQ7P9LNtoLEP77V2YPCHYCZE4C5B4yyD0EyGyYiCSSAhOLRERERERERI8hMTMbYkIyDPEpyE1uCG2MLfSJIvTJBojZEgenBQxpIpAG6I1dHYsmOCAvyegpIL2pgBsNRTR0lsPbSQ4HJWeBI6osTCwSERERERER1WCiTgcxOh6GiPsQ7yfCkJACMTEFyFIby+i9BiI3pJF0QVaQmAnoMg3Q3QPi9MCymBzjPncbAQ2d5WjoJEdzVyv4elihqYscchl7ORJVFBOLRERERERERDWIIVkF8V4sDBGxMETehxiTAOiLH6ssV6ZVUXSVL9batIdjco6I5BwdrsTrAOQCAGytAB+3vCRjaw8rtPZQwMOOPRuJyoqJRSIiIiIiIqJqSszJhSEyDmLEg0QiMss+jlkm1JzEYpjSkLdYTDHUOiAwQYfABJ1xWy072UOJRiv4uFvBWs5ejUTFYWKRiIiIiIiIqJoQNVoYQiJhCL4Hw90IiPHJgFjyHIQlETSqigdnIW6I5VtJJiHbgIRIDU5GagAAVjKguascHeso0LWuEm08rWDF4dNEJphYJCIiIiIiIosSHR2Nvn374vjx46hfv77U4UjOEJcEw+3wvEdYNKDTlXxQWWWqzF+nFBTAHYMBMEP+T2cAbiXrcStZjx9u5MBOIaBjbSt0ravEE3UVqOMgr3gjNZQoivjpp58wduxYAIBGo8H+/fvx0ksvSRwZmRsTi0REREREREQWRNQbYAiLgiEoBIYbIRBTqmCYckYGoNAB2uqdJjC4CxArqVNhtlbEP9Fa/BOtBQA0dpajR30lejRQwNfdCoLA3oz5Lly4gI8++siYWPztt9+wceNGJhZroOr9G4OIiIiIiIioBhBzcmG4FQb9jRAYboUB6twqDgBQuKVBG+9ete2aWZZT1bUVnqZHeJoa226o4W4j4Mn6SvSor0RnLwWUj/ncjOIjw/MffU01B5c8IiIiIiIionK7dOkSRo8ejXbt2qF9+/Z4/fXXkZCQgL1792L06NFYuXIl/P390bt3b+zatct43Lhx47Bu3TrjsWPGjEFoaGihbaSnp+N///sfOnTogB49emDJkiXIycmpqlOsNKJBhD74HjQ/HETuovXQbjsIw+VbVZ9U/I+VY4Yk7ZpTioM0CazkHBEHQ3Ix72QGhu5JxcpzmbiRpJUkFnOLiIjAq6++avx//P333wMAQkND8eqrr6JDhw7o2bMn1q1bB4PBgOjoaIwfPx4A4OPjg3PnzmH+/PmIiYmBj48PoqOjYTAY8PXXX6Nv375o27Ytxo0bh+DgYGObPj4++OWXXzBo0CD4+flhzJgxiIqKkuT8qXhMLBIREREREVG5ZGRkYMqUKejevTt+/fVXbNmyBZGRkdi8eTMA4Pr167h16xZ27NiBGTNmYPHixTh16pTx+E2bNuHZZ5/F3r17Ubt2bUyePBkajaZAOwsWLEBGRgZ+/vlnbNiwAdevX8dHH31UZedpboaEZGh/+xu5SzdCu2lnXjJRWwnzJpaRXFn9V4aOtpG+Z1yGRsT+u7mYciQdYw+osC1IjcTs8i0oI7Xc3FxMmjQJ9vb22LlzJxYuXIg1a9bgl19+wZgxY1CrVi3s2rULixYtwg8//IDvv/8eXl5eCAgIAACcOnUK/v7+eO+991CnTh2cOnUKXl5eWL9+Pb755hu899572LdvH+rVq4fXXnsN2dkPVjQPCAjAggULsHfvXqSmpuLzzz+X6CpQcTgUmoiIiIiIiMolJycH06ZNw8SJEyEIAho0aID+/fvj2rVr8PX1hSAIWLFiBdzd3dGiRQtcuHABO3fuRI8ePQAATz31FCZMmAAAWLJkCXr27InTp0+jefPmxjYiIyNx7NgxnD9/Ho6Ojsayw4YNw/z5843bLJ2ozoH+yi3oL9yAGBErdTiFkgkqqUOosDArAyB9btEoIl2PTYHZ+OpqNjrVUeC5Jtbo1UAJa6vqMVT61KlTSElJwccffwwHBwc0b94c77//PlQqFWxtbbFkyRJYWVmhadOmSExMxPr16zFhwgQ4OzsDADw9PQEAjo6OkMvl8PT0hCiK+OGHHzB79mz07dsXQN7/6WeeeQYHDhzAqFGjAAATJ05Et27dAACjR4/Gjz/+KMEVoJIwsUhERERERETl4unpiWHDhmHr1q24desWQkJCEBwcjA4dOgAAvL294e7+YM4+Pz8/bN++3fg6vxwAODg4oHHjxggNDTVJLIaGhsJgMOCpp54yadtgMCAiIgJ+fn6VdXoVJhoMMATfg/7CdRiCQitnNWczEnJVUodQYTcEvUUlFvMZROD8fS3O39fCXiGgj7cSzzexRttaCqlDK1Z4eDgaN24MBwcH47YXX3wRixYtQuvWrWFl9SCt5O/vj8TERKSnpxdbZ3JyMlQqFdq1a2fcplAo4OfnZzIdgre3t/G5g4MDtNqaMbS8pmFikYiIiIiIiMolPj4eL774Ilq3bo0nn3wSL730Ek6ePImrV68CgEnSAQD0ej1ksgczcpW0P3+bo6Mj9uzZU6D92rVrm+tUzEpUZUB3+gr0F64D6VlSh1N6mSqpI6gYJRCiNwAW3hkwS5s3H+PBkFzUd5RhUDMbDGlmDSdry5ut7tH/o/msra0LbDMYDADy/s8Wp7Bj84/LrwPISzaS5WNikYiIiIiIiMrl6NGjcHZ2xqZNm4zbtm3bZlwBNiIiAllZWbC3twcABAUFoUWLFsayt2/fNj7PyMhAZGQkfHx8TNpo3LgxMjIyIAgCGjZsCAAIDg7G2rVrsXz5ctjY2FTa+ZWVIeo+dH9dhOFqMKA3lHyApcnMBJRaQFM9Ezp6NwGihScVHxWdYcDGK9n47no2nmtijZda2qKBk1zqsIwaNWqEiIgIqNVq2NraAgA+/fRT/PTTT/Dw8IBWqzUmAK9cuQI3Nze4uLhAEEx/EA+/dnR0hIeHBwIDA9GyZUsAgFarxY0bN9C9e/cqOjMyF8tLhxMREREREVG14OLigtjYWJw5cwZRUVHYvHkz/vjjD+MCLNnZ2Vi0aBFCQ0Oxc+dOHDlyBGPGjDEef/DgQezfvx+hoaFYsGAB6tati65du5q00bRpU/Ts2RNz5szBtWvXcOPGDcyfPx/Z2dlwcnKq0vMtjGgQob92B7kBP0GzZlveQizVMakIACKgcKu+K0NnO0sdQfmpdcC+O7kYc0CFuX+m41KcZQz77dGjBzw8PLBw4UKEhobi+PHj2L59Oz7//HNoNBrj9mPHjiEgIACjR4+GIAjGJGRQUBByc3Nha2uLtLQ03Lt3DzqdDhMmTMDatWtx4sQJhIaG4oMPPkBubi4GDBgg8RlTWbHHIhEREREREZXL888/jwsXLuCtt96CIAho06YN5s2bh4CAAGg0Gnh5ecHT0xMjRoyAp6cnPvvsM3Ts2NF4/ODBg7F9+3YsWrQInTp1wldffVXo0MsVK1Zg6dKlmDBhAqysrNCzZ0+8//77VXmqBRj0GsQG/4r7t36B74XWkBdczLpasnJMgzbOTeowyiXJwQInVywjEcC/MVrcSdHC1fUC/q9FKzxdryFkgjRdMa2srLBhwwZ89NFHeOGFF+Dh4YG5c+eiX79+qFu3LpYtW4Zhw4bBzc0Nr7zyCqZMmQIA8PHxQffu3TFq1CisXr0aTzzxBLy9vTF48GD89NNPmDRpEjIzM/HBBx8gMzMT/v7+2LZtG9zcqud773HGxCIRERERERGVi1wux+LFi7F48WKT7RMmTMDevXshk8kwb948zJs3r9Dj69ati+XLlxfYXr9+fQQHBxtfu7m5YfXq1eYNvpx0mixE39yDyOs/QpOdBABI8muK2pctZ0h2RciVaVKHUG4x1tU/sZivnksKLqqS8d75U2jo4Ij/a+6LAd6NoZBV/TDppk2b4rvvviuw3dfXt8iVmpVKJb755huTbXv37jV5PWvWLMyaNavQ4x/+/w8Aw4cPx/Dhw8sSNlURJhaJiIiIiIiISqDJUSHy2k+IvrETOo3pcOFoXEQtoQeEGpDXkokqqUMot1Ary1wRuqzsFcCNzKvG15GZGfj4yjl8desaxjRvheGNm8OmiEVViKoa34lERERERERERdDrchF1/SfcC9wKnSaz0DLqrBik+OrhfsNyFt0oL0FTfXssBqH41Yiri2bumbiYnltge2KOGl9cv4yf7t7C663aYlCjJpALXDqDpMXEIhEREREREZldSUMXt23bVoXRlJ0oGnD/zq8IvbgRuZnxJZaPsb8Od7Sv/MAqW0aq1BGUjxIIE6t/d0WFDAjLuV5smcQcNT6+cg7bQ29jeuv26OFVv4qiIyqIiUUiIiIiIiKihyRFnkbIubXITAkp9THpacFIb9oeTqGVGFhVyMyCYK2BmKuUOpIy0btLs7iJubX0yMXlzNL1Gg1LT8M7Z/5CB49aeNOvA3zd3Cs5OqKCmFgkIiIiIiIiApCeeAsh59YiJeZ8uY6PqRUCp9BmZo6q6incMqC5X72SVFnOUkdQcQKABP2tMh93OSkBk04eQd96DTHNrz3q2TuaPziiIjCxSERERERERI81dUYsQs+vR1zI76jI6h/JKZehrtMctnHVe0iu3CENQPVKLCbZV+9rDgA+7npcV8eV61gRwLGYSPx1PxovNG6OV1v6wcW6ZqxUTpaNiUUiIiIiIiJ6LGlz0xF++WtE39gFg15jhhpFxDaORdM4LzPUJR25svot4BJtU/0Ti7nyio+j1xoM2BkajN8iwjC+hS9GNW8JGzlTP1R5uHwQERERERERPXZigw/g3+3DEHntRzMlFfPEp52Fxrl6z/cnE6tfYjHMqnqvCO3tDNzJDDNbfVk6Lb68eRWjjv6Kf+NizFYv0aOYWCQiIiIiIqLHRpYqApcOTsbNk4uhzTF/As2g1yCuZYrZ661KgkYldQhlFiRW78SinW10pdR7PzsLs/49iQXn/kFyjrpS2qDHGxOLREREREREVOMZ9BqEXtyEc7tHITX2UqW2FZt9BvrqPL1deqrUEZSNDRAuVt+h0LXsgBvpNyq1jWMxkXjp6EHsCbsDsRpfK7I8TCwSERERERFRjaa6fwVnd49C+KXNZh32XBSdJgMJratx77CsbAg2lX+dzEXvVr2HntdxToK+AosGlVamVosVgRfw+l9/IDy9+g13J8vExCIRERERERHVSHqtGsGnP8PFg5ORrYqo0rZjDOcgVuNP3ArXDKlDKLVMJ6kjKD8nJRCUcbVK27yekoTxJw5ha3AQ9KKhStummqca/5ojIiIiIiIiKlxK7EWc3f0yooK2AxIkT3Ky45HcWlfl7ZqL3EEldQilluRQfYf2NnbPQK5BW+XtagwGfHnjKib9+TtC0qrZ0HeyKEwsEhERERERUY2h02bj9j/LcfngG1CnS7sabrRNoKTtV4RcmS51CKUWY109E4tKOXA3+5qkMdxWpeCVE0fw9a1r0BnYe5HKjolFIiIiIiIiqhHSk27j3O4xiL65G6iCOetKkpkeirTmUkdRPjJRJXUIpXbXqnquCN3SIwdpWumHnOtEA766dR2T//oDsVmZUodD1QwTi0RERERERFTtRQXtxMX9E6FOj5I6FBMx7sFSh1AuQq5K6hBKLQjVL7EoE4BYbeWuBF1WN1KTMf7EYfwVa1n/h8iyMbFIRERERERE1ZZOk4lrR+ch+PSnVbLic1mlpAYiu141XLU4QyV1BKVjA0QapO+dWlYt3XW4n5ModRgFZGg1mHv2b6y+ehFaQ/VL2FLVY2KRiIiIiIiIqqX0xFs4t2csEsKOSR1KsWIaRkodQtllZUOwsbxE7aN0btUwaQsgS7grdQjF2hEajMl/HeXQaCoRE4tERERERERU7UQF7cDFXyZBnR4tdSglSlCdR65b9fv4rXCz/AVcMp2ljqDsmriICMmKkDqMEt38b2j0yRgOjaaiVb/fbERERERERPTY0uVm4NofcxF8eoVFDn0ujGjQIa6F5Q17LYncIU3qEEqUZF/9hkErbKpPD9YMrQbzzv2NVRwaTUVgYpGIiIiIiIiqhfTEWzi3dywSwo9LHUqZxWaegc5W6ijKRq6w/MRitE31Six62QM3025LHUaZ7QwNxusnuWo0FcTEIhEREREREVm8qKAduLB/ItTpMVKHUi56bRYS/LKkDqNMZKJK6hBKdFdWvXrReTglwCBUr2RovluqFIw7cYhDo8kEE4tERERERERksQwGHW79vQzBp1dANGilDqdCYrRnYZBLHUXpCbkqqUMoURCqT2LR2RoISr8udRgVkqnV4t1zf+Pb20FSh0IWgolFIiIiIiIiskja3AwEHnoTMbf2Sh2KWeSqk5DkV42So+kqqSMoni0QLVaf3n+N3NKgEavRz78IIoCNN69i8cV/Oe8iMbFIRERERERElic7LQoX9r+ClJjzUodiVjGKy1KHUHrZagh2uVJHUSSdmyB1CKVmYwUEZ12VOgyzOhQZjhn/HEdaruW+R6jyMbFIRERERERUyXx8fHDu3LlKb6dPnz7Yu7f69+5Lvn8FF/a/gmxVhNShmF1Wxj2ofKpPLzuFa7rUIRQpw1nqCErPx0ONDF221GGYXWByIiadPIKI9FSpQ0F0dDR8fHwQHR1dYtkzZ84gNDS0CqKq+ZhYJCIiIiIiIotxKuZ3fHhjMdTVdIGL0oh2uSl1CKVmZW+5K0Mn2VWP94hcACJzq/fcisUxiHosOzcZN5OvSBqHl5cXTp06BS8vrxLLTpgwAUlJSVUQVc3HxCIRERERERFZhF9Cf8DqS+8hOjsSx+t7wiBXSh1SpVCpgpDZsHoM45UpLDexGG1tkDqEUmnpoUVCborUYVQKZ6USrrJdSFSHYvGZaTgTe1yyWORyOTw9PSGXV6MVkmoAJhaJiIiIiOixlT907uTJk+jTpw/8/f2xdOlS3LlzB8OHD0f79u0xZcoUZGZm4t1338W7775rcvzDQ5zPnDmDoUOHok2bNujbty+2b99eaJvHjh1D27Zt8c8//5Sp/Xzbt283lh03bhyCg4MLbcdgMODrr79G37590bZt2wJlfXx88Msvv2DQoEHw8/PDmDFjEBUVVdFLWi6iKOKboJXYemM1ROT1QgvJDMH5Zj6oHn3Syi623j2pQygVmWi5icW7VtUjsZgmFv5/tLpTymRoYvcPUnPyzk9jyMXKi/NwMOwnSeJ5eCj0o9NP7N27F3369AEA47/jx49HQEAAAODixYsYPnw42rZti8GDB+P333+v+hOopphYJCIiIiKix97mzZuxYcMGLFmyBNu2bcOMGTPwzjvvYMuWLQgMDMTu3buLPV6v12PmzJl47rnncPjwYbz99ttYvHgxQkJCTMpdvnwZ//vf//DJJ5+gZ8+eZW7/xIkTWLduHT744APs27cPHTt2xPjx45GWVjD5s379enzzzTd47733sG/fPtSrVw+vvfYasrMfzPMWEBCABQsWYO/evUhNTcXnn39egatYPlq9BqsuvVtoMuJS+jXcbtGhymOqCompF5DrYfkfyYUcldQhFOm6aPkrEjd3MyA8u+Q5/6obAUAH11DEZ54y2W6A4b8vCdZAtNAVu/N/nwYEBGDSpElITEzElClTMHz4cBw8eBCvvfYa3n33XVy8eFHiSKsHy/8tRkREREREVMmmTZuGli1bYtCgQXB3d8fAgQPRvXt3dOzYEd26dUNYWFixx2dkZEClUsHDwwP169fHkCFD8O2338LT09NYJiwsDFOnTsW8efMwYMCAcrX/9ddfY8qUKXj66afRqFEjzJw5E/Xq1cOBAwdM6hNFET/88APefvtt9O3bF02bNsWSJUsgl8tNyk6cOBHdunVDixYtMHr0aAQFBVX0UpZJrj4HS8+9hdOxR4sscyLjEmKatK+6oKqIKOoR2yxe6jBKZgGLchTKDoi10MTVw0RFzVuACACe8MxAZFrRC0X9EroNAYEfwiBaXq9SNzc3AICzszPs7e3x448/4sknn8T//d//wdvbG0OHDsXLL7+M7777TuJIqwcrqQMgIiIiIiKSWoMGDYzPbWxsUK9ePZPXGo2m2ONdXFwwevRovP/++9iwYQOefvppvPjii3B2frBs7bJly6DT6QpdWKC07YeGhuKzzz7D6tWrjftzc3Nx7949k/qSk5OhUqnQrl074zaFQgE/Pz+TlVC9vb2Nzx0cHKDVaos9T3PK1efg43MzcS3pfIllD+Zcw8v1WsE15lYVRFZ14jLOoL7DUCgyLThBps6BYJcDMdtG6khMaN0sf47Keo7A7fTgvO59NUhnDxkiUjeXWO7PqIMQAExvvwgywXL7tYWFheHPP/+Ev7+/cZtWq0Xjxo0ljKr6YGKRiIiIiIgee49O9i+TFfwQLAiCydA+nU5nsv/DDz/E2LFjcezYMRw7dgw7duzAhg0b0KtXLwDAqFGjoFAosHTpUnTr1g1K5YOFSUrTPpA35Pq9995Dt27dTLY7ODiYvLa2ti7yeIPhQQ8ihUJRaLnKVpakIgDoRT32ye5hlEdD2CVFVnJ0VUevUyPeNx31zztKHUqxFK7p0FhYYjHTSeoISuZiH4d76VJHYV6tXW0Rq/qk1OVPRB0EIH1yUa8veti8TqfD4MGD8cYbb5hst7Jiyqw0LDdlTEREREREZEEUCgWysrKMrx9e6CQxMRGLFy+Gt7c3pk6dij179uCJJ57AiRMnjGWeeeYZTJ8+HWq1Gps3l9zbpzCNGzdGXFwcvL29jY+NGzciMDDQpJyjoyM8PDxMtmu1Wty4cUPyXjhlTSrmU+uz8YtDFjQO7pUUmTRic8/CYOH5Cyt7y1vAJdHegnt5AnCzAW5kXJc6DLNq5GiLrKy1EKErufBDTkQdxPrAxVU6LLq439ePaty4MSIiIkx+rx4/fhwHDx6silCrPSYWiYiIiIiISqFNmzY4ffo0zpw5gzt37uCjjz4y9vhzdnbG0aNH8fHHHyMyMhIXLlzA7du34evra1KHg4MDZs+eja+++grR0WVf0GHixIn47rvvsH//fkRGRuKzzz7D4cOH0bRp0wJlJ0yYgLVr1+LEiRMIDQ3FBx98gNzc3ALzO1al8iYV86XkJuH3OvbQK+3MHJl0NDkpSGqTK3UYxZIrLC+xGKW0vLn7HtbANRXaarC4TGl52ljDWv89NIbydcGs6uRimzZt8MMPP+DevXs4fvw49u41nQ/Szs4Od+/eRUZGBsaMGYOgoCCsWbMG9+7dw8GDB7F69WrUrVu3SmKt7phYJCIiIiIiKoWhQ4fi2WefxbRp0/Daa69h0KBBqFWrFgBAqVRiw4YNuH37NoYMGYKZM2dixIgRGDlyZIF6XnjhBbRo0QJLly4tcwwDBgzArFmzsHbtWgwaNAhnzpzBl19+iUaNGhUoO2nSJIwcORIffPABhg8fjri4OGzbts24cEFVq2hSMV9kVgRONfKGWMRw8eooWnYRomC5PfAE0fISi3esLDexaGcF3Mq6KnUYZmNvZQUv5WFkaCo2DUFVJhc/+OADqFQqDBo0CF9//TXeeustk/3jxo3DihUrEBAQgHr16mHjxo34559/MGjQIHz++ed49913MWTIkEqPsyaw8A7XRERERERElad+/foIDg422fbw8GUA+OSTB/OJLV++HMuXLze+fvHFF43P27Zti+3btxfazsNtCIKAXbt2FbqvpPYBYPz48Rg/fnyh7Tx8rFwux6xZszBr1qwSYwKA4cOHY/jw4YWWrShzJRXzBWXchHNzf7QPDjRLfVLLzoxGaisD3G7KSy4sAUGtkjqEAq5bcG/A5h7ZuJieI3UYZiEXBPg6BSIm/YpZ6qvMORfz51FUKBTw9fUt0EvxpZdeMj6fPXs2Zs+ebXz95JNPFihPpVNzvuIhIiIiIiIii2PupGK+0+lXENasg1nrlFKM4w2pQyhahkrqCEzZAfGiZfbwtJIB93KuSR2G2XRxT0BM+u9mrbMyei7Gx8fj1KlTUCgUcHV1NVu9VDImFomIiIiIiKhSVFZSMd+RrMtIbNimUuquammqW8iQdl2doqlzILO3nB54WndB6hCK1NJDgySNSuowzKKrhw4Rqu8rpW5zJxe3bduG1atXY9q0aVAqlWapk0rHohOL9+/fx5QpU9ChQwf06dMHW7duNe67efMmRo4ciXbt2uHFF19EUFCQdIESERERERGRCZ1Bi0/Oz660pCIAiBCxX38b6XUKLl5THcXUCZM6hCIpXMu3aEdlyHCSOoKiJRtuSx2CWfi7KRGVurZS2zgRdRAbry0zS11z5szBpUuXMG3aNLPUR6Vn0YnFmTNnws7ODnv37sV7772Hzz//HEePHkV2djYmT56MTp06Ye/evfD398eUKVOQnZ0tdchERERERESPPVEUsS5wMQITz1Z6WxqDBvuVCchxrlPpbVW2pNRLyKltmb3x5PaWs4BLop1lDoP2cTcgMjtW6jAqrIWzLZIz1gBVsKDQ0Yh92BG8qdLbocpjsYnFtLQ0BAYGYurUqWjUqBH69euHnj174syZMzh06BCsra0xd+5cNG3aFAsWLIC9vT2OHDkiddhERERERESPvR9urcNf0YeqrL0MbTp+dQd0thbcla00RANim9yXOopCya0sJ7EYaW2ZK0Lr5Jbb47S06trZQp+7GTqx6oa+bw/ehGMR+6usPTIvi00s2tjYwNbWFnv37oVWq0VYWBguX76MVq1a4erVq+jYsSMEIe+bHEEQ0KFDBwQGBkobNBERERER0WPucPhO7A35tsrbjVffx4n6nhDliipv25zi0s5C62R5H9UFg0rqEIzuyiwvsdjQCbidGSJ1GBXirFTCVbYLal1Clbe98drHuBR/usrbpYqzvN9W/7G2tsbChQuxY8cOtGvXDs8//zyeeuopjBw5EomJiahVq5ZJeXd3d8TFxZW5HUEo/FERRdVZ0kOKNivSrhRtVqRdKdrk9eW5WsK58vry+krd5uN0fR+nc5Wizcq8vkRkHufu/4mvr6+QrP27mSE416wlLHOgbOkY9LmIa5kqdRgFCDkqqUMwug691CEU4GBfvYdAK2UyNLU7hdScYEna14s6rLw4FyGqm5K0T+VnJXUAxQkNDcXTTz+NiRMn4u7du1iyZAm6desGtVpdYJUfpVIJjUZTpvqVSrk5wzVSKCqnXrYpXbuPS5tStctzrXltStXu49KmVO0+Lm1K1S7Ptea1SfS4uZMahNWXF8AAaXuTXUq/BucWHdDqzhVJ46iI2JwzqGv9POS5FpQiTVdJHUEeeyBBtKDrAsDDFghKq74LygoA/F3DEJX2j6Rx5OjVWHr2Lax46nvUsqsraSxUehabWDxz5gx2796Nv/76CzY2NmjTpg3i4+Px5ZdfokGDBgWSiBqNBjY2NmVqQ6PRF/kNtXV5Aweg1Zbv2xMp2qxIu1K0WZF2eX0rt93qdn0r0m51O1de38ptl9e3ctutbte3Iu1Wt3N9nK4vEZUsMfs+Pjk/Gxp91c3LVpwTGZfh1MQf9cICpQ6lXLS5aUhsrUady2X7jFupcnIhc1DDkGkraRhaN8vrZl7XJQX30yxveHZpdfXMRGTqHqnDAACkaVKw7NxMfNLzW9ha2UsdDpWCxQ6FDgoKgre3t0my0NfXF7GxsahduzaSkpJMyiclJRUYHl0aolj4oyKKqrOkhxRtVqRdKdqsSLtStMnry3O1hHPl9eX1lbrNx+n6Pk7nKkWblXl9iaj81LpsfHx+JlJzk0ouXIUO5lxDar1WUodRbtE4D1FmWb+gFC7pUoeAdGepIzDloABuZl6VOoxy6+whQ2SqZa3KHJkRgpUX34Ve5JeC1YHFJhZr1aqFiIgIk56JYWFhqF+/Ptq1a4crV65A/O8uUBRFXL58Ge3atZMqXCIiIiIioseOQTRg9aX5uJd+V+pQCtCLeuwT7iHbo6HUoZRLTtZ9pPhaVi84uX3BxOLca2/h41uLijxm4oWX8dTJDiaPsP8WOfkr8TiG/dsfL555HqeT/jI5bsql8biTcbtAfQl2lpVsbeqRCbU+V+owyqW1qy1iVWukDqNQlxNO49ugVVKHQaVgsYnFPn36QKFQ4P3330d4eDhOnDiBjRs3Yty4cXjuueeQnp6OZcuWISQkBMuWLYNarcbzzz8vddhERERERESPja03VuNivLTzshVHrc/GLw5Z0Di4Sx1KuUTbWVZPOLmVyuT18fjfcTblVJHl9aIeUepIrG3/FfZ1+8P4aGjXCHpRj5XByzCt6UxMbjwdn9xebOw8dCb5FNyVHmjh2LJAnVFKy0m2KmRAaPY1qcMol0aOtsjKWgsROqlDKdJv4dtxKHyH1GFQCSw2sejo6IitW7ciMTERI0aMwPLlyzF16lS8/PLLcHBwwKZNm3Dp0iUMHz4cV69exebNm2FnZyd12ERERERERI+Fk1G/4WDYT1KHUaKU3CT8XsceemX1+7yYkXYXaU2ljuIBmSHN+Dxdm4YNYZ+jpWPrIsvfV8dAZ9CilaMf3K09jA8rmRXStCqk6VR42vMZPF2rP9J0Kqi0qQCA7+5txoRGkwut847ccobHtvTMRapW+uHhZeVhYw1r/ffQGCw/9i1BK3Ez+bLUYVAxLHbxFgBo1qwZvv3220L3tW3bFvv27aviiIiIiIiIiCg6Ixybrn0sdRilFpkVgVONfPFUSDAEg+X0eCuNWM+7cA5tLnUYeXJSjU83hK7Bs7UHIKmYuTXvZYejlnVtWMsLLr3lrHCBjcwGdzJuwQARtjJbOCmccS75NFyV7oX2VgSAa6Jl/PwEAPG6m1KHUWb2VlaoqzyMpOxIqUMpFYOox+pL72F1r5/hZO0qdThUCItOLBIREREREZFlydXnYOXFecjRq6UOpUyCMm7Cubk/2gcHSh1KmSSnXoHaqwVs7xc/t+CM87/D1doWS9o9Vej+kX/vw52MFJNtu596Ac0d3XDs/j0sv/Ev5IIM7/k9id61H8xLOfb0Abzv9yRaOXsAaSoAwKXU87iquoytnXdi1Z3lRcYUkR0OK5kC8669heCMW2hg542pTWfC18kPckGOKU3ewozA1yCDgLebz4VckGNrxFeY1fzdwit0AJIfWnEr7ed3INi5wmno+4UWT9k0HvqEEJNtrlO2wapWU+TeOonMI6sAmRwOz8+BdYsexjKpW16Dw4D/QeHlU+S5tfTQ41p2fJH7LZFcEODrdBUx6VekDqVMknMS8MWVhXi/61oIguWtCv64Y2KRiIiIiIiISu3r658hIiOk5IIW6HT6FTg164gmIdVpaKWImEYxaHa/bpElDseG4p/EaAypX3jPRr1oQERWGr55YgC87R8sq+yitIFeNGDJ9VOY2/oJGEQRi67+jZPPjIUgCPgnIQqe1rZ5SUUAyNVAZ5uKleeWYVaLd2Ettyk28sjscGTo0jHI6wVMajwVv97fh1lX38D3nXejtk0dvFh/FJ6vMxiAADsrO5xPOQMXhSsa2Hlj0Y15uJF+Hb09+2J609kQBAEatwdJpZygo9CEnIF12wGFXzWDHvqUSDiPXw8r9weJUsHOGaJBj4zfPoXDszMB0YCMA8ugfOcQBEFA7t1/IXNwLzapCABqWfX7P9DFPQERqiNSh1EulxNOY1/IdxjefILUodAjLHaORSIiIiIiIrIsf0UfwrHI6j0l1ZGsy0hs2EbqMMokXnUWGpfCe2qlaXKx5tYFtM5P/hUiJjsTWoMBfi6e8LCxMz6sZDKoNLlQaXPR36sxnvVqApU2FymaHADAprtXMKW5v0ldWyM3oqWjL7q4PVli3P9r8QG2dz2Anp5Pw8exFWY3nw8vm7r4I/43Yxk7K3vYWeXNf/ndva8wodFk7I3ZAZ2ow49d9uJa2hX8lXQCAJDulHeMQZ2OrOPrYVW3VZFtG1T3Ab0Oinq+kDm4Gx+CzApidhpEdRqsffvA2rcvRHUaxOy8Yd7Z/3wLu6cmFXtejV1E3M0ML/H8LUlXDx0iVN9LHUaF/HR7A24lB0odBj2CiUUiIiIiIiIqUUzmPWy8Wn3mVSyKCBH79beRUbuZ1KGUmmjQIs4npdB9q26dw6B6zdDUsej558IyU1HH1h7W8oKDFl2U1rCRW+FWWjJupiXBVm4FF6U1TidEw/3h3or/ORZ5Ev8kncSzf3fHs393x9GEwziacBjP/t29QN1WMivYWzkYXwuCAG+7xkjMTShQ9mLKOTgqnODj2ArX0wLR2fUJWMtt4O/SGdfT8obuJtrlDYPOOhoA6zbPQe7RuMhz1iWGQ+ZUC4JVwfkdBTtnQGED3f1g6O4HAwpbCLbO0ISchczetcTeitY20cXutzTt3ZWISl0rdRgVphd1WHVpPtI1KqlDoYcwsUhERERERETF0uhz/5tXMVvqUMxCY9Bgn3UCcpzrSB1KqcVmn4H+kZHH55JicTklDpObty/22LBMFaxkMsy48Af6HPsJk878huuqRACAXJBhZsvOmHjmV7x27hDm+HaFXJBh090reOOR3ooA8NWwmdjaaQe2dPoZWzr9jO7uT6G7+1PY0unnAmXfDpyMb+9tMr42iAaEZt6Ft13BhODWiM2Y+N9K0DLIYEDeIi16UQ/xv3kVI60N0IRfhCYyEPY9JxZ7zvqkexDkCqRtn4Ok1YOg+m4atDF5i60IMjkc+k6D6rupUG2bAYf+b0KQyZH1zzewe+rVYuutbQ/cSKs+i7Y0d7JFSvoaQCh+js7qIjknHmsvLzS+J0h6TCwSERERERFRsb4OWoF76XelDsOsMrRp+M1dgM7WUepQSkWnyUCC34PEbq5eh6XXT2O+35OwKaQn4sPCM9OQodVgeIMWWN+5P5o4uGDy2cOIU2cCAEY38sXf/f8Pfz/zfxjRsCX+TYyGq7UNvO2d8b/LJ/Ds8e347OZZiKKI+g5WqG/X0Piwk9vDTm6P+nYNoRf1SM5NgtagBQA86f4UdkX9iFNJfyEy+x4+v/spMnUZ/82r+MCl1PNwsHKAj6MvAKClky9OJhxDeFYo/k3+G62d2wIAbhqykXloBRyffweComBPRJPrlRwBQ04GbNoPhvPoVZB7NkbaD29Bn5a34Ipt5xFwn3MEHnMOw7bDMGhCz0Fm5wIr94ZI3/0+kr94AZl/fFEggVXLKRGGapKkq2tnC1H7FXRijtShmNWlhFPYH/Kd1GHQf5hYJCIiIiIioiL9HX0YRyOq97yKRYlTx+JE/VoQ5QqpQymVGP15iP99it949wp8XTzQ3bN+icctatMDv/YeiT51GqGVswcW+D2JenYO+DXmwQIk9lZK2FnlXYdNdwPxRnN/bI+4CZ3BgF96j8CVlHgcj7sHqFVFtpOQE48XzvRHUNpVAMBL9cdidMNX8MXdTzHpwiiEZ4VidbsvYWdlb3Lcd/e+wgTvKcbXL9YbBRu5LaZdngh/l4542vMZAMDJP7+ClVdLKJs+UeI5Ow56F24zdsG6ZS8ovHzg8PwcyFy8kHP9weIlMmt7CMq8+R2z/vkWdk+9CvWF3RANerhN2w5t5FVobp80lndSAkEZ10ps2xI4K5Vwle1CtrZ6rVxdWj/e3oDbKVelDoPAVaGJiIiIiIioCDGZEfjy6jKpw6hUdzND4NSsLboGX0fhy6NYjpzseCT56eB5zQpHYsOQnKvGE0fyem5pDXoAwNH74Tj73Csmx1nJZHCQKY2vBUFAYwcXJOQUHNp+NikGzgprtHL2wJd3rqBHrfqwkVuhs7sXrqTGo19aqkn591otNj73sq2Lv3s/WHFbEASM834V47yLH1681v8rk9f2Vg5Y0faROQEdgbR/jsGQmYzET/rmbdNrAACJt/6E57vHTYoLMisI1g9SHoIgwMrdG4aMxALta8IuQGbjBIWXD7L/2gJlsycgKKyhaNQR2qhrsG71NACgsXs6LqZpiz0XS6CUydDU7hTiMoOlDqXS5M+3uLrXz3BUOpd8AFUaJhaJiIiIiIioAJ1Bi1UX360x8yoW51L6NTi36IBWd65IHUqJYqyvwBOdseWJAdA9NEz389sXAAAzW3YucMyrZw6hs3sdvNGiAwDAIIq4k56Cl70Lrqq86e4V/M83r0egTMgrCwB6UYQoAtBoIXfMhj7DztynViyNmwCXceuA/xKoAJB5fAMAwKHvtALlVd/PgMLbH/a98pKaomiALiEEtp1eLFA2+59vYN//7bwXggDkX1dRb3xuLQfuZll+b0UBgL9rGKLS/pE6lEqXpI5DQOCHeK/LGqlDeaxxKDQREREREREVsPvOFoSn19weT486kXEZsU3aSx1GiTLTw5DWQkRdO0c0tHcyPuytFLC3UqChvRP0ogFJOdnGXoy9ajfAD+E3cDI+AvcyVVge9C8ydBoMbdDcpO5zSbFwsFLC97+VoFs7e+Lo/XCEZKTir4RItHWtBQCwck2r2pMGkO4EyF28IHerb3wISjsISjvI3epDNOhhyEyGqM/rUahs0R3qczuQG/wPdEkRyDy8CoacTFi3G2BSryb8IgRrByi8WuadW91WyL11ArqEMGjunIZVfT8AgI9HDtJ0mVV70uXwhGcmotL2SB1GlbkQ9xf+ij4kdRiPNSYWiYiIiIiIyER42h3sufuN1GFUuQM515Bar6XUYZQo2u12sfvj1Fnoe/xnBKYmAADGNfbDhKZt8MmNMxj5z36EZqqwqetzsLdSmhy3+ZGVoEc38oWt3Aqv/HsQnd280N8rbzVnuW26mc+oZPG2xS+YYkhPQPKawdBGXQcA2HYdBdsnxyLz99VI3fwK9InhcPm/LyCzNp3fMfufb2H31CTja9suIyEobKDaOgUKb39Y+/aBTABitDfMf1Jm1slDhojUTSUXrGG+vv4ZVDnJUofx2OJQaCIiIiIiIjLSG3RYF/ghdKJO6lCqnF7UY58sAqPcG8IuOVLqcIqUmnoNWfV9YR/9INm2pN1Txuf17BxxdeCDeQ0FQcBrzdrjtWbti613S7eBJq8dFEqs6/JsgXJyq6rvsRhhbSiwzWno+8bnchcveH7wr/G1IAiw7/EK7Hu8UuC4h7mMX2/yWmZtD+fRq0y2tfTQ4WpWwbkZLUlrV1vcV30idRiSyNSmYdP15ZjXeaXUoTyW2GORiIiIiIiIjPaGbEVYWvE94moytT4bvzhmQePgLnUoxYptIF3iU2ZQVXmbd2T6kgtVkgzckazt0mjkYIusrLUQ8fh9GZDv7P0TOB3zh9RhPJbYY5GIiIiIiIgAAJEZUdh556uSC9ZwKblJ+L2ONwZEqiHXlG/xmg371HCwFTD+OZtC93/8fTZikkx74S0Yb4u6HnIE3tVhx4lcyARgVF9rtGn64KP7ip+yMbqfNQThHBq6NYR1SvFDhCuFWlXlTV4XpUksNnUVcTsrL4mbufUnCPb2sB85tNCy6V9sgiEu3mSb49tvQF6nFjRBt6A+cBiQyWA39HkoWvkYy2Ss/xq2LwyEVV2vMsfnYWMNa8P3yDBU/fB0S/PV9RVo794Z9jauUofyWGFikYiIiIiIiCCKIpZcugl3hznIUG9Ftva+1CFJKjIrAqca+eKpu8EQxILDcItz8bYWN8L16Opb+Edug0FEQqoBM1+yRS1XwbjdwVaAwSDip6M5GPG0NUQR2PZ7Dj6dag9BEBAUpoOzvYAGteQQRT3ut0hCo7MS9KxMU1Vte06ASoL8KQDIlRFALqC5GgRdcAgUHdoVWk40GGBISobD5Fcg83jwMxHs7CAaDFDv+xW2g54DRBHZuw/A6f05EAQB2tt3ITg5liupaGdlhXrK35GYbbnD9qtKbZs66JfjgvB/18Cvz0dSh/NY4VBoIiIiIiIiwr7wEFxLScKVFA0SdK+gocsoqUOSXFDGTVxt0bZMx2SpRez7WwPv2kV/3E5KE6EzAI3qyOBs/+AhlwnIVIvIygE6tLBChxZWyMoBMtV5WbXDZzUY0O3Bgiv3M/+FzlYoqpnKo9FC7pxVZc3lukpwjgDqOgA302/DkK2G+vAxyOvXLbKsIUUF6PWQ168HmaOD8SHIZRCzsiFmq6Fo4wtFW1+I2WqIWXk9YXNO/A2bvk8VWW9R5IIAP6drSMy+VN7TqxFkghxPO3bE8KhUuNwPRtzd35ASfV7qsB4rTCwSERERERE95pJz1NhwI9D4Ol2rxdmkenBxeB/O1k2lC8wCnE6/gvBmHUpdfu/fuejia4U67kV/3I5LMcDVUYDCqmDCzMFWgNIKiIo3ICrBAKUCsLcRcCNcB8f/eivm02uzEe+XUbYTMhMr56obepvuVGVNmXB3jIcoADmHjkLp3wayWp5FljUkJEJwdoKgKNhLVbC3AxQK6GPvQx9zH1AqINjZQhscAsHBvly9Fbt4JCA6/XCZj6tJGtp7Y7ymIXzvXIZMpzFuv31qOfS6XAkje7wwsUhERERERPSYW33tEjK0mgLbb6rUuJfzIrxdX4MgPr4fHw9nXUaid5sSywVH6hASrcfzXZXFlotLNsBKBny5T413N2ZhzY5s3LufN4egTCZgaE8lVu9U44tdarzYyxoymYDDZzUY2K1gvTHaszDIC2yudHK7qlsZOt6u6sdBu9oA19OvQRsaDl14BGz6FN+rUJ+YBEEuR+bWn5G2bBUyNm+FLioGACDIZLB9ri8yN21F5lffw3ZgfwgyWV5vxX69yhxbV08dIlK/L9d51QRKmRLPO3TEoLBo2CfdK7A/Oy0S965sqfrAHlOcY5GIiIiIiOgxdiYuFseiI4rcr9brcSbRGU2dFsAevyIp+0oVRmcZRIjYr7uNUbWbwTE+pNAyWp2In4/l4uW+1lAqih+6G59qQHauiCfbKDDwSQGnr2uxdrcaH0ywg6ujDL39lXiitQIAYKMUcOueDg52Amq5yvD1rzm4d18P/+ZWGN5LCY06GcltNPAMLD6ZaW5yedUlFiMUZZvj0hwauqoQn5QL9b7fYDv0eQgKRbHlDYlJEHNyoOzsD9kzvaG5cBmZX2+D06ypkLk4w/rJLlB2bAdAgGCthPZOKAR7O8g93JH1027oIqOh9GsFm4H9IQhFv3/auysRlbIakGZ0uOR8HFqgZ2ImrGMuF1vuXuD3qNPsedi7Nq6iyB5fj+9XTkRERERERI85rUGPlVcvlKpsaHo2bmb0QwPXNyEXrCs5MsujMWiwzzoBOc51Ct1/6IwGDWvL4duo5P47Y56xxoeT7NGumRUa1pZjVF9ruDvLcO6mzljGRinARpmXPTp0VoMBTyjx1xUtDAYRCyfYISRGj8C7eb0co60uQRSqtlefTK+qsraC5VW7IrStFRCcdQ05x/+CvJ4XFC2alXzMC4PhNOdNKFu3hFU9L9gOHQCZmws0V64ZywjW1hCs8xLAeXMr9kLumQuAwQCnd6ZDdy8K2hu3i2yjuZMtUtLXAFX8s7YEdlYOGGbrj75378JaVfLCUqJBi9unPqmCyIg9FomIiIiIiB5Tu8PuIjors9TldaIB5xKVqG//P3gp/sb9zL8rMTrLk6FNw2/u9hiqcYSV2nRuw0vBOqRniZgVkHc9df/lwq7czcSaNx1MysplAmwfys0KgoDabjKkZRZMGN2O0MHeRkDD2nIcOqOBb2MrKBUCfBrIERqrh38LK2RlRELVsitcb1VhNza1qmraEYDrhqpNLLbwyMbF9Gxor92AISMTqkXL83b890NVBd2Ey+L5pmHKZYD8wQ9VEATIPT1gSC84B6Y2JAyCnS2s6nkh5/hfULRoBkGhgFXTRtDfiwT8WhU4xsvOFqL2K+jEHDOeafXQ3skPXaLvQ5EVWKbjUmMvIiniH3h496ycwAgAE4tERERERESPpUytBt/eDirXsdFZasSgMzp6dEFyxtfIrcLea1KLU8fiRP3meCYsB4Jea9z+9ku20D+U//rln7zFI4b2LNi78/OdajRvIDfOmWgQRcQm6vFU+4LDbQ+d1WBE77w6BAEQxbzko14E8FAeMsbpJlzhV9HTK720tLyec2IlJzOdgKpbJgaQC0Bkbt7/C4fXx0PUPxiGnXPkGADA5rl+BY7L/Oo7WDVuZJwzUTSI0MfFw/qJzgXK5hz/G7aD+ue9yPuh5j03GIxPH+asVMJNtgepOfEVObVqx0XphmfFevAILt/vKQC4ez4A7g27QxA4YLey8MoSEREREVG1d/jwYSQnJ0sdBgCgT58+2Lt3r9RhlOi74BtI05R/5VQRwMUkEZnCNNR3HmS+wKqBu5l3cb5Zy4fzenB3kqGW64OHtVKAtTJvXkSDQURalgE6fd4RbZrK8edlDa6F6hCfYsDOE7nIzoVxXsV8wZE62Frn9VYEAO86cly5o0Nskh5BoTo0rvvgI71KdQOZ3lXYY1Grhdwpu9KbyXWt2skEW3pqkZCbAgCQubpA7uFmfMDaGrC2htzDDaLBAENGJsT/ejFatWyBnNNnob0ZDH1iEtQHDkNU5/w3r+ID2tBwCDbWsKpXN++4+nWhuX4T+vgEaG/dgZV3fZPySpkMzexOIzXnVhWcveXo5uSPUXFaeESWP6kIAFkpoYgNPmimqKpGdfkbko+JRSIiIiIiqtZiYmIwc+ZMqNVqqUOpNuKzs7EjNNgsdSXm5OJ8sg9qOc2HncLLLHVWBxfTr+F2C/9SlU3NEPHepmyExeYlofp0UKBfJyV2nsjFx9uycT/JgLdG2BjnVMx3+L+5FfP1aq+AUiFg1fa8Ho/+LUwHIcbWDa/gWZWNlUvlL+CS5ljpTZhQGYqe4/BhhrR0pH+8GrrIKACAdY8nYPPUk8g+eAQZazdBn5AAh1fHQbA27bGaczxvbsV81t26QFAqkPHlt7Bq0ggKP1/jPgFAB7fwx2rKgdo2dTAWLdEhOBDy3NJP01CcsIsboddVnyHku3fvxoABA6QOo9Q4FJqIiIiIiKo1sbCxg1SsTTevIldv3nnrAlM0cFK8Al+XCESqfjZr3ZbqRMZlODfxR92wwAL7xj9nY3zu7izD+tkP5lkUBAHPdVXiua7Fr+Q88yU7k9e21gKmvWBbZPnE1Ivw9mgM66Sq+T9hZZuOXNSt1Dbi7aru/3dzNwNuZscUud9+5FDjc7mrC1yWLzS+FgQBNk/3hM3Txc/n5zj5FZPXgo01HCaMKbTsEx6ZiFDtLk3o1Z5MkKOXQ3u0DAuCTGfeId+5WQmIvPYTGneYZNZ6K4ubm5vUIZQJeywSEREREZFkhgwZgh9++MH4euLEifi///s/4+sdO3Zg9OjRuHTpEkaPHo127dqhffv2eP3115GQkAAA6Nu3r/Hf/OFjR48exYABA9CuXTuMGDEC58+fN9Y5btw4LFmyBH379kXv3r0RHBwMHx8fnDx5En369IG/vz+WLl2KO3fuYPjw4Wjfvj2mTJmCzMwHvWe2b99uLDtu3DgEBxfe+89gMODrr79G37590bZt2wJlfXx88Msvv2DQoEHw8/PDmDFjEBUVZYYrW7SQtFQcjqycnm3pWi3OJtWFi8P7cLZpXiltWJoDOdeQWq+l1GEAAERRj9hmVTcPn0yuqvQ2IpSGkguZiai4V2VtlaSThwwRqk1Sh1ElGtp7Y7ymAXzvXIZMp6mUNu5d3QqNOrVS6gaA6OjoUv8d0Wg0WL58OXr27InWrVujT58+2LFjh7Gu/KHQ+XU++hg3bhwAQKPRYOnSpejatSu6du2KOXPmQKVSVdo5FoWJRSIiIiIikkyPHj2MST+tVovAwEBcv34dWm3eohinT59G9+7dMWXKFHTv3h2//vortmzZgsjISGzevBkAsGvXLuO/AwYMwO3btzFv3jxMnToVBw4cwJAhQ/D6668jIiLC2O7evXvx2WefYd26dbC3twcAbN68GRs2bMCSJUuwbds2zJgxA++88w62bNmCwMBA7N6d13PoxIkTWLduHT744APs27cPHTt2xPjx45GWVnBY6Pr16/HNN9/gvffew759+1CvXj289tpryM5+MDddQEAAFixYgL179yI1NRWff/65+S/0Q9YFBcKAyu0FdlOlxj31C/B2fQ2CWLM/dupFPfYJEch2byh1KACA+xlnoHWsmnkJZVWwaM9tWdWsCF3fEbiVcadK2ipJa1db3FetkTqMSqeUKfG8Q0cMCouGfVJEyQdUgF6ThfDLX1dqG0Dp/o5s3rwZJ0+eREBAAI4cOYJhw4ZhyZIlSEpKMqnLy8sLp06dMj6+/fZbKBQKTJw4EQCwevVqBAUF4auvvsL333+PzMxMvP3225V+jo+q2b/hiYiIiIjIovXo0QMXLlyAKIq4ceMGGjZsCCcnJ9y8eRMGgwHnzp3DE088gWnTpmH69Olo0KABOnbsiP79++Pu3bsAHgwbc3Nzg42NDbZs2YKXXnoJgwcPhre3N8aPH4+nnnoKP//8YHhu79690aFDB/j5PVhFd9q0aWjZsiUGDRoEd3d3DBw4EN27d0fHjh3RrVs3hIWFAQC+/vprTJkyBU8//TQaNWqEmTNnol69ejhw4IDJuYmiiB9++AFvv/02+vbti6ZNm2LJkiWQy+UmZSdOnIhu3bqhRYsWGD16NIKCKrZYQXEuJsThTHxspdX/MLVejzOJzrC2WwAPu9LNRVhdqfXZ+MUxC1p76YcwGnQ5iG9V+XMfAgDUldyOAFw3VE1i0dnhfpW0U5JGDrbIyloLETqpQ6lUPo4tMD7LA03uXoZgqJpeqdE3dyM7rXJ7hJfm70jLli2xbNkytG/fHg0aNMAbb7wBrVaLe/fumdQll8vh6ekJT09P2NraYvHixRg/fjz69OkDtVqNH374AYsXL0bbtm3h4+ODFStW4Pz580X2oK8snGORiIiIiIgk06lTJ6jVaty9excXLlxAp06dkJCQgEuXLkEul0Mmk6Fjx45o0qQJtm7dilu3biEkJATBwcHo0KFDoXWGhobi8OHDJkPLtFotevToYXxdr169Asc1aNDA+NzGxsakjI2NDTQajbH+zz77DKtXrzbuz83NLfChMDk5GSqVCu3aPVgVVqFQwM/PD6GhocZt3t7exucODg7G3prmJooiAoKuVErdxQnLyIaV0A+d3HsiNm0zdGL1WUShLFJyk3DEyxsDItSQa6VdSCgm9yzqKp6FTFvJ8xOmqQBBBMRK6iHpDJhn+Y7iudsKCEqrvIR+aXnYWMPGsA3phnSpQ6k0dlYO6K9ohrp3rqJq1/sGRIMOoefXo80zn1RaG6X5O9KvXz+cPn0an3zyCcLCwnDz5k0AgL6YeW/nz58Pd3d3zJ49GwAQFRUFrVaLUaNGmZQzGAy4d+8efHx8zHlaxWJikYiIiIiIJKNUKtGpUyecP38eFy9exNChQ5GQkICLFy9Cr9eje/fuSEhIwIsvvojWrVvjySefxEsvvYSTJ0/i6tWrhdap1+vx+uuvY9iwYSbbbWweLKZh/chKrUBe75CHyWSFD/DS6/V477330K1bN5PtDg4OJq8LayP/eMNDPXQUCkWh5cztj+h7uK1KqZK2HqUTDTibpEB9+zmoo/gHcZl/SRJHZYvMisCpxr546m4wBLHq5gZ8lDYnFYl+atS+YlNy4Qo1pIPcOQt6lUPJZcshx7VqUk/1XFIQl1Y1PSOLYmdlhXrK35GYXblDgqXU3skPnaNjocwq/Hd3VYgPO4aGCUFwruVXcuFyKM3fkTVr1mDXrl0YPnw4hg0bhkWLFqFPnz5F1vn111/j4sWL2L9/P6ys8tJ4+UnIn376CXZ2pos8ubu7V/Q0yoRDoYmIiIiISFL58ywGBgaiY8eO6NixIy5fvoxTp06hZ8+eOHr0KJydnbFp0ya88sor6NSpE6KiooyrQQuCafKhcePGiI6Ohre3t/GxY8cO/P3332aJt3HjxoiLizOpf+PGjQgMDDQp5+joCA8PD5PtWq0WN27cQOPGjc0SS2npDAZsvHGtStssTHSWGldUnVDXZQ6s5S5Sh1MpgjJu4mqLtlKHgWjZxUrrSPgwhXPlDYdOc6y0qo3sFMCtTOkSXQAgFwT4OV1DYvYlSeOoLK5KN7ykaIPuwTegzKq8BVRKR0TIubWSRrB9+3Z88MEHmDNnDgYMGAC1Oq+Hc/7ftIedO3cOn3/+OVauXInatWsbtzdo0AByuRwqlcr4d8jBwQHLly9HcnJylZ0LwMQiERERERFJrEePHjhx4gQcHBxQu3Zt+Pr6Qq1W48KFC+jZsydcXFwQGxuLM2fOICoqCps3b8Yff/xhHJpsa2sLALh9+zaysrIwYcIEHDp0CN9//z0iIyOxdetWbN26FY0aNTJLvBMnTsR3332H/fv3IzIyEp999hkOHz6Mpk2bFig7YcIErF27FidOnEBoaCg++OAD5ObmYsCAAWaJpbSORIUjNrsqBpWWTARwMUlEhjAN9Z0GSx1OpTidfgXhzQofql9V1JkxSPWt/F54ctvKG7Ybb1vJQ7kBNHfPQrZe2uH5XTwSEJ1+WNIYKsuTTv54OU4Lz0jph5rnS429BFVcoGTtu7i44M8//0RUVBQuXryIuXPnAoDxb1q++Ph4zJ49GxMnTkSrVq2QmJhofDg4OGDkyJH48MMPce7cOYSEhGDu3LmIiIhA/fr1q/R8OBSaiIiIiIgk1axZM7i7u6Njx44A8oaS+fv7Q6VSwc3NDc8//zwuXLiAt956C4IgoE2bNpg3bx4CAgKg0Wjg5uaGIUOGYObMmZgzZw4mTJiAFStWICAgACtWrEDDhg2xatUqdO7c2SzxDhgwAElJSVi7di2SkpLQrFkzfPnll4UmLidNmoTMzEx88MEHyMzMhL+/P7Zt22ZccKYqiKKIbXduVll7pZWUk4uknBZo7z4fmdlbka21jMUzzOVw1mWM9G4Dz4jrksUQbR8EN7QruWAFyOSV12PxnrJyE6NWMiA8R9qevF099YhI/V7SGCpDbZs6eCbHGc7BgVKHUqh7V75F++e/kKTtjz/+GB9++CEGDhyI2rVrY+TIkZDL5bh16xaeeuopY7nTp08jKSkJmzdvxubNm03qCA4OxrvvvotPP/0Ub731FrRaLTp37ozNmzcXGI5d2ZhYJCIiIiIiyf31l+mce998843xuVwux+LFi7F48WKTMhMmTDA+/+yzz/DZZ58ZXw8cOBADBw4stK1t27aZvK5fv36BVTRPnDhh8vqTT0wn+x8/fjzGjx9faP0PHyuXyzFr1izMmjWr0LKPtjt8+HAMHz680LLl9VdsNO5lWO5iEIHJGjgpXoGvSyQiVT9JHY7ZiBCxX3cbo2o3hWN8aMkHVIL0tNvIaNwejuGV1/NPpldVWt23ZQagEqeqbOWhweXMKlpBuxDt3ZSISvkEVb6KSSWSCXL0dmgPn7AgyHTxUodTpKTI08hMvgsH9+Zmqa+sf0cOHjxosm/y5MmFHlfc3wNbW1t8+OGH+PDDD8sTstkwsUhERERERFSDfX/nhtQhlChdq8XZJC/4urwPUbcDaTl3K73NyC05kNsLqDeq8EV2QlerkXvfNKvV5B1b2NSRIf26DnH7NIAM8BquhKPvg4/WYWvV8HpRCdt6cmgMGuyzTsTLznVgnRZXqedTlJg6oWgZ3qTyGshWVU69AnDdULk9FhP10vXkbe5kh5SMz/JW1UbVvB8rm7d9IzytAuzvXK70tipOxL3ArfDru0zqQKo9JhaJiIiIiIhqqIsJcbiRWrUT+VfETZUatvIX0N4tFZEpWyAKldNdLS1Qh8zbejh3LPwjsWgQoUk0wHuqDaw9HnQnk9sLEA0i7u/ORZ2h1hBFEbE7ctHiQzkEQUDGLR0UToJJEidDm4Zf3e0xVOMIK3VGpZxPcZJSL0Fduyls4yup12KaKi85ZuaVYkQXINusNZrycdcjSC1NstfLzhaidjN0Yt7cjlX5fqwMSpk1+tr6oXFYIASDdKuhl1V86FE07TwNtk71pA6lWuPiLURERERERDXUd9Wgt+Kj1Ho9ziQ6wdpuATzs/M1evz5bRPyvGtg0KPrjsDZFhKgHbBvIYOX04CHIBeizAH024NRWDqd2VtBnA/qsvOOSjmnh8YyiQH1x6licqF8borzgvkonGhDbpBLnr9TpIXcx/8JAuS6VOz5YaxVWqfUXxVmphJtsD7K1ecOEpXg/mpOPYwuMy3RDk5DL1SqpCACiqEfE1W0lF6RiMbFIRERERERUA4WkpeJ8gjQ9sswhLCMbNzP6oaHrW7ASbMxWb/yvGjh3tIJ1raI/DufGG6BwESBTFExuye0BQQGoYwzIiTZAUAJyOyDztg5yh6J7h93NvIvzzVqi8tc5Lig+7Sw0TpWXqFM4m38OT5WT2as08nYGgjOqft5LpUyGZnankZpzy7hNqvdjRdlbOWCYrT/63rkLG4mG+ZtD7J2D0OZIN89mTcDEIhERERERUQ30c8htqUOoMJ1owNlEBfTKOfBy6FXh+rJC9MgK08OzX/G9uHITDBDkQOQ3OQhenI17X6qhjsyb70+QCag9UIl7G3IQsTEHdQYrIcgEJB7TwrN/8fVeTL+G4BYdKnweZWXQ5yK+VWql1S+3NX9iJs628lKwdrYxlVZ3UQQAHdzCcT/zb+M2qd+P5eXv1AZjUpSoFxZY7dedMehyEH1rj9RhVGucY5GIiIiIiKiGSc5R44+oe1KHYTYxWWrEohM6enRBcsZXyC3HSsQGrYj7e3Lh9YKy0J5fD8tNEKFXi3DpYgXP/gJU53SI2JyDpnNsoXCRwa27As4drSAIgMxaQGawDnJ7AdaeMkRvy0F2pAFObeSoPVgJQTBt63jGJTg28Ue9sMAyn0NFxGSfQV3rAZDnmj9hJ5OZP7F4T1E5C7d42gE30oMqpe7iPOGZhYjU3cbXlvJ+LAtXpRueMdSFZ3DVX7/KFB20E95tx0EmxVQFNQB7LBIREREREdUwe8PuQlPN5jsriQjgYpIBGcI01HcaXObjE49qYVNfBgefkvvX1B2hRLN37eDkZwXb+nLUGa6EwlWA6pLOWEZuI0BmnZekSTymheczCqSc1kI0AM3m2iI73ICM64Unxw7mXENqvZZlPoeK0GnSkehXOcuhyHQqs9cZLKuc96+XczL0VTwgvZOHDBGpG022WdL7sSQCBDzp1B4vx2nhGVX95m0tSW52IuLDjkodRrXFxCIREREREVENkqvXY0/4XanDqDRJObk4n9ICtZzmw15Rt9THpQfqkBGkx60FWbi1IAtpV3RIu6LDrQVZBcoKcgFymwc9uwRBgHUtGXRpBRNSmXf0kNsJsK0vR/Y9A+ybyyFTCLBvJkf2vcITOXpRj32ySGS7Nyx1/OYQbTgPUVYJSbVslXnrE4Agg/l7LDoqgRsZV81eb3Fau9rivmpNge2W9H4sTm1bL4xFC/gHX4U81/yL9FiKyGs/SR1CtcWh0ERERERERDXIsegIpObmSB1GpQtM0cBRMR5+LlGIUP1YYnnvqTbAQ3mV+N80AIDaA5UFyt7bqIZ9Ezk8++ftEw0icu4b4PZkwaGSScc0qD0kr5wgAPmd4USDiOI6xql1WfjF0RYjctygyEopMX5zyMmOQ7KvHh5BZk4FpKcBggiI5plxT3QVUBl9K5u4Z+BimqYSai5cIwdbZGWthQhdgX2W9n58lFyQo5dDe/iEBUGmq76Ls5RWRtItqOIC4VKnvdShVDvssUhERERERFSD/BpR9avdSiVDq8WZpDpwdngfLjYtii2rdJVB6fHgIbPOGzqq9JBBNIjQpRsg6vIyL46trJD8jxYZN3TITTAgbr8GejXg3Mk0IZcVoofMJq93GADYNJAh/ZoOOXEGZN7Uw9a7+BV5U3KTcMTLAXqFbQWuQtnE2FZCjz2dHnIX8/Vmy3ExW1VGChkQkn3N/BUXwcPGGjaGbdAYCl8x2xLfj/m87RthnKYBWt25DJmu6hKxUosNPiB1CNUSE4tEREREREQ1RGxWJq4kJUgdRpW7pVIjXD0M3q6vQyjHwDytSsSdJWpkR+TN6+f2lBXceysQt1+DsDVq5MYb4D3FxmQ4KgAkHtXA85kHvcbcuisgUwq4t14Nu6ZyOLUtOZETmRWBU40bQRSq5uN5RnoI0puZfzi0wtl8C7ioHM1WlVErz1yotBnmr7gQdlZWqKf8HemaiHIdL9X7USmzxgD7jhgYFgX7pPLFXp3Fhx2DXlfze3ubG4dCExERERER1RCHIsOqeFkKy6HW63Em0QlNHOfDQfgVSdlXii1fb5S18bnSTQbfz+yNrwVBgGdfJTz7FhyW+rBGU017GsptBDR81abMsQdl3IRLC3+0Cw4s87HlEe1xF74hxffwLCu5bRqA+mapK97WvO9iAUCctmoWHZELAlo7XUdM+qUyHSf1+7Glow+6x6fDJuZyGaKuWfSaLCSG/4k6zZ+XOpRqhT0WiYiIiIiIagBRFHEoMlzqMCQXlpGNmxn90ND1LVgJZU/ySeVU+hWEN/OvkrZSUgORXdc88yHmk8nM12MxXGHehVtaeugQk1M1PXm7eCQgJv1QlbRlDvZWDhhm2x5979yBTVrNn0uxJLF3DkodQrXDxCIREREREVENEJicgJismrtqa1noRAPOJiqgV8yBl0NvqcMptcNZV5Do7VcFLYmI9Y42a40ynfkSi7dk5k0sZstCzFpfUbp66hGR+n2VtGUO/k5+GJOiRL2wql0p25KlxFxATma81GFUK0wsEhERERER1QC/RbC34qNistW4rOqIui7/g43cVepwSiRCxH5dMDJqN630tuJV55DrYsZei9kq89QjA4IMBvPUBaCJi4iQzHtmq68o7d2UiEr5otLbMQdXpRtetmqDJ4NvQJmVKnU4lkU04P7d36SOolphYpGIiIiIiKiay9HpcDzm8VtsoTREABeTDEgXpqKB02CpwymRxqDBPutE5DrXqdR2RIMWcS2TzVdhmgqQVTwhKLoKyK14NEZKmygz1la45k52SMlYAwiWPcOpAAFPOvnj5TgNPKKCpA7HYt2/86vUIVQrTCwSERERERFVc3/GRiFbp5M6DIuWlJOLcykt4Ok0H/aKulKHU6wMbRp+dRegt6mE5ZEfcj/zDHQ2Zuq1qDfAyqXiQ/FzXCoeSr469sCNtFvmq7AQXna2ELWboRMtezXhOrZ1MRbN4R8cCHlultThWLRsVQTS4q9LHUa1wcQiERERERFRNXcoMkzqEKqNqykaxOnGw9tlrNShFCtOHYsTDWpDlCsqrQ2dNhMJfuabl9PKOb3CdaQ6mCGQ/3g6JcJQib0InZVKuMn2IFtruXPyyQU5+jh2wAsRyXC+f1fqcKoNLuJSekwsEhERERERVWPx2Vm4mGC5iQ1LlKHV4kxSHTg7vA8XmxZSh1OkO5l3cb5ZS1TmANsY3TkY5OapS25T8QVc4uzMc7bO1kBQ+jWz1FUYpUyGZnankZpTuT0iK8LbvhHGaRqg1Z0rkOk1UodTrcSH/AEDr1mpMLFIRERERERUjR2ODIehUlNPNdctlRph6mHwdpkMAVZSh1Ooi+nXENyiQ6XVn6tORLKf1ix1yWUVTyyGW5lnRehGbunQiOY5r0cJADq4huN+5t+VUn9FKWXWGGDfEQNDo2CfxLlXy0OnyUDivZNSh1EtMLFIRERERERUjR2O4mrQFZGj1+NMkiOsbefD066j1OEU6njGJcQ0aV9p9ccor5ilHkGnqnAdt2QVTyzayIE7WVcrXE9RnvDMQmTa7kqrvyJaOvpgfKYbGodchiCab3Xtx9H9O4ekDqFaYGKRiIiIiIiomrqXkYZ7GRWf146AsIxs3Mjogwaub8FKsJE6nAIO5lxDar2WlVJ3Zno4VD5m6PWaparY8TLgpqHiybAWHmqk6ypngZJOHjJEpG6slLorwt7KAS/YtkffO3dgnRYndTg1QkrMOei02VKHYfGYWCQiIiIiIqqm/o6NljqEGkUnGnAuUQG9Yg68HHpLHY4JvajHPlkk1O4NKqX+GNfbFa8kPQ2QlT8xKLoKyK1gCHIBiNEGVbCWwvm62uK+ak2l1F0R/k5tMDZFibphlddL83Fk0GuQHHVG6jAsHhOLRERERERE1dQ/95lYrAwx2WpcVnVEXZf/wUbuKnU4RmpdFn5xVENr72b2ulNTryGrgVCxSvQGWLmWf5VptRkudUsPLeJykite0SO8HWyhzl4HETqz111ertbueNnKD08GB0GRlSp1ODVSUsRfUodg8ZhYJCIiIiIiqoZScnIQlGL+BArlEQFcTDIgDVNR33mI1OEYJecm4ncvR+gVtmavO7Z+xRf6sHIq/wIuqQ4VH46djrsVruNR7tbWsDVsQ65eZfa6y0OAgCed/PHy/Vx4RN2QOpwaLSnyFESDeRYUqqmYWCQiIiIiIqqGTsVFczXoKpCcm4vzyc3h6fQeHJT1pA4HABCRdQ+nmzSCKJj3I31C6nnkuFesTrlN+ROL9yuYK23mKiIsK7JilTzCzsoK9a3/QLrGMlZXrmNbF2PRHP7BgZDnVs48kvSANicNqjgOMS+OldQBEBERERERUdndTMqFHDLo8Xiu/Jq59ScI9vawHzm00P3pX2yCIS7eZJvj229AXqcWNEG3oD5wGJDJYDf0eSha+RjLZKz/GrYvDIRVXS+TY6+m5MJRMQ5+LlGIUP1o/hMqo+vpN+Hcwh/tggPNVqco6hHXPAGNkj3KXYdcVv7EYrhCj4rkymXW91DhSRofIhcEtHa6jpj0i+artNyxyNHboT1ahAZBpr9faJkN+9RwsBUw/rnCFx/6+PtsxCSZ/r5YMN4WdT3kCLyrw44TuZAJwKi+1mjT9EG6aMVP2RjdzxoNasnNd0LVhExujayom3Ct20HqUCwWE4tERERERETVjFYv4nhIHTgKddDIRQtYJSM8OxRp2gypQ6sSmqtB0AWHQNGhXaH7RYMBhqRkOEx+BTIPd+N2wc4OosEA9b5fYTvoOUAUkb37AJzenwNBEKC9fReCk2OBpGK+DK0WZ5LqoJXL+xB0O6HKuVMp51dap9KvwKlZBzQOuWK2Ou9nnEF9uyGwyi5fhk/Qqsrd9m2h/InFeo7AzbRgoILTRD6si0cCIlIPma/Ccmpk3xi9VSLs71wusszF21rcCNejq2/haR6DQURCqgEzX7JFLdcHF8nBVoDBIOKnozkY8bQ1RBHY9nsOPp1qD0EQEBSmg7O98FglFW3sasPNxhduqR5wDJHBKjkX6Cp1VJaLiUUiIiIiIqJq5lqiDur/1pC4kagAUAcyoQ6aO4lwtktHkjYKEVnREM2YZLEUhmw11IePQV6/btFlUlSAXg95/XoQFKYfew0ZmRCz1VC08QUgInvXfohZ2RAc7JFz4m/YDhtQYgy3VGrYyIfB31WFSNU3ki7ocTjrCkZ6+8EzwjwrIet12YhvnYF6FxzKV0GWqnzHyYAbhvL3vnVxiMO98neWLKCrpx4Rqd+br8JyUMqs0c+2NRqFXoUgFn1tstQi9v2tgXftooexJ6WJ0BmARnVkUFiZ/mJIzzLg/9m78/Coqvvx4+87+2RmMlkJIYQkEAIkbAFBcEFEtIo7VutSa6ut27e1aq1fd2td29/XvZtWW61ttbhg3RdwwQVBJeyQjWxkI/vMZPaZ+/sjGohJyDZhEvJ5PQ/PM3Pvued87mSGPPPJOefT7oV5OTpUFf7xjg+XR8UWo/D2F37OX26M2D2NRIqiJdY+jYRwFvHVNmIKu2a31fom1BYHSnxslCIc2SSxKIQQQgghhBCjzIYaf7djYRXK2xRoswN2kk2zmBjrJaDUU9JeiicUwTWiUeR9630M+bMIO3qvPhze14Bij+2WVARQLDGg1xOqqQVVBYMeJcZMoLAExWrpdbZitzhCIdY32phsuxmb8jYN7ugsl1VReTVYyPkpU7DVl0akz2r/elJ1J6IZTL7U4QBNGMID26tRTVAIDGI4gHiTwg7HtkFe3d3cBCNVzfdHdPbjQE23TeOYegfG6r5no76yzsfCXB1trt6ne9Y1h4m3Kd2SitAxa9Ggg6r6jl1bDXqwmBR2lAWxHaazFfVGOwmWPOJdqcSV6g+YodvzaxjaXYZucc8zpMc6SSwKIYQQQgghxCizoabvFEyLV6XFawQmoddMIi8ujMnYQrWvnDpvw/AHOQwCpWUEyyqwXXsl7ld7X6IaamhE0WpxPfM8oeoaNMmJmE85EV16GopGg/nkE3A98QwoCuYzTkbRaDpmK555yoBj2uN0o1WWsiDpaGpanyCoegd/g4PkD/t51djIefbxGNvqht6ft5nGmT7GbR7ETLVwGF2Ck2CjfUCXeeIGPtS3JsW3sK8tMpV7p8bG0Ox8EJToFEay6KycpJvKhKLN/WpfWBmkZG+IW38Uwwtre//jQV1TGJ0G/rzaQ0V9mJR4hbOXGMlM1aLRKJx5rIGHVnnQKHDu8UY0mo7Zij844fCZrWiNnUyCJpv4fQlYi9QDfsR9/6zDu8tAEos9ksSiEEIIIYQQQowije4wpa0DS6IEwlDUrAESgUQmWWCczY0rXEOJq4wgkUnKDCc1EMSz+k3MZ56CotcftG24oRHV68WwIB/NiUvxf7kJ11PPEXvdVWji7BiPWohh/hxAQTEaCBSVolhi0CYl0v7vlwhW7sUwcwamU09CUfqethZSVb5o0JEWcwOpxs+odX4YobvuP0eglTcSYzjLZ0PrHfpem3t1m0hWFqEMYj29LtYx4MRis3VwiTyzDna3bx3Utd+VGmNCDTxJUHVHpL+Bmhc7iyP2VqNv39yv9oGgyvNrfPzgBCMG/cF/TvUtYdw+laNm6Tn1KIXPtgV47CUPt/84hnibhqX5BhbldXyuTAaFXeVBrDEK4+I1PPWGl/LaEPlTdaw8ztCvz8RIoNGZiLflkeBLJ67CjLHw2+XkA3+vhYsrUENhFG1kK7EfDiSxKIQQQgghhBCjyIba7sugB6q2HWrbY4BsLPpsMuOCaHSNlLlLaR2hBWC8az9Gm5aKPie7z7bms0/HHAigmDpmW2knrCBYUYW/YCum448FQDHun4nl/WAd5tNPxrf+SwiHif3V/+B64lkCO3ZjmDmj3zFWuz3UuOcxP+kImp1P4Q21DPAuh6bOU8MH6VNZvseLEhrswuIObmclrdOPJH7XIBKLplYgfUDX1JkHPAwAOUluvnIMPRFoNxhI0LxCi7e+78YRFm9M5KRQKkmFA9sn8631fialaMnN7Du1c+GJRvwBI2Zjx88zfZyGPTVhNuwMcvKRBqAjodjZ9xd+zj3eyMcFAcJhlTt+HMPDqzxsLg6RnzNyU0kmSyqJhunEtyQTW6RBE/g2iTj4/TsB8PpQK6pRJg/sfT0WjNx3gxBCCCGEEEKIbjbXR7ZQiDsAOxt0wHgUxpNth7gYB02BKsrbq0ZMAZjA1h2EnS5a77y/40CwY5Zl6/adxN11c5e2ilYD2v2JQ0VR0CYnEXZ0T5oGSvagxJjRpaXiXfsx+pxsFL0e3ZRMQuWVMIDEInTMhfqqMUyi8Sqm2Lezt+21gd3oEBW5ionNns3Cwm1D3iKwOnYn8cwc8HUaZeBVVPboB14RWqtAhXfoeysaNBqyYz6j1rVryH0NhILCUbFzmVVejNa3Y8DXf10YxNGuct3jHfuNfvORoKDYxcO/6Fp8R6tRMB+wqllRFFISND3uy7i7IojFpDApRctb6/3kZukw6BWmpWsprRlZiUVF0WK3TychlEl8tQ1zl8IrkV3OHi7di0YSi92MnHeDEEIIIYQQQog+7Wgc2ky0g1GBijaoaIsF8kg25THR5iOgiX4BGOvPfoQa2j/ryPvOGgBMJy/v1tb112fRZWViWn4cAGpYJVRXj3HRgm5tvWvXYT7tpI4nitJR0AUgHO58OBhNPh9NvqnMSbgFj/dZXP7qwXc2QF85thKXM59pRZuG1E9r6w5cGbOwVgzshVACrQMea6cy8MTijGQ/Ba6hzQpVgHnxZVS2rRtSPwM13jyB5R4b9sLNg+7jl+eZCR2wi8F/P+n4fJ55bPd9ER9Z5WFqupZTF3fMTgyrKjUNIZbM7b6twFtf+Pn+0o4+Oj4SHT+YkEqkc3WDojfGkWjJI945HvuevguvREq4omZY+x+tJLEohBBCCCGEEKOE0x+myjHEJX0D0OKlswCMTjOJ3LgwZmMrtb4yag5xARhNfFzXA98sZdYmJaCGw6jtbhSzGUWnRTc9B+8H69BOGI8mORHfZxtRPd5v9lXcL1BahmIyokubAIBu4gT823aim5xBYFcRppOOH3LcW5p92PQXMzNuLxWt/xxyf/21xvk1tslzmbBny5D6qZ6wh2kVWQO7qL11YO21sCs88Pd1c7hwwNd815HJ7VS2vDTkfvpLq2hZap1LTul2NKHaIfWVGNt1vz/jN0uZx8VrCIdVnB4Vi0lBp1WYNUXL21/4SR+nISVew4cFftw+OvdV/FZhZRCzsWO2IkDGeC0FRUGmTtSyvTTIaUcbhhTzYFljp5CoySauLg5rEQMqvBIp4cqh/bwOV5JYFEIIIYQQQohRYmdjMGoThoJhKG7WAAlAAukWSLG5cak1lLrKCKjRKwATbnPg/P1jWH72I/STMzEeswiCQdyvv4PqcqFNT8N62cVd9lWEb2Yrrjix87lx8UKCFVU4//x3DLPz0M/MjUh8zkCA9Y0pzIi7DSX4Iq3eoSfE+uM17zbOT5tGXPXgx2to+YqM5MmYGgbwznM4QBuCkLZfzcMJCgNd4J+TEGaHe2izQI9I0lLZ8pch9TEQmZYsjm8NEzPEmaT90eJUueNpN78810ROuo5l8/QEgrDqAx9Ot0rmeA3XfN/UZV9FgLe/8HP2kv2fk+Pm6tlTE+LBFzzMn6Y7ZMugtToz8bF5JHgmYi83YSwcAVMlXW7CTa1oEuOiHcmIIolFIYQQQgghhBgldjRGdn/Foahrh7pvCsCYddnkxgXR6Juo8Oyh2T/wPfYGynLumZ2PtfFxxN1/R+dzRVEwHX9sZ6GW3tguv6TLc8VkxPrjCyMb6AF2tXowac8kP6GVypa/oQ44nTYwITXEK9oqLkhMx9xUNbhO1DA12fVMbhjX/2vCKroEJ8GGuH419/SvWRch/R7wDvy6b+XGm6lr/d3gOxgAo9bECaZcMku3oKjDN+P4RyebOh8n2jX88fr9+ywqisLJRxo6C7X05trzYro8NxsVrj57kJV1BshsmUCCYTrxLUnEFipoOj8eIyCp+A21vAYksdiFJBaFEEIIIYQQYpTYOYISiwfyBGFnow5IQSGFKbEQb3HSHKyi3FVFWBk5iYFo84ZCrG+wMdl2MzblbRrcXw3reJ5gO/+1xXCONwF9e/Og+qhr+5x029nonf3/Oepsjn4nFlusA3t/TIqF3c6SAV1zoAyrGY/7D4QZvv1KvzXDNp2j61sx7i0Y9rFGG0Wjwx77TeGVvdbvFF4ZmcKVtWjnR2Ym8+FCEotCCCGEEEIIMUqM1MTigVSg0gGVDhuQS4Ixl0l2P0FNPaXuUtqDQ5hmdhjZ43SjVZayIOloalr/SlB1D9tYTb4G3k3N5JQKD9qAZ8DXh0M+6ma0kr7R3u9rdKb+z1qtMQ0soWSz1MAgJ8UmGo2Yw8/hCLUOroN+suhsnKSbwoSioe1xebjRm+JJjPmm8EqpFl3n23HkJxVBCrj0RNN3k+jx+/3cddddLFiwgKOOOoqHHnqosxrRzp07Offcc5kzZw7nnHMO27dvj3K0QgghhBBCCDF8Kh0hHP7R8eX7QG0+2LbPwK66dELOpcwwnsR8+5GkmVOiHVrUhVSVLxp0BPXXk2obeqGYg6loL+ezyZmoyuDSADXe9YQGULdDo/Q/87dH3//lwUlm2NY2uO//MTodE43v4fBXDOr6/poXO4uLmvRMKJOkIijYYrPJsJ/MXO8PWLj1RLLXp5G4/cCk4uihVu9DDY78P/AcSiN6xuI999zDhg0bePrpp2lvb+e6665jwoQJnHHGGVx++eWcfvrpPPDAAzz//PNcccUVvP/++8TExPTdsRBCCCGEEEKMMqNhtmJfQiqUtGiAeCCeiRYYb/PQrtZS4iqNagGYaKpxe6h1z2N+0hE0OZ/GFxrckuW+bHPsxJ6Tz5zCzQO+NuBro3Gml5RNpr4bA0qgtd9976T/P/e0uGZq2wa+T6FWUciL3Ua1Y/iWnscbE/leKJXEwrE98UmrjyHBNpN4TxpxZUYMXZY4K71eNyqEQqh796FkToh2JCPGiE0stra28vLLL/P3v/+d2bNnA3DppZeyZcsWdDodRqORG2+8EUVRuPXWW1m3bh3vvPMOK1eujHLkQgghhBBCCBF5OxqHfz+4Q62+HerbzcBkTLrJzIgLodU3UekppekQFIAZSVTgq8YwicYrybbvoKrtv8MyzqeOAmKz55FVMvA9//byFeOUY+jXlpmulv51qoPd/SxoYtHDDtfgZgEuTGqgouWtQV3bFwWFo2LnMqu8GK1vx7CMMdKZrWkk6qcR35SMbRdoOnPFo2+WdV/CFTVohjmx+Pjjj7Nx40aee+65YR0nEkZsYvHrr7/GarWycOHCzmOXX345ALfffjvz589HUToy3YqiMG/ePDZv3iyJRSGEEEIIIcRhaSRVhB4O3iDsatQC44BxnQVgWoN72eOqHDMFYJp8Ppp82cxJuAWP91lc/uqIj/F2ewHnZswkuWJgM+s87dW05IZI2KHtu7HDCboQBA/eNpyg9Hu+Ynaii68cvn623u/IpBAVLc8O+Lr+GG+ewIkeK7GDmAU6mikaPXH2GSQEMojfa8E0CgqvRMqh2Gfx0ksv5eKLLx72cSJhxCYWq6qqSEtL49VXX+Uvf/kLgUCAlStXctVVV9HQ0EB2dnaX9omJiRQXFw94HGUYZuEOR58yZnTHHStjRmtcudfDb8xojTtWxozWuGNlzGiNK/d6+I0pRCT5giqlLWNrmfD+AjAziDfMYFKcn5CmgT3uUlzB4St0MlJsafZh01/MzLi9VLT+M6J9q6i8Gizk/JQp2OpLB3TtXss2Epjbj0FUdPHOPitDu/tZD0avgT3ebf1rfIC5CUaqWu6P+ApcraJlqXUuOaXb0YRqI9v5CGUwJZAQk0eCI4XYUh0677fJxLGTVARQK4f/522xWIZ9jEgZsYlFt9tNRUUFL7zwAvfffz8NDQ3ccccdmM1mPB4PBkPXXWMNBgN+v39AYxgM/fgryyDo9cPTr4wZvXHHypjRGlfu9fAbM1rjjpUxozXuWBkzWuPKvR5+Y4rhV1VVxe23305BQQGTJk3irLPO4l//+hc///nPWbVqFYmJiXzxxRfceeedLFu2jHvvvZePPvoIp9PJxIkTueGGG1i+fDkA06ZN45577uGJJ56gqamJZcuW8dvf/rbzy91XX33FfffdR0lJCRkZGfz85z/ne9/7HgA1NTXcdtttFBQUYDKZWLFiBTfddBN6vT5i91rUEiQ0tr67d+Hww/Z9BiANrZLGjDgVi6mVen8FVZ66aIc3bJyBAOsbU5gRdxua4Iu0eAsj1rc/7OdVYyPn2cdjbOv/a+hoK8QxeS6xe/puq4tt6zOx2Gzt3xt7epKPTa6BLY+fGhtDs/NB+rd2u/+yLFksbQ0TU7Qpov2OPAqx9qnEM4X4OjuWojCK+m2Gduz+h6Q2t6G63CjWyNX42Lt3LyeccALXXHMNzzzzDKeffjrFxcWdS6HXrVvHww8/zJ49e8jIyODmm29m8eLFALz//vs8/PDDVFdXM3XqVG688cYuq3+H24hNLOp0OlwuFw8++CBpaWlAxy/s559/noyMjG5JRL/fj8nUv01k918T6vWv18ZBRd0hEBjcXxKjMeZQxo3GmEMZV17f4R13tL2+Qxl3tN2rvL7DO668vsM77mh7fYcy7mi717H0+oqRKRgMcsUVV5Cdnc3LL7/Mrl27uOOOO4iPjwegoKCAK6+8kuuvv574+HjuvfdeysrK+Nvf/obZbOapp57i1ltvZcmSJZ2TFh599FHuueceEhMTueWWW7jjjjt48MEHaWho4IorruC6667j2GOPZfPmzdx0000kJiZyxBFHcPfddxMTE8Orr75KU1MT11xzDZMnT+aiiy6K2P2Wt8p7+FsdBWAUvi0AMyEGUm0ePNRR4tqDXz389qLc1erBqD2T/IQ2qlqeRiUyy+IdgVbeSIzhLJ8NrdfZ7+tqUkqI3ZPdZzudse9EYI257wSVAuwL7epPaJ1SY0yogScJqpGb3WrUmlhuyiOjdDNKP/eFHG20egsJtjwS3GnYy00YCr+9T5VRX3glgtS6RpTsSRHvd9OmTbz88ss8+uijnceKi4u56qqr+J//+R9WrFjBe++9x9VXX817771HU1MT//u//8tdd93F7Nmz+fjjj/nZz37Ga6+9RkZGRsTj68mITSwmJydjNBo7k4oAWVlZ1NbWsnDhQhobG7u0b2xsZNy4cQMeRx2GJPtw9CljRnfcsTJmtMaVez38xozWuGNlzGiNO1bGjNa4cq+H35hieH3xxRfU1tayatUqrFYr2dnZFBUV8eabbwId+7BfddVVnZMPFixYwE9+8hNycnKAjv2rXnzxRZqamkhNTQXgZz/7GUuXLgXg1ltv5dJLL+U3v/kN//rXvzjqqKP44Q9/CEBGRga7du3i2Wef5YgjjqC6upq8vDwmTJhARkYGTz75JLGxsRG93wqHJBZ70+CGBrcZyMKkzWJafAi9vplKzx4a/f0sIDIK+EIhvmiwkmW7iVjlHRrckaluXOep4YP0qSzf40UJ9S8p29i8Cc/4qZjrDv6fq0bpO7G4Rxfuc/LbtMQQ2wYwMzVWrydBs5oWb32/r+nLDNt0jq5vxbj38JulGGOdSIJ+OgmNiVhLDiy8cngmTyMhvK8ZzTAkFi+55BImTZpEZmYm+/btA+Cll15i3rx5XH311UBH/RG3243D4eDpp5/mvPPO4/TTTwfgRz/6EV9++SXPP/88N910U8Tj68mITSzOmTMHn89HWVkZWVlZAOzZs4e0tDTmzJnDX//6V1RVRVEUVFVl06ZNXHnllVGOWgghhBBCCHEoFBYWkpWVhdVq7Tw2d+7czsRiYmJilxVNZ511FmvWrGHVqlXs2bOHHTs6KreGQvsTdvPmzet8PHPmTEKhEGVlZezZs4cPP/yQ/Pz8zvOBQKDze8pPf/pTbrnlFt5//32WLFnCihUryM3Njej9VkpisV+8IdjdqAWSgWQmx0KCxUVraC9lrgpCh8HyzTKnB62ylCOSjqa29a8RmZFX5ComNns2Cwu39XNOmkpNVg1T6lIP2krxt/bZ0y4l1Gdi0aft/z6QBo2GbMt66lw7+33NwVh0Nk7STWFC0eCqUY9EGq0Be+w3hVcqY8ZU4ZVIUfc1DUu/B06u+1ZZWRl5eXldjl177bUAlJaW8vbbb/Of//yn81wgEOCYY44Zlvh6MmITi5MnT2bp0qXcfPPN/OY3v6GhoYEnn3ySq666ipNPPpkHH3yQe++9l/PPP58XXngBj8fDKaecEu2whRBCCCGEEIeAVqtF/c5U1AOfG41dF87feOONFBQUcOaZZ3LBBReQnJzMD37wgy5tDtwTMRzumKmj0WgIBoOcfvrp3SYy6HQdX6fOOOMMFi9ezJo1a/joo4+45ppr+NnPfsZ111039Bv9RkWbJBYHo8oBVQ4rMB27YToZcQHCmn3scZfiHMUFYEKqyoYGHakx1zPB+Dm1zg+G3OdXjq3E5cxnWj/3Daxv+4J0+0oMbQdJSrW3HrwTHRSGDz4rLtMORa5+bOhIx0LdefHlVLZ93K/2fZkXO4sjqqrRu0d/UtFoTiLBnEt82zjspTq0vrFZeCVS1H3Nw9Lvd393wf7fNT0JhUL87Gc/46yzzupyfKBbBQ6F5pCNNAj/93//x6RJk7jgggv43//9Xy666CIuvvhirFYrTzzxBF9//TUrV65ky5YtPPnkk8TERG7jTCGEEEIIIcTINXXqVMrLy3G5XJ3Hvp2F+F0ul4s33niDhx9+mGuuuYYTTzyRtraOJZoHJiN37dq/h9v27dvR6/VkZWWRlZVFRUUFGRkZnf/Wrl3L66+/DsDDDz9MU1MTF1xwAU888QTXXnst7733XsTu1R9SqWuXJYlD5fTD9n16dtalEXAuYbrxexxhX8Qk88Fn3Y1ktW4Pm1rymRB3I0ZtwpD7W+P8mprJc/rVNhzyUze9j+SKwwm63pPi4QSFvlLmZnNVv+IBODK5ncq2F/vdvjcJxiTO181kceF29O5Rupxe0RBrn0amfQX57vNZsPkEpqxPJWGn9oCkohis4Uos9iQjI4Pdu3d3OXb++efz5ptvkpWVxd69e7v8fvrPf/7DunXrDll8I3bGIoDNZuP3v/99j+dmz57N6tWrD3FEQgghhBBCiJFg8eLFpKamcvvtt/Pzn/+c4uJi/vGPf2C327u1NRgMmM1m3nvvPRISEigrK+O3v/0tQJeikI899hhpaWkYjUbuuecezj77bCwWCxdeeCHPPfccDz/8MGeffTbbtm3joYce4r777gM6tmz67W9/yx133IFWq+Xjjz+O6FLovc7QmK4IPRxCKpS2KEAcEEeqeQ4TYr14qKWkfQ/+8OgpAKMCXzWGSDReSbZ9J1Vtrw6pv9e82zg/bRpx1X1XoK5xryfNtAKtt7fgVHQJDoL74ns87Y47eP8pFtjh6N+S5iOStFS2/KVfbXujoHB07FzyyovQ+Xr+Q8VIptNbSbDlEe9OI67MiH73gYVXRCSpLQ5UfwDFoO+78RBdcMEFrFixgr///e8sW7aMd955h+LiYo444gjS0tK46KKLmDVrFkuXLuWDDz7gmWee4dlnnx32uL41omcsCiGEEEIIIURPNBoNjz/+OPX19Zx55pn86U9/YuXKlV2WM3/LYDDw//7f/+Pdd9/l1FNP5YEHHuCqq64iOTm5yyzFs846i5tuuonLLruMBQsWcPvttwMde1795S9/4ZNPPuG0007jkUce4aabbuKMM84A4De/+Q1JSUlcfPHFnHfeeYwbN45bb701Yve61ymzFYdbowe21psors/C4DmBWeYTmWefzzjj0GcBHipNPh8bmqaQHHsLVv3EQfcTUkO8oqnCk5jeZ9ug38m+PM9B2+htvRdwabIePOE1LraxX/ti5saZqWt9uM92B5NqTuOH6lTmFG5G5xs9y+Qttkmkxy9nVvAHHLnzVHLWZ5K8RY/eIf9vDCtVRW3uuzhRJEyaNInHH3+cl19+mdNOO413332Xv/zlL6SkpDB37lx+//vf8+9//5sVK1awatUqHnzwQRYsWHBIYoMRPmNRCCGEEEIIIXrS1NRETU0N//73vzuPPfXUU4wbN46VK1eycuXKLu2XL1/O8uXLuxz7/ve/3+X5okWL+OUvf9njeEcddRSvvPJKj+cSExN57LHHBnMb/VLjlP0VDyVfCHY37S8Ak2mDJIuLtnA1e1zlI74AzJZmHzb9D8mL20tl6z8H1Ycn2M5/bTGs9MZjaD/4UuDq8AbGa45DCfdc9kVr7D35UmPq/bWMNcAOZ997G06ymvF4/kCYwc0y1SpajrfOJad0G0qoZlB9HEoarZG4bwuvVJgxSuGVqFGbWmF8UkT6mjhxIoWF+2cJ/+IXv+hy/vjjj+f444/v8dpTTz2VU089NSJxDIYkFoUQQgghhBCj0lVXXcUtt9zCcccdR0VFBc8++2y3AiuHgxqXJBajqdoJ1U4rMI1YwzQy7AFUbQNl7lIcwfZoh9cjZyDAF40pTLffhjb0Ii3evpc1f1eTr4H3UjM5pcKLNtD7rESvu56mvBBJ23pOL2iU3hOLe/Rh6GViXVaik6/aDp4sTDQaiQn/C0eo9aDtepNlyWJpS5iYmv4VrIkWozmZBHMuCa3jiC3VSuGVEUJtao12CCOCJBaFEEIIIYQQo05iYiKPPPIIjz76KPfffz9JSUn88Ic/5MILL4x2aBFX6xp7Sxrbnv8VSkw8sWfe1uP55id+RGhfSZdj8Vc8h27cFHy7PsL1zoOg0WI95QaMOcd0tml5+qdYV/wafeq0QcXl8sOOBj0wAY0ygRy7Sqy5jYZAFRXu6kH1OZx2t3kwas9kXkIblS1PoxIc0PUV7eV8lpXLscWFKGrv78Nq02aSOKLHc4q/98TiDrXnpLlBC8XurQeNLUanY6LxPRrcZQdt1xOj1sRyUy4ZpVsOel9Ro2iw23OIVycTXx2LpcusxKElE3++8V3ijWbunrOkx/PnrltNkbNrYZKXlpzNVFsCa2rLuX/H52gVDbfMPIqlKZM621z02WvcNvMoZtgjM4NvNFCbDs1S6JFOEotCCCGEEEKIUamn5c2DdeAStJGmZowlFr3b38dfsh7j7BU9nlfDIULNldh/9Ed0ifsTG0qMHTUcwvnm77B+71pQwzhfuxfDr95CURR8xZ+jsSYOOqn4XWEVyloVaI0D4hhvnk1arAevUk+JqwTfCCkA4wuFWN9gJdN2E3blXRrcXw7o+m3Ondhz8plTuLnXNk5HKW1T52Mv7mE5tKuXpdR6KAqHoYdLpid5+drp7HU8raKQF7uNasdXfUTf3QzbdI6ub8W4t2DA1w4nncFGgnUm8e0TiCvTo98d+VmJb9eU8knDXs6YOLXH8yE1TEV7G39btIIMy/5CWHEGEyE1zN3bPuXGvEWEVZU7t6zjoxMvQlEUPtlXRbLRPKaSigBqc2u0QxgRJLEohBBCCCGEECOUqqrUto+dpdBhj4P2tX9EN2FG721aayEURJ+Wi6Izdj3nakb1tGHMXQaqivO/d6O6W1AsCbg/+TvWU24YttibPCpNHhOQgV6TQV58GJOhmb3eMup9TcM2bn+VOz1oleM4IuloalufJKj2v0DJp44CYrPnkVXSezKuOrEIe3EPSVunE/RBCHRNP4QTFdQekooaBWoCB6/IvDCpgYqWt/oV+7es+lhO0k4mtajvfRsPFYstkwRtDvGN8diK1QP2qYz8Euc2v4+Hd31J3kGSf9VuF4FwmJlxyRi1XX9eTT4PrQEfJ6Vmoapw25Z1NPu9JBrNPFFcwK0zj4p4zCOdzFjsIIlFIYQQQgghhBihWn0q/rGTV6T9/ccxzjqZsLOx1zbBhjI0seO6JRWhY9YiehPB2kJQVdCbUcx2/CVfoLHER2y2Yl8CYShq0gBJQBIZVki2tuMI11DqKiPU28aCwyykqmxo0JIacz0TjJ9T6/yg39e+3V7AeZNmklS5vcfzzS2bcadNJ6b6O0kxFfQJDgL1XStst8f2PM70xCBb3A29xnFkUoiKlmf7HTfA/NjZzK/ai94d3aRiR+GVXBL9k7BXmDF1WeLcc/GbSHlw1wZOS8um4SAVr/e4WhhvtnRLKgLEGYyYtDp2tTURVlXMWh1xBiOf7dtL4hicrQigtjiiHcKIMKjE4quvvsqKFSswGAxdjrvdblatWsWPf/zjSMQmhBBCCCGEEGNaq3fsLIP2l32Fv3IzCVf8E+db/6/XdqHGchStnrYXbiBQsxtd4iQsy3/eMYNRo8V6wtW0PnsVKBqsJ1+HotHS/snfsJ78q0N4N13VuKDGZQGmYtNPJTMuALpGyt2ltAZchzyeWreHOnc+85OOoMn5FL5Qc5/XqKisDhVxwbjJWPft6bFNzaQqsqsndjuus3VPLDZbe56V164U9xrDnAQjVS339zsHl2BM4qTQeBILt/XvgmFgikkhwTSD+NZkYks0aP3fnjl0hVc2NNawqbmOl5as5N7tn/fabo+rFZ1Gw8+/fI+dbY1kWuxcN2Mhs+KS0Soarp2+gJ+sfwNFUbgpbzFaRcMTxQXcPHPxIbuXEcXnRw2FULTaaEcSVf1OLDY3N+P1egG4+eabmTp1KvHx8V3a7Ny5k4ceekgSi0IIIYQQQggRAQ7/2Kj6qgZ9uN76PbZTfoWi7z4T8UDBpgrCXieWuacTc9zP8Ba8Rts/ryH+yn+htadgXvB9jLNPQVEUFEMM/tINaGLi0CVOwvHSbQSqd2CcsRTLidegKMM7S6wn7YFvC8CkolFSybGr2MwOmgKVlB/CAjAq8FVjiATjlUy176Sq7dU+r/GHfaw2NXGePQVjW3238/WtG0hPmISxuWtCXGvovmR0r6n7e3tyvEphe0WPY2fHxtDifBCUvj8TCgpHx84lr7wIne/gy6ojTVG0xNqnkRDOIr7aRkxhdD/DvlCQe7Z9xs0zj8LUw0zEA5W52nAG/KxMz+F/cubxcmUhl3/xNquPW8l4s5ULMnM5Y2I2CgoxOj2fN+wl3mgiw2Ln15s+YGvLPpanZnLDjCOj8tmKCrcXbJZoRxFV/U4srlu3jptuuglFUVBVle9///vd2qiqynHHHRfRAIUQQgghhBBirHL4xkZisf3jv6FLnY5hyqI+29pOuwk14ENj7Pgyrxt/A4GqrXi3vYPlmEsAOs8BtH/yd6zfuw7Ply+hhkMkXP0Crc9ehX/3RxhnHD88N9RP+wvA2IFZpJhmMdHuw6fUUdq+B0/IN+wxNPt8bPBNYXbCLXg9/8AV2HvQ9o5AK28mpnGmz4bW27XAihoOUpfTQMYXiV2Oa5TWbv3s0YW7TdrTGyuhh1tOjTFBoH/7Qqaa01juthB7kGIzkaY32kmw5BHvSsU+TIVXBusvxQXkxiVxdHL3maTfdeesY/CGglj1HatTb52ZyOaWet6oLuGn2XMBsOj2r1x9ongzN+Ut4oWKnQTDYf679Ptcuv5N1taVszw1a1juZ6RR2z0okljsn7POOou0tDTC4TCXXHIJjz32GHb7/ipBiqIQExNDTk7OsAQqhBBCCCGEEGONwz82lkL7dq4h7Gqi4YETOg6EOtaLNuz6kOSb1nZpq2h0KMb9X2UVRUGXmEHY2X1fPv+eL9GYYtGnTsP98dMYsheh6I3oM+cTqNoa9cTidzV7odlrpLMATFwYk7GFal8Zdd7e952MhK3NPqz6HzIrrpqK1ucO2rbWU80H6VNZvseLEupa/brGtZ4082noPPuPKf7uMxZ3KKEuebdUK+xo29VtmXOsXk+CZjUt3u4zJA+kU3Qstc4hp3QbSqjmoG0jwRqbRYJmKnENw194ZSjeqdlDk8/Donc69qUMhDs2bX2/towvTr6kS1udRoNVsz9xqCgKWdY49nm7J3S/aKzGrjcyw57En4sKOGbcRExaHQsSUyloqR8ziUXc3mhHEHUD2mNxwYIFAPzjH/9g3rx56HRS+0UIIYQQQgghhstYmbEYd/EfILy/So1r7Z8AsJ5wdbe2rf/4OfqMfCzHXQaAqoYJ7ivBfMQ53dq6P/kblpN+2fFEUToKugCoof2PR6hAGIqaNUAikMgkK4yzunGGqyl1lRMk8lV9XIEA6xvHMd1+G9rQi7R4C3ttW+QqJjZ7NgsLt3XJBYYC7eyb2c6ELw+YxeVs6XqxAUpC4S5JxETbPiq/k380aDRkW9ZT59p50LizLJNZ2hIipmZTH3c4eBqdifjYPBK8E4mrMGM8hIVXhuLpRSsIHvBef2T3lwBcO31Bt7aXrX+LBYnjuTJnHgBhVaXI0cwPMrpXaX+iuIBf53bMMNYoHW2ho0DQCP9oRZTq9vTd6DA3qMzgwoUL+eqrr9i0aROBQAD1O++an//85xEJTgghhBBCCCHGsrGSWNTGpXZ5rhhiOo4nTEQNh1DdrSjmWBStHkPO0bjX/R3d+By0iZPwbFxF2OvCOGdFlz78ZV+hGK3oU6cDoJswA9+uD9Bn5OMv+oyYpT87NDcXIbUuqHXFAFOx6KeSGRdE0TZS7imlNeDs8/qB2N3mwag9k3kJDipbnkIl2GO7rxxbicuZz7Sirgm96sAXjNeegObb3KfLBfogBDpSEKEEBfWAXJzdCDscXQusKMC8+HIq2z7uNU6j1sRyUy4ZpVtQ1MjP7jXFjCfBNIOE5mRiizRoAiNniXN/TYixdXlu0ekBmGSJJaSGafF5sRuM6DVajktJ58nizUy3J5JpsfOvsh04g37OTJ/apY8NjTVYdQZyv6kEnWdP5v3aMo5ITOXjfZX8T878Q3NzI4AqMxYHl1j84x//yOOPP05sbCxWq7XLOUVRJLEohBBCCCGEEBEwVpZCH0zYsY/mx8/BfvEfMGTOw3zk+ahBP653HyLsakGflkvcDx/tsq8igPuTv2NZvv+7qXnhuQT3bqP1mSsw5p6AMXfZob6ViHEHYGeDDhiPwnim2sEe46ApUEV5e1WXpN1g+UIh1jdYyLTdRJzmPfa1b+yx3Rrn19iy8plQtnn/tZ5GmmYGSN7SkcRCBX1iG4G6jr0X3faufWQmtPFVW9cl1Ucmu6lsebHX+HJt0zmqrhXj3oKB31wvFEWL3T6NhPBk4vZav1N4ZfQkE/urztPOig9X8dSiFSxITOXirJn4wyEe2LGeJp+XWXHJPHHkyV32VQR4sriA62cs7Hx+QWYuW1rqueTz1/le6mROGivLoAFkxuLgEovPP/881113HVdccUWk4xFCCCGEEEII8Y2xMmPxu2LPvK3zsTYuleTbP+98rigKlmMu6SzU0pu4H/2xy3ON0YL9ggcjG+gIoALlbUBbLJBHsimPibE+Ako9Je2lQy4AU+70oFWWcETiUdQ6/kow3N6tzWu+rZyfNo246v1Lp/fqN5HMkZ3PdVYHAToSi43W/e9rkw4K27d06e+IJC2VLX/uMR6rPpaTtJNJLdrS4/mB6ii8MpN453ji9ujRuUffrMSBuHvOks7HaTE2tpx6WedzRVH4afbczkItvXl68aldnlv1Bv6w8HsRjXO0kBmLg0wsOp1OTjvttEjHIoQQQgghhBDiAA7/4ZncEMOnxQstXiMwCb1mErlxYczGVmp8ZdR6uxe46Y+QqrKhUUtqzLWkGb+gxrn2O+dDvKKt4vzEicQ0dVSVbneW0zptIXGFHdMntcb9GyhWG/e/r6clefjKsb84SG6cmbrW3/UYx/zY2RxRVYXOPbSkojV2MomaqcTVx2MtUlE6w5HPmxggSSwOLrE4b948CgoKSEtLi3Q8QgghhBBCCCG+4fDJUmgxeIEwFDdrgAQggXQLpNjcuNQaSl1lBNSBFYCpdXupc89lXtJ8WlxP4w02dZ7zBNt5zRbDSm88hvaOYi1743cRRy4AGnV/YrFU11ERWqtApW//3oqTrGY8nj8Qpuuy6ARjEieFUkgs7LoPY39pdebOwiv2ctMBhVckkSiGRm2XpdCDSiyedtpp3H333Wzfvp3JkydjMHRdb3/WWWdFIjYhhBBCCCGEGNNkxqKIpLp2qGuPAbKJ0WWTGRdEo2+iwrOHZn9bn9dDRyru68YQCcYrmGrfRVXb6s5zTb4G3kvN5JQKL9qAh9aWbbRPysNSqaL4Wzvb7fimovX0pACb25sBSDQaiQn/C0dofzsFhaNj55JXXoTOd/DK0N9lsqSSaJhBfEsSsYUKms76M/KZEhEkMxYHl1i89dZbAXjmmWe6nVMURRKLQgghhBBCCBEBMmNRDBd3EHY26oAUFFLItkNcjJPmYBVlrso+C8A0+3xs8E1mdsIteD3P4QpUAVDRXs5nWbkcW1yIooapTisnpzIDXK0dFxqgVO1I7rWpHXsymrU6Jhrfp8Fd1tl/qjmNE90x2Ao39+t+FI0Oe+x0EkKZxO+1Yi6UBKIYfqoUbxlcYnH37t2RjkMIIYQQQgghxAFUVcUT7LudEEOlAhVtUNFmA3JJNOaSbvcT1NRT2l6KO9T7rKytzT6s+ouYFVdDRes/ANjm3Ik9Zx5zCgtoaPmSjKQsjI0uFGOAYELHisepCWF2uveiVRRm2bez1/ElADpFx1LrHHJKt6GEAr2OC6A3xZMYk0e8czz2Ui26zhyPJBXFIeL1RzuCqBtUYlEIIYQQQgghxPAKSW5EREmbD9r2GYB0dJr0zgIwdf4Kqj313dq7AgHWNyYz3X4b2tDLtHh38aljE/bseWSWFFCTXU9WYzL6eAdt9qSOi/QV4IWFiQ1UtL4JwGTLZJa2BDHXbOolMgVb7GQSlKnE18dhKQqj9DW1UojhpMp/1INKLC5btgxF6f3Du3bt2l7PCSGEEEIIIYToW1i+r4oRIPidAjATLTDe5qZdraXEtadLAZjdbR6M2tOZl3AcVS1P81Z7AedNmom2Zj0TrWeitbXRaElkog12Ogs5MilEReuzGLUmTjTlMam0AOU7iRqtLqaj8IonjbhyE4YuhVckqSiiSw3LdhWDSiyeffbZXRKLwWCQ8vJyPvnkE6655pqIBSeEEEIIIYQQY5UkFsVIVN8O9e0xwBRMuinMiAui1TdT6Smlyd+GLxRifYOFTOv/Eqd9j9WeLVyQMJF6i4PUfW1Um1Ts1jrmaI1UtdxPXuwMFte1YNy7f5ai2ZJGgmEaCc1J2HYraDpzl/KhECOM/Ec9uMTiL37xix6Pv/DCC3z++edccsklQwpKCCGEEEIIIcY6+b4qRjpvEHY16oBxwDimxEK8xUlLcC9lzkqqNEs4IvEoXvf8h5hwMamaRdSaQnjUYrTepzjHPIfxRVtQNDri4meTEMwgrsoihVfE6CFLoSO7x+Kxxx7L7373u0h2KYQQQgghhBBjUki+sIpRptIBlQ4bMIME4wzSbX48gX24lQTWjdtMVrgCR9wU8vQfckRTDCkaGwm67xNbqkPnPXCJsxCjhCyFjmxi8d1338VisUSySyGEEEIIIYQYkzQh+KlqiHYYQgyOF/AaACsaJYsYwyKKTAVM1K0nzrGYQPBYqlGoNgF5UY5ViEHS67Qsi3YQURax4i3t7e20tbX1ukxaCCGEEEIIIUT/mcMKp3yqiXYYQgyaYvJjSq9GZyinLmUHL8ZN4/R98Pq4Amrt6bQFJ1Di8OGXWV9ilLIbjJJYHMxF3y3eAqDX65k7dy5HHnlkRAITQgghhBBCiDFNcopiFNIlt2JKrkQbqID6vXjCKrtTtrAmxURd6GTi62dzTmAPX9o38r6mhlSdn/HW4wlpplPu0tPo9UX7FoToN40ilckjWrxFCCGEEEIIIURkKJJYFKOBLoRpYi0GSwVKazm0tkIFhLVQM99BpWstO8blsU8zjVDAitM/nkmVOXgnO0lor2BrZjbrHW+g8joGYE7sfMzGhTT6ktjj8BCWPRfFCKaVxOLg91jcvn07Tz/9NEVFReh0OrKzs7nkkkuYPXt2JOMTQgghhBBCiLFJEotihNLGujFNqERHOdRXQkMAGvafd2UoFCd/QXtbOdWT5/KBcxM60wqSFDONVpXxrflM2VCLb2Eb+YVbmJw6lffNTuo9tTS4vwb31wBMNqeQFLMUnzqZEqeKMxCIzg0L0QuZsTjIxOLGjRu59NJLycnJ4eijjyYcDrNp0yYuvPBCnn32WebPnx/pOIUQQgghhBBibJHEohgpFBVjWgMGeyUadxk07IOq7s1CRoWq/Ab2tn4IzjAtadN53buVibGnsLHZg9looNqoMqU0E0NaPNO25LItzwG1xazUGijMns/Hzs2E1BAA7kA9lW3/ASAWHTlxx6DTz6XGbaWq3XMoXwEheiQzFgeZWHz44Yc555xzuOuuu7ocv+uuu3jkkUd47rnnIhKcEEIIIYQQQoxZ8n1VRJFi9mGauBe9vgKlsQJa3NDSe/vWaSolMR/jbakDwJ04idWaSkLBEM7wfMBDk1uhWBdiiaojaJuLruVDZpQtZmu6E7+niRmFm5iUlMGHcVDRXt6lf5Ugta6PgI8AmGGZQlzMEhzBCZQ4AvhCoWF4FYQ4OJmxOMjE4s6dO7nnnnu6Hf/hD3/I97///SEHJYQQQgghhBBjnaJROr6xBaMdiRgrdONaMCVVoQ2UQ1011PRdrTlgUyifWUl98+fg/uaYJYH/2trx+NoZZ1nI5lYPMVoTbW7YoYQAHe0lOdjjN2BqcpNnOZGttv8SCnqwNFZwapOG8inzWePZjj/cczGXNl8pbb5SAFK0FsbHHY+qzKDcpadBCsCIQ8Sg0UY7hKgbVGIxPj6elpbuf6pobm7GYDAMOSghhBBCCCGEEKCJUQg7pHiFGCa6EKb0WgwxFSitZdDaBu39v7xxVpBSzQcEmvfnB0J6M++kWmlurwBA0Z4A+BlnTKLaCRWqCibAqyOUPBtt+xdYKlWmzziNncorqGoIRQ2TVbKJi+3j+Swllt3OwoPGEQy3s7ftDeAN9MDs2HwsxiM7CsA4vYRU+QyJ4WGXHNjgEovHH388d999Nw899BBTpkwBoKSkhHvuuYdly5ZFNEAhhBBCCCGEGKsUC+CIdhSH3o1bryFOH88tM+7q8fxPvvwBpe3FXY49c8QqJluz+bhhLQ8X/w6touX6qTdxdNJxnW2u+PpH/CrnFnJs04c1/pFMa2/vKLyiVnQUXtk38IIovgSFPTlFNLVs6nJcVTR8mpVJpXMnAHbjFLa2dPQfq4+j+pt2wQQFXY2KuzwPm/5rCASI36VhyrzTKGn/b2d/prY6TmirY8bkubwXKKE96OpXfI3uAhrdBQBkGseRbF2KT51CqUPFMcYLwLie+TeKxYLl3DN7PO949AnCdfVdjtl+eSXa8ePwb9+F57W3QaMh5sxT0M+Y1tnG+cenMJ99KroJqcMa/0gTazBGO4SoG1Ri8dprr+UnP/kJp512GjabDQCHw8GMGTO48cYbIxqgEEIIIYQQQoxVGotCiLE122pt/bt80fwpJ6ec3uP5kBqiylPJY3P/Sro5o/O4XR9HSA3xf4X38oupN6CqYR7YfRevHb0WRVFY3/QpiYaksZdUVFSMafs6Cq+0l0FDA1QOritVUanP91Hmf59Qi7vb+a05s9nuKOh8bjOfRai94/1rwNZ53GWHuBoIt5tQ83JRyrcAMH6TCe+i5extWdOl3wl7tnCRJZ4vJ86iwLFtQDF7gvuobF3VEQ86psYdhU6XT63XRqVrbBWA8W/ZTrCwBP28OT2eV8Nhwo1NWC+/BE1SYudxJSYGNRzGs/oNzKedDKqK+6XXiL3tBhRFIbC7GCXWNuaSigCxMmNx4IlFj8dDbGwsL730Ep988gnFxcV4vV5mzZrFsccei0YjpcuEEEIIIYQQIhI0lrFVGMARaONPex5hui2v1za1nmqC4QAzbDMxarvOFmr2N9EWbOX45BNRUbl39x20BlqINyTwbPmTXJ9zy3DfwoigxPgwpX1TeKWhHFo8By280h+e8QolGVtpa93V4/my7Hw+deyfwWjSJbKtZf8moeGQufNco0Ul7pvH7rrZWJSt8M1y5YwNifgWLaSheWOX/vXtLRxV2EJO+ize01TT4m8e8D10FIBZB6wDYLplMvHmY3GGJlLiCOA9jAvAhN0ePG+vQTtxQu9tmlshFEI7MQ1F3zVdFHa6UN0e9LNyARX3i6+itrtRrBa8H6zDfNaK4b2BEcouMxYZUBbwjTfeYNmyZezYsQONRsNxxx3HT3/6UwoKCrjxxhtZu3btcMUphBBCCCGEEGOOMsYSi38qfZjvpawgM2Zyr23K3WWMM6Z0SypCx6xFk8ZEkXMXhc5dmDVmYvV2NjR9Rrwh8bCerahPacaauwV79n+J1f4NQ807KBW7wD20WXlhLexd6KAg4ZVek4oNGTN5u72gy7EU23l4QvsrD7n8+s7He437Z+EGm2IhPbvzuaJC9tdZxNr3L7M9UFLVdn5Q5+eo2HyUIZZOd/j2UNH6LM3Oexmn+QMLE3azIElhnNk0pH5HIu9b72PIn4VmXHKvbcL7GlDssd2SigCKJQb0ekI1tYSqa8GgR4kxEygsQbFaxuRsRZAZizCAGYsbNmzgxhtv5KyzziIlJaXLuVtuuYWnnnqKa6+9lueee4558+ZFPFAhhBBCCCGEGGs0lmhHcOh83bKRLa2beGbBKh4sur/XdhXuMnQaPf+79RoKnbtIj8ngqinXkhs7E62i5YrJ1/DzzT9Fg8Ivp96IVtHyTMVfuW7qTYfwbg4BfbCj8Iq5AqWlDNoc0L8tCPvNmQElyRtobyvvvU3KFF4NFqIesGRfp5jY7YgD/J3Hmj37k4CluhBLD0hHeBxzMbN/z0ytH6bvzmdrtgNve223MbW+dvILNzM5dSprzC7qPDWDu8EDBFU3ex1vAm+iA2bFzsFqXESTP5lSx+guABMoLSNYVoHt2itxv/pWr+1CDY0oWi2uZ54nVF2DJjkR8yknoktPQ9FoMJ98Aq4nngFFwXzGySgaTcdsxTNPOXQ3M8LE6mXGYr8Ti08++SQ//OEPueWW7lPHp0yZwv33d/zH/+c//5m//vWvkYtQCCGEEEIIIcaosbIU2hfy8X9F93Jdzk0YtQefLVbpLsMZdHBa6tlcmnUVb9Su5rotV/KPBS+RYhrPORPP55TxpwMKMboYNjavJ04fT3pMBnfu+F92OLaxNPkE/mfK9SjK6Hp9tXEuTKmV6MIVUF8F9cNTiCRkVKjM30d160fgDPfazmcfz2pjA/6Av8vxNPu5rG/cf8yiM9F2wJaMW9UQB6Yj/NXjMGdPhNq9nccMDpW8muPYkvwmQb+zx/HttcWcrTVQOGUeH7u2EFIjt5S5yb2FJnfH3o8ZxkTGWZbhZwqlToU2v7+Pq0cONRDEs/pNzGeegqLXH7RtuKER1evFsCAfzYlL8X+5CddTzxF73VVo4uwYj1qIYf4cQEExGggUlaJYYtAmJdL+75cIVu7FMHMGplNPGnWfrcGyGyWx2O/E4s6dO/nf//3fg7a58MILufLKK4cclBBCCCGEEEKIsbMU+pmKJ5luy2VhwlF9tv11zu34wl4sOisAOdbpbGvbzHv1b3JxxmUAxOj2T/V8tvyvXDP117xS/R+CapB/LXyFX2z+KR83fsDS5BOG54YiRRPuKLwSW4HGVQ6NjYMuvNJfrdNUSmI+xttSd9B2QbONNxIVnJ62ridUhUpPBrB/CfY4QzJ7D2iyV1XB3KUJvnA+xi6twFynkmtZwTbDK6jhnpOompCfGUUFTErK4MM4qGgv7/MeB8obbKKy7UUArKqGyXFHYdTPo9YTS8UILwDjXfsx2rRU9DnZfbY1n3065kAAxdSRLNNOWEGwogp/wVZMxx8LgHJAIs37wTrMp5+Mb/2XEA4T+6v/wfXEswR27MYwc8bw3NAIY9fLUuh+JxZ9Ph8m08H/chQXF4fHM7I/VEIIIYQQQggxWoyVGYtr971Ls7+J7607GgC/2pFE+rhhDe8u+axLW51Gh05j7XyuKAoZMVk0+PZ16/er5g3Y9LFMs83g7+VPsCjhaIxaE/lxC9jWVjAiE4uKxYs5bS86XQXKvnJo9sLA65QMWMCmUD6zivrmz6B7wecuVK2eDyamUOcq7nYu3X4aG5q75gVidXHd2gUTFHTV+5cXe8vSMaYnQFPXm40thZzZZ1Doe/mgMVkaKzi1SUP5lPms8WzHH/Yd/CYGSVXC1Ls+BT4FYHpMBvGW43AF0ylxBPCMsAIwga07CDtdtN75zfYCwY74WrfvJO6um7u0VbQaOGDvUkVR0CYnEXZ0nzEaKNmDEmNGl5aKd+3H6HOyUfR6dFMyCZVXwhhJLMoeiwNILGZlZVFQUMCkSZN6bbNp0ybS0tIiEpgQQgghhBBCjHVjJbH42NwnCYb3F/r4y57HALhy8jXd2v5y8+XMjZvPTzKvACCshil1FXN22nnd2j5T8SS/yP4VABo0hOlY1htSQ6gjaM88/fgmjIlVaL1lsK8Wqg9tbI2zgpQqawk0t/bZVgU2Zk+n2LG1x/MtoTl0mYoI6BVrt3auWIirPrBjhUDMPPRNa7q1Td6qw7fwFMrb3j5obIoaJqtkEz+yj+fTlFh2Owv7up0hc/grcPj/0RGnxkSKfSmKZiaV7UbqPN5hH78v1p/9CDW0fzm7952O19d08vJubV1/fRZdViam5ccBoIZVQnX1GBct6NbWu3Yd5tNO6niiKJ1VvQmHGUEfrWEnVaEHkFg844wzePTRR1m0aFG34i0A9fX1PProo5xzzjkRDVAIIYQQQgghxipljBRvGW+a0OV5jLbjxifGTCKkhmj1txCrt6PX6DkqcQnPlj/JVOt0JsVk8NLe53EFnd/sq7jf1y0bseqsTLPlAjA9NpeP9q0hP+4IPm9ax2VZVx2am+uJPogpvaaj8EpzGTic0PM2gsPKl6CwZ1oRTc2b+n1NYc48vnJ83eO58daj2dTafRVjKGTudqzRohL3nWPukmzsievB1d6t/cSNsXgXH0td8yd9xmhsq+OEtjpmZM3lvWAJ7cEIV7XpRVD1Uu14B3gHDTDTNgubaTEt/nGUOHwE1d73qxwumvi4rge+WcqsTUpADYdR290oZjOKTotueg7eD9ahnTAeTXIivs82onq83+yruF+gtAzFZESX1vG51U2cgH/bTnSTMwjsKsJ00vGH4tZGhFhJLPY/sfjDH/6Qd999l9NOO41zzjmH/Px8YmNjaW1tZdOmTaxevZrMzEwuu+yy4YxXCCGEEEIIIcaMsTJj8WD2eev5wYbTeHTOk+THH8F5Ey/CH/bxaPHvaPE3MyN2Jg/N+XOXfRWhY2/Fq6dc1/n8nLTz2d62las3/YRl407k+OQTD+l9aBOcmFIOLLwS7PuiYaIqKvX5Xsr8awg197Hu+QDVk+ey1tlzUhEgpDkO6L4Eud3fvWhIlTFMNpquB4NaQklz0bo+69YeYPLGCfgWzKalpefZkt81oWwLF1ni+XLiLAoc2/p1TSQ1e7bR7OkYN92QQIptGX41mz1OhdYRUAAm3ObA+fvHsPzsR+gnZ2I8ZhEEg7hffwfV5UKbnob1sou77KsI38xWXLH/82NcvJBgRRXOP/8dw+w89DNzD/WtRIVBo8Gs63da7bDV71dAq9XyzDPP8Mgjj/Dyyy/zzDPPdJ5LSkrioosu4qqrrupzH0YhhBBCCCGEEP2jiVVAoWP96Rhyy4y7Oh+nmiewbun+GXWKonBxxmWdhVp681j+X7s8t+is/H72Y5EN9GA0YYwT6zHYKtA4y6GpadgLr/SHJ1WhZNIW2lp3D+i6lrTpvO7tPaEXb5rGtuaek2VN7u4J8mJdmOO/m1gE2stmEGvcCP7uxVo0IZi2JZdteQ7aneX9ilvf3sJRhS3kpM/kPW0tLb6mfl0Xab5QM5WtLwEQoypk2Y/CaJhHnddOhdNzyD7ilnPP7HysjY8j7v47Op8rioLp+GM7C7X0xnb5JV2eKyYj1h9fGNlAR4FEU/eZuGPRgFKrBoOBG2+8keuvv56qqira2tpISEggPT19zJQSF0IIIYQQQohDRdEpaOIUwi1jLLM4SmksXkwTq9BrKqChApq8EJ08VjdhLVTPb6PK9QHh1oHNlvMkprNaU0ko2HthkhjTGYRd3Zf62nQxOHqYFLlNDdFTSkJ1GwlnzURTXtDjODqPyoyyxWxNd+L39P/FTarawQ+MFrZl5vO5YzNqNLP1ikp9+2fQ3jEzc6o5nUTLUtrD6RS3hfCEojebVfRfmqX73qFj0aDmbOp0OrKysiIdixBCCCGEEEKI79AmSWJxJDOMb8KQWNlReKW+DvaOvJ+VMwNKkjfQ3lY+4GsDlgT+a/Pg8XXf9/BbMfoUtrb0PNko2ZhEVQ/Ha1QVYuixArW7ZjZWzRYI97wnoakpTK7lRLbZ/kso2H1Px95ofe3MLdxMVmoOa8xO6jw1/b52OLkCVbhanwMgSWNkfOxxKNpZVLlN1LqjXwBG9GxCTPQTi6qqcscdd/D666+TmJjIiy++yDXXXMOWLVtYsWIFv/vd74Y9BlkMLoQQQgghhBAjmDZJQ6D40Bd9EL0wBDBPqkFvrEBpLo9a4ZX+CBkVKvP3Ud36ETgH/h4K6c28m2qjqb38oO2SrOdS0tDzbEabLq7X64LxCjp390RsqMWKmpuDUtH7cm1rpcr0GaexU3kFVe19JmVP7LVFnK01UDRlPh+5NhMa4PXDKaT6qHa+B7yHAsy05mEzH0VrIIXitugUgBE9GwkzFnfv3s2qVat48sknmTZtGq+99hrl5eW8+uqrxMfHH5IYJLEohBBCCCGEECOYNlm2nYo2XYITY0olulAZ1O+F2pGTiOpN6zSVkpiP8LbUD+p6VdHw2eRMKhw7D9pOp7Gwq80G9Ly8Wk/vyRenHeKrez7nbZmLmYPvAxm/S8OUeadR0v7fg7briSbkZ3rRJtKTMvgoTqG8vWzAfRwKzd4dNHt3AJBuiGOc9XiC5LDHqaFlBBSAGcsmjIDEotPZ8VeNJUuWoCgKLpeLzMxMpkyZcshi6L5TqhBCCCGEEEKMcHv37mXatGl89NFHLFu2jPz8fO655x6KiopYuXIlc+fO5YorrsDlcuH3+7n//vs59thjycvLY9myZfznP//p7GvZsmX861//4rzzzmPWrFmceeaZbN++vfN8VVUVP/7xj5kzZw6nn346Tz/9NMuWLes8X1BQwAUXXMDcuXNZtmwZzz//fETvVZsoX9sOOU0YY0YttrwvsE96HovvH+gqP4LqCjjIPoMjQcCmULy4iu2swuseXFIRYGvObLb1kVQESLOfR9tBElyhUO8FXhssvS8b99cmQtqkPscfv8nExPgT+mzXG0tjBStKKznVMh+Dxtj3BVHkC7VS1baa2rbfYQ49QL79MxYl+8iymZE/Pxx6kU4sVlRUcNlll5Gfn8/SpUv5xz/+AUBpaSmXXXYZ8+bN49hjj+UPf/gD4XCYDRs2cPHFFwMwffp0Hn/8cR5//HG+/PJLpk2bxoYNG1BVlT/+8Y8cc8wxHHHEEVx55ZXU1OzfAsDhcPDrX/+aefPmccwxx3D33Xfj9Q5s+b3MWBRCCCGEEEKMWk8++SR/+tOfKCkp4Ve/+hXr1q3jzjvvxGQycfXVV/PSSy/hcrn46KOPePzxx0lMTGT16tXcfffdnHDCCSQlJQHw+OOPc8899zBlyhRuv/127rnnHl544QWCwSBXXHEF2dnZvPzyy+zatYs77rijc4lZaWkpl1xyCT/+8Y+599572bJlC3fddRdJSUmceOKJEblH7ThJGRwKGqsHU9pe9Jpy2FcBjT5ojHZUA9M4K0Cp8gGB5tYh9VOWnc+njk19tlNUDeXtE4DeExEun77Xc1WGMDkHme/kC+Rj7Ecp7YwNSfgWLaSheWOfbXuiqGEySzbxI/t4Pk2JZbezcFD9HFKKSn37F9D+BQBTzRNJtCzFHZ5EsSOEOygFYIbbxAgmFn0+H5deeil5eXmsWrWKqqoqfvWrX2G327nvvvtYtmwZL774ImVlZdx2221YrVYuvPBCHn/8cX7xi1/w6aefEhMTg9vtpqCggMcffxy73c4///lPXn/9dR588EGSkpL429/+xqWXXsrrr7+OXq/n1ltvJRAI8Pzzz+Pz+bjnnnv47W9/y3333dfv2CWxKIQQQgghhBi1rr76aqZPn8706dO57777OPXUUzn66KMBWLx4MXv27GHJkiUsWrSIuXPnAnDllVfyxz/+kfLy8s7E4tlnn83y5csB+MlPfsIvf/lLAL744gtqa2tZtWoVVquV7OxsioqKePPNNwFYtWoVubm5XH/99QBMnjyZ0tJSnnrqqYglFnUpMmNxuBhSmzAkVKD1lMO+kVl4pT98CQp7phXR1Nx3MrAvDRkzebu954rM35UedxZfNB18dlOjp/fEeJEuzAkHSSx6yyZizEyGhoaDjqGokP11Fr65bTjaBp8UNLbVcUJbHTOy5vJesJT24AjdPLMHrsBeXK3/BCARA7nxx6HRzWJvu5kaKQATcXaDkThj77NxB+rTTz+lubmZ++67D6vVytSpU7nttttobW3FbDZz9913o9PpmDJlCg0NDfzxj3/kxz/+MXa7HYDk5GQAYmJi0Ov1nc+feuop7rzzTo488kgAfvvb33LMMcfwySefkJ2dzZo1a9i4cSM2mw2Au+++m7POOoubb76581hfJLEohBBCCCGEGLXS09M7H5tMJtLS0ro89/v9LF++nM8++4wHHniAPXv2sHNnx/LOUGj/ktbMzMzOx1arlUAgAEBhYSFZWVlYrftnpsydO7czsVhaWsrs2bO7xJSfn88LL7wQsXvUWBQUG6ijJ8cxYilGP6b0bwqvNJWDwwWOaEc1eKoC9fO8lHnfJ9TcQ3nlAXKmTOHVYCEq/UuwNgTy6LGs8zdsuhhcBwlruxqir7REwJiPnvf6jEXrh+m789ma7cDbXttn+4OZULaFiyzxfDVxFpsc24bUVzSE8FPjfB94H4A86wxizUfTGhhPicNHoJdq26L/Mqz9S7r1V1lZWbffNeeccw533nkneXl56HT7Pyf5+fk0NDTgcBz8P6/29nbq6uq47rrr0Gj2J/C9Xi/l5eUoikI4HGbJkiVdrguHw1RUVDBz5sx+xS6JRSGEEEIIIcSopdVquzw/8MvTtx5++GFefPFFVq5cyVlnncWdd97ZZY9EAL2+5+WaWq0WVe2aZDnwudHYfU+2cDjcJWkZCboUDYFBVPUVoEt0dBReCZZD3egovNIfnlSFkkmbaWs9eIGT/vLZx/OqsRF/oH8FQVKtS/m69eDJzGRjElUHOV+nqhDDwXKTuIunYE+xdVTf7oPBoZJXcxxbkt8k6B9aJl7f3sLiwhamps/kPW0tLb6mIfUXTS3eXbR4dwGQposlxbaMIDmUuXQ0+3xRjm50yrDZI9rfgYnDA/X2Owbo8/fMt+cfffRRsrKyupyz2+189dVX2Gw2Xn755W7XpqSk9CtukMSiEEIIIYQQ4jD3wgsv8Jvf/IZTTjkFgJKSEoBuCcOeTJ06lfLyclwuV+dMkh07dnSez8rK4ssvv+xyTUFBQbcvcUOlG68hUCKJxX7RhjBNrENvrUTjKIfmZqiIdlCRE9ZC9fw2Kp1rUVsDEekzZLLxRqKCw9Pa72t8ytEcbG9FAJs2rs9+AokKevdBPothDaH4uWgdn/QrLnOdygzLCrYbXkEND/31SarawQ+MFrZl5vO5Y3O/Z3OOVP6wg6q2VwEwqQpz7QswGRbQ4EugzOEhPMrv71DJsMVGtL/MzEwqKirweDyYzWYAfve73/Hvf/+bpKQkAoFA5x/ACgoKSEhIIC4u7qB9xsbGkpiYSENDA0uXLgXA7/dz/fXXc9lll5GVlYXT6URRFCZN6iiUVFhYyGOPPcb999+PydS/pd6yWYcQQgghhBDisBYXF8eHH35IVVUVX331FTfeeCPQ8QWrL4sXLyY1NZXbb7+d0tJS3nnnnc5KnQAXXnghu3bt4qGHHqKsrIzVq1fz73//m4suuiii96AdL1/dDkZrdRMzrQj7jPew2/+GsfFVNOWbOpKKhxFnpsKW/C+oaHsnIkkzAFWr54P0FOo8NX03/kaCeRY7Wvret0+v9L1c1NmP/Ez7nulg6n/FZnsp5OjP6Hf7vmh97cwt3MxFTGW8eULE+o06RWVf+0YqW/6Ix303k83/YlFSLXMSjFh6mUEnOmRGOLF4zDHHkJSUxB133EFpaSlr167lhRde4JFHHsHv93ceX7NmDY8//jgXXHABitJ3Ya8f//jHPPLII3zwwQeUl5dz2223sWnTJiZPnsyUKVM49thjueGGG9i6dSs7duzg5ptvxu12Exvb//uTd4oQQgghhBDisHbffffxm9/8hlNPPZWUlBTOPfdctFotu3bt6ra31HdpNBoef/xxbr/9ds4880wmT57MypUrWbduHQATJkzgiSee4Pe//z1/+9vfmDBhAjfddBPnnHNORO9BlyqVobtQ1I7CK/EHFl6JdlDDJ2RUqMzfR3XrRxDBJfEqsDF7OkWOrQO6zmg8BdXZ95LyYKjvGU8NMSoJfbRRvQbCU2ahKf+qnxFC8lYdvoWnUN72dr+v6Yu9tpiztQaKpsznI9dmQurhsaz+W+5ALZWt/wYgHj0z4peg1c5mr8dCdbsnytGNLJGesajT6fjTn/7Eb3/7W84++2ySkpK48cYbWb58ORMmTODee+/lrLPOIiEhgUsuuYQrrriiX/1edtlltLe3c8cdd+ByuZg5cyZPP/10Z9GX3//+99xzzz38+Mc/RqfTceyxx3LbbbcNLPYB360QQgghhBBCRNnEiRMpLOxa/fWDDz7o8vyBBx7ofPz66693OXf55Zf3et2RRx7Z2XdTUxM1NTX8+9//7jz/1FNPMW7cuM7nixcvZvXq1YO8k/7RTdL23egwp5j8mCZWdxReaSyHtnZoi3ZUw691mkpJzEd4W+oj3ndRzjy+cnw9oGus+olsa+7fctl2X897lx6oyhhmWj8WU7r3zsSqLYAB7F86cWMs3sXHUtfcv2XU/aEJ+ZletIlJSZP4ME5DeXtZxPoeScIEqHGuBdYCkGudht18DG2BVEocPvxjuACMVa8n3RLZ4i0AU6ZM4dlnn+12PDc3l3/96189XnPg7yuAX/ziF13Oa7VarrvuOq677roer09ISOChhx4aQtSSWBRCCCGEEEKIg7rqqqu45ZZbOO6446ioqODZZ5/lyiuvPKQxaGMVNIkK4aaxtf+ZLqkN07gKtIEKqK8+bAqv9EfAplA+s5L65s8PWtxksGomz2GNc2BJRYAE6zkUefqXVGrsR9zF2jDL+5FYDLVZUHOnoVTs7NfY35q8cQK+BbNpaRnYrMy+xDRWsqJJQ/mUeaz17sQX6ntp+GjW6i2k1duRwJqgszHOdjxhplPm0tE0xgrA5MYn9msZ8lghiUUhhBBCCCGE6EViYiKPPPIIjz76KPfffz9JSUn88Ic/5MILLzzkseizNPiaDvPEmi6EKb0WfUwlmrZyaGk5rAqv9Ffj7AClfECguXVY+m9Nm8Zr3m0Dvs6giWVHqxnoe39Hu86Kqx+Jxa1qiP6mJjxNc4hRdjKQ+iKaEEzbksu2vDbanZF9MylqmKySAi62p/BZShy7nJGp0D3S+cNO9ra9BryGEZgTu4AYY0cBmD0O72FfACYvPinaIYwoklgUQgghhBBCiINYvnw5y5cvj3YY6LO0+L46/BKLWpsbY1oVesphXyXs67uozuHKm6hhz9TdNLcUDNsYnsR0XtFUEQoO/L2Uaj+P8ob+FY1JNib2Kye8T1XBArT33TZQnwDTsmDvwJYf6zwqM8qOYmu6C7+naUDX9oexrZ5lbfVMz5rLe8FS2oPOiI8xkjW4vwT3lwBMNqeQZDkebziLEqeKKxCZIkMjycwESSweSBKLQgghhBBCCDEK6LMOk8rQiophQgPGuEo07nJoqIeqaAcVXaoC9fM8lHnXEGoZhnXP3whYEvivzYPH148s3ndo0FPqTAH6t+TXoovrf1wJCvr2/s1y83rnYmLg+xqamsLkWk5km+2/hILDU4hkQtkWLoqJ56v0WWxyDHxG6OHAHainsvUFAOLQMy3+GHTaOVR7rOw9TArAzExIjHYII4okFoUQQgghhBBiFNBP0nR8gwtGO5KBU0x+TOl70eu/KbzS6obWaEc1MrhTFUombcbROrzLaEN6M++m2mhqLx/U9RPjzuaLxv7vI6jH2u+2jljob6rGVzEBU1YK7Bt4MRtrpcr0GaexU3kFdZgqOuvdLSwubCEnfSbvamtp8UV+huRoESZArfND4EMAZlinEmc6FkdoAsVto7MAzESLlThj39XOxxJJLAohhBBCCCHEKKDoFXQTNQTLR8eXcV1yK6bkSrSB8o7CKzWRjfvnG98l3mjm7jlLejx/7rrVFDmbuxx7acnZTLUlsKa2nPt3fI5W0XDLzKNYmjKps81Fn73GbTOPYoZ9eJc7hrVQPb+NSuda1NbhXS6qKho+m5xJhWNghU8OVOfLAfo/4ywY7H/ypcGi9juxCODX5WPgnQFcsV/8Lg1T5p9Gieu/g7q+vxKrdvADo4Vtmfl87tiMegj2Hax82ovWopB2vrHH86UPefDVdv0cTv6VGdN4DY5tQepW+0EDqSsN2HL3p4v2POYh9RwD5rShVadv8xbT5i0GIFVnJcV6PGHNdCpcehq8o6MAjCyD7k4Si0IIIYQQQggxSugnj+DE4jeFVwwxFSit5dDaOmyFV96uKeWThr2cMXFqj+dDapiK9jb+tmgFGRZ75/E4g4mQGububZ9yY94iwqrKnVvW8dGJF6EoCp/sqyLZaB72pKIzU6EkaT3tbYemMs3WnNlscwx+38Y020l82TKwZawuv77fbSsNYab3ozL0tzylWRhS7dDaNqCYvjX+axPeRcvY2/LBoK7vL62vnbmFm5mcOpX3zS7qPDXDNlbb5iCu3SHs83tO86hhFX9DmIyrTBiT9lc01loU1LBK7Us+xp9pRFVVav7jI+c3WhRFwbkriD5WGXJS8bsCYRd7Ha8Dr6MH5sTOJ8a4kEZfEnucHkLqyCwAI4nF7iSxKIQQQgghhBCjhD5Li2cErYXW2tsxpVahoxzqK2Hf8BdqaPP7eHjXl+QdJPlX7XYRCIeZGZeMUdv1a2+Tz0NrwMdJqVmoKty2ZR3Nfi+JRjNPFBdw68yjhi32kAkq5zZQ3foROA9NgrgsO59PHZuG1Ee7upCBzFYEaBjAVpFF2hAnDSCxSFhDMDYfXetHA4rpQBkbkvEuWkBj85eD7qO/YmuLOVtroGjKfD5ybSYU4WXYIbdK/Rt+TOm9v4aBZhU1BOZ0DRq90uVc0KkSckPsbC0qUPOCn1A76KzQuCbA+JWGiMbbkwb31+D+GoBM4ziSrcfjDU+m1KniHEEFYKQidHeSWBRCCCGEEEKIUSLqBVwUFWNaAwZ7BZr2cmjYd8gLrzy4awOnpWXT4Os9c7XH1cJ4s6VbUhEgzmDEpNWxq62JsKpi1uqIMxj5bN9eEodxtmLrNJWSmI/xtgx8b8DBasiYydvtQ6swnRSTz9bWgSUV7Xor7gEkFreqA0+ytpfmYLdvAPfgCoIoKkz9ejL+uW042ooG1cdAaEJ+phdtYlLSJD6K01DWPvACNL2pf8OPfb6OYFvvs/x89WH0cUq3pCKA1gKKHjzVYVBBMYA2Bly7g2itkZ+t2BdPcB+Vrf8BIBYdOXFHo9PnU+uxUumKXgEYo0ZLTlx81MYfqSSxKIQQQgghhBCjhC5Zg2ID1XnoxlTMPkwTvy28UgEtbmg5dOMfaENjDZua63hpyUru3f55r+32uFrRaTT8/Mv32NnWSKbFznUzFjIrLhmtouHa6Qv4yfo3UBSFm/IWo1U0PFFcwM0zF0c85oBNoXxmJfXNn8PwFXzuxpkyhVeDhUPe20+n/x4wsBljyYbEAa2Cb1JVsAKuAVzk1xMeNxtN+YYBxXYgrR+m757H1mwn3vbaQfczEDGNlZzSpKFiynzWeHfgC/W/IE5P2ktCtO8JMeVXZmpf9vfazrcvjKKFyr958VSFMY5TSDnVgHmSFkXT8bj8T14UBcafZUDRKDSsCTD+7OGfrXgwKkFqXR8DHwMwwzKZOPMSHKE0ShwBfKHhKcLTk2lx8eg0Uf7jzggkiUUhhBBCCCGEGEX0mVr824b3y7RuXAumpEq0/nKor4l44ZXB8IWC3LPtM26eeRSmHmYiHqjM1YYz4Gdleg7/kzOPlysLufyLt1l93ErGm61ckJnLGROzUVCI0en5vGEv8UYTGRY7v970AVtb9rE8NZMbZhyJonSf4dVfDbOD7GENgebB7QU4WD57Cq8aG/EHek809UesIYutzQN/r1l1A5/V5U9QMLgGlgR1V+Ri1X0NwcFvD2BwqOTVHMeW5DcJ+g9Nxl5Rw2SWbOJiewqfpcSxyzm4iuDhgErtyz5Szzb0OBPxQL59KiGPStxCHcknKbRuCFLxpJcpN5jRx2lIOFqPfb4ORQGNUcFVGERrUTAma9j7nBd3ZZjYWVpSTjcM6TMxVG2+PbT59gCQorUw3r4UVZNLhcvAPu/QkrR9yZP9FXskiUUhhBBCCCGEGEUM2ZrIJxZ1IUzpNRhiKlFayzqKYrRHdoih+ktxAblxSRydPLHPtnfOOgZvKIhV3zHb6taZiWxuqeeN6hJ+mj0XAItu/0ysJ4o3c1PeIl6o2EkwHOa/S7/PpevfZG1dOctTswYcqzdRw56pu2luGdoy5MEImWy8majF4Wkdcl/2mLMIugeeVNZhGfA1zlgGVBkaIOSKQc2bgVK+bcDjHchcp5JrPYVt+tWo4UO3n5+xrZ5lbfVMz5rLe8FS2oMDS2w2vB/ANFGDdVrfqZ0J3zcQDhjQmjqSgqY0De7yEK1fB0k+oeOz8O05oGO24pkGmj8LoIYh+0Yz5X/y4twWInb2yEglBcPt7HW8CbyJDpgdm4/FeCRN/iRKHd6IF4CZnZgc0f4OFyPj3SCEEEIIIYQQol8MuVpYPfTkhzauHdP4SnRqOdRXHZLCK0PxTs0emnweFr3zLACBcEdy9f3aMr44+ZIubXUaDVbN/sShoihkWePY5+2+FvmLxmrseiMz7En8uaiAY8ZNxKTVsSAxlYKW+gElFlUF6vI9lPvWEGo5hOuevx1fq+eD9BRqXcVD7sukjWd7qwEGUSwoGDQN+Jp9MeqAE4sA7n2zsSjbYYhJpNgShZzZp1Poe2VI/QzGhLItXBQTz1fps9jk6H+S1LE5SNCpsuvWjr8CqN/8qBzbgsy4t2tyV9EqaA/YKlFRFIzjND3uy+gqCqGNUTBP1NLwfgDrNC0avYIlW4u7fOQkFr+r0V1Ao7sjmZ9pTCLZugyfOplSBziGWABGqygsSB4fiTAPOyPz3SCEEEIIIYQQoke6dA0aG4QHumpTUTFO3Ich9tvCKw2HvPDKUDy9aAXBA5JHj+zuqOZ77fQF3dpetv4tFiSO58qceQCEVZUiRzM/yJjRre0TxQX8OncRABqloy1ASFUHlKtypyqUTNqMo3Vwy1qHSgU2Zk+nyLE1Iv2lxJ7HnobBLTF2+vQDvqbSGGbGQCpDfyPYEAfTJ0NV6YCv/a7krXq8C0+mou2dIfc1UHp3C4sLW8hJn8l72jqafY19XpNxlQkOmLxc/2bH0veUU7vvi1j+Fw+WyVqST+o4p4ZVvLVhEo7q/rNqXOMn5YyOdooC327TqYZVhrhl5yHjCTZS2boKAJuqYWrcMej1+dR6bFQMogBMbnwiNkN095scqSSxKIQQQgghhBCjiKIoGHK1eDf0vRxaifFimliNXlveUXil2QPNhyDIYTAhxtbluUXXkRCZZIklpIZp8XmxG4zoNVqOS0nnyeLNTLcnkmmx86+yHTiDfs5Mn9qljw2NNVh1BnK/qQSdZ0/m/doyjkhM5eN9lfxPzvw+4wproXp+G5XOtait0Zv1WZQzj68cX0ekLy0GihyJgG9Q1zcOYrJmoSbE9waRWATwtudjYuiJRYD0jXZ8i4+hrvnTiPQ3UIlVOzjXGMOOzHw+c2w+aPEdQ3zX10tj7FjKbEjSoIZVQi4VbYyColOwzdDRsMaPKU2DIVlD86cBQh6wH9E1LdReEkJj6pitCGBK1+DYGiRmihbXzhDJ3xt9yTVVCVPrWgesA2B6TBbxliU4gxMpcQTw9qMAzKKUCcMc5eg1ahKLl19+OQkJCTzwwAMA7Ny5kzvvvJOioiKys7O56667mDlzZpSjFEIIIYQQQojhZ8jT9ZpY1Kc0Y0yqROsr7yi8Uj1KphgNQZ2nnRUfruKpRStYkJjKxVkz8YdDPLBjPU0+L7PiknniyJO77KsI8GRxAdfPWNj5/ILMXLa01HPJ56/zvdTJnNTHMmhnFpQkrqe9rXJY7qu/aibPYY0zMklFgIlx57K+cXBJxTi9jfZBJBa3qYMvEOSrSsE0ZQLU1Qy6jwNN3piGb8FsWloiM/tzoHQ+N3MKN5M1fiprYtqp9VQPuI9Aq0rJ/R4yrjRhmaIlYYmOcFCl7lU/QZeKeZKGjCtMXfZVBGh430/Kafs/JwlH6/GUhyn/o4fYOTpiZ2u/O9So4/CX4fCXATBOE0OKfSlocqlsN1Lv6bkAzOKU1EMY4egyKhKLb775Jh9//DFnn302AG63m8svv5zTTz+dBx54gOeff54rrriC999/n5iYmChHK4QQQgghhBDDy5inhW+XKOqDmNJrMZjLUVrKoc0BrigHeAjcPWdJ5+O0GBtbTr2s87miKPw0e25noZbePL341C7PrXoDf1j4vT7HDpmgMr+B6paPwBnditmtadN4zTu04iXftdeTBQx8uShAsjFxUHV/mlUVbMAgCzP7lXwMRCaxqAnBtC25bMtro91ZEZE+ByO2rpiztXqKpsznQ9dmQurBZ9alnW/sfGxI0JD7//bvs6goCsknGDoLtfQm8ypzl+dak8Kkywa+Z+ZoEVTdVDveAt5CC8yyzcZiWkyLP5mSbwrAxBmMzIgfzA6gY8OITyy2trby+9//nlmzZnUee+uttzAajdx4440oisKtt97KunXreOedd1i5cmUUoxVCCCGEEEKI4aexKlgXV6Bt2tFReKV+cHvhiYFrnaZSEvMx3ub6aIeCO3Eir2iqCAUjVyV8YuypbGweXFIRwKqNG/S1/gQFg3NwM2w9pRkYJsZDc8ugxz+QzqMyo2wxWyc68Xujt3+AEgowrWgT6YmT+CheQ1l7WdRiGQuaPFtp8nTMVM0wJjLOcjw5CUeiUZQ+rhy7BreBwSH0u9/9jjPPPJPs7OzOY1u2bGH+/Pko3/xgFUVh3rx5bN68OUpRCiGEEEIIIcShZUxthb1lEJCk4qEQsCkUL65kO6vwuqOfVPRb4nnN5sUTHMz8wN61hfKHdL1WtQ76WkfsEAZWFYLWocX+XaYmldyWk9DqzH03HmYxTZWcUlrJqZZ5GLWH7wzCkcQbbKKy7SVmx0dv79TRYEQnFtevX89XX33F1Vdf3eV4Q0MD48aN63IsMTGRurq6QxmeEEIIIYQQQkSNJi+770YiIhpmB9iU9Tb1zeujHQoAIb2Z91JjafI1RLTfcZaFFLYNfrYiQDBk7LtRL+rNQ9sPtL1kKlgiuz2atVJlevg0UKKfPlHUMJklBVzsjGOGbXq0wxkTdIqOeSlHRzuMEW3ELoX2+Xzceeed3HHHHZhMXbPxHo8Hw3fKfBsMBvx+/4DHGY7ZrNGYITtWxozWuGNlzGiNK/d6+I0ZrXHHypjRGnesjBmtceVeD78xhRhumrRxKAl21Oa2aIdy2PImatgzdTfNLQXRDqWTqmj4LCuTCufOyHeuOwEY+PfqAzl9+kFfW2kIkzeU+U8BHaHkOWjbI5sAjt+lIXv+6ZS4/hvRfgfL2FbPsrZ6ZmTN5b3QHlwBR7RDOmzlJs7Dorf13XAMG7GJxT/84Q/MnDmTY489tts5o9HYLYno9/u7JSD7YjAMTzUjvf7QV0kaK2NGa9yxMma0xpV7PfzGjNa4Y2XMaI07VsaM1rhyr4ffmEIcCprcKYQ+3RTtMA47qgJ18zyUe9cQahlEieNhtDVnNtsckU902k1T2dY89CWfDUN4uYq0IU4Z4sLK9vJcYg1fgT+yy1fHf23Cu2gZe1s+iGi/Q5FatoULY+L5On02XzuiU8H6cLdw/NJohzDijdjE4ptvvkljYyP5+R17JHybSHz33Xc57bTTaGxs7NK+sbGx2/Lovvj9oV7/ej34ydsQCAxu49xojDmUcaMx5lDGldd3eMcdba/vUMYdbfcqr+/wjiuv7/COO9pe36GMO9rudSy9vkKMZJqZUyWxGGHuVIWS9AIcbYXRDqWb8ux5fOr4elj6tpnOIOQa2lLkBIMd5xASi1vDIWDwMx4B1HYTamYeSvnmIfXTk4wNyXgXLaCx+cuI9z1YencLiwpbmJo+k/e0dTT7Gvu+SPTbgvFL+m40xo3YxOJzzz1HMLh/E+L/+7//A+CGG27gyy+/5K9//SuqqqIoCqqqsmnTJq688soBj6MO7f/NQ9anjBndccfKmNEaV+718BszWuOOlTGjNe5YGTNa48q9Hn5jCnEoaKakg9kIHl+0Qxn1wlqont9GpXMtatvIK9bQOGkmb7UPTxLZrBvHthYtMLQ/wiQZEnEO4fpWgFhgiCt73bWzsWi2QDiy//krKkzdNAX/nDYcbUUR7XuoEqt2cK4xhh2Z+Xzm2IyK/OIbqszYHMbFTIh2GCNe9Hcf7UVaWhoZGRmd/ywWCxaLhYyMDE4++WQcDgf33nsvJSUl3HvvvXg8Hk455ZRohy2EEEIIIYQQh4yi1aCdPS3aYYx6zizYkr+eirZ3UMMjL6noGjeZ1aGiYUsWjbN9H09o6DO7LVr7kPvwxQ99U9xgsw0mTh1yPz3R+lSm756HyZI6LP0Phc7nZk7hZn6oTiXVnBbtcEa9Y9NOjnYIo8KITSwejNVq5YknnuDrr79m5cqVbNmyhSeffJKYmMhWfxJCCCGEEEKIkU67cGa0Qxi1QiYoW7SPLcYXaXdWRjucHvnsKaw2NeEPD8+sVJ1iYnfb0BOCAFrVMuQ+HLERCATwtM2NTEc9MDhU8mqOQ2cYmUU9YuuKObuigeW2+eiUEbtQdUTTKFqWpp8a7TBGhVHzDnvggQe6PJ89ezarV6+OUjRCCCGEEEIIMTJosiaiJMWhNrZGO5RRpXV6mBLzOrwt9dEOpVchk403E7U4PK3DNkZa3HmsbxhaJehvBUIDK6jak30xKskRiMVfk4x5ajrUVEWgt+7MdSq51lPYpl89Ime5KqEA04o2kZ44iY/itZS174l2SKPKnKSFJJgi8U48/I3KGYtCCCGEEEIIIfbTLpBZi/0VsCkUL65ku/oiXvfITSqGNTo+SB9Prad6+AZRFSrbJ0WsO6dv6HOXKgzhCETSwRfKj1hfPYktUcgxnD6sYwxVTFMlp5RWcKplHkbt0BO/Y8Xxk86IdgijhiQWhRBCCCGEEGKU087Pg6FvTXfYa5gdYFPW29Q3r492KAelAl9NzaXINbwFQtLjzqDa7YlIX4oKDe1D72e3Zuh7PX7LWzYREhMj1l9PkrfoybCP7L34FDVMZkkBFzviyLVNj3Y4I16MzsqR45dGO4xRQxKLQgghhBBCCDHKKQl2NFMiN/PscONNVNi5qJBC3ysEfG3RDqdPRTnz+NKxZdjHaQ7MilhfCUY73gjkBLeFQ5FLkqsKAfO8CHXWu/SNdsYnHDPs4wyV0VHP8UWFrDTNxaqP0GaWh6Fj0k7CoDVGO4xRQxKLQgghhBBCCHEYkOXQ3akK1M73UDD+NZpbNkc7nH6pyZrLGufXwz5OivUYih2Rma0IkGiIzMxAB0AEc17u0ilgs0auw15M3phGXHzkErXDKbVsCxc1apkfOzvaoYxIx6eP7OXtI82oKd4ihBBCCCGEEKJ3mtk58Mr74Bt5hSSiwT1BoWRiAY62wh7P/2m1B6tZ4Ucn97zv3H3/cFPd2HW/v1t/ZGZCkpbNxUH+84EPjQLnn2Bk1pT9X61//283Fyw3kj5OO+CYW9Om8Zpv64CvG4yQZgkQuUrTVm1kKksD+OIVjG1qZDoLagklzEXr/DQy/fVCE4LpW/LYlueg3Vkx4OsP9ftR525lUWErU9PzeE9bT7OvccAxH44mWDKYnjAn2mGMKpJYFEIIIYQQQojDgGI0oJk9jfCX26MdSlSFtVA9v41K51rUtp6TrF/tDrCjLMSRuT1/JQ6HVfa1hLn2PDPj4vevy7WaFcJhlX+/7+X7xxtRVXjuXS+/u8qCoihs3xPEblEGlVR0J07kFU0VoWDk9hjsTbwpl+3NkakE/S0Nloj11WaDcRHrDdrLphNr3gi+yN7zd+k8KjPKFrN1ohO/t7nf10Xz/ZhYtZNzjTHsyMznM8dmVCKU0B2llqafGu0QRh1ZCi2EEEIIIYQQh4mxvhzamaWwOX89FW3voIZ7Tiq2e1RWr/OTkdL71+HGNpVgGDLHa7Bb9v/TahRcHpV2L8zL0TEvR0e7F1yejmTM21/4WbHYMOC4/ZZ4XrN58QQjUP2kH8ymUwlHOIEUCESu4nB9TGRjUz1GwqmH5rNhalLJbT0Jja5/r8dIeD/qfG7mFG7mYjWbVHNa/270MKRBw/Hpp0U7jFFHEotCCCGEEEIIcZjQTElHSYjcktTRImSCskX72GJchdtZedC2r6zzsTBXx/jE3r8O1zWHibcp6HXdq4hYzQoGHVTVh6naF8agB4tJYUdZENsgZiuGdSbeS42lydcwoOsGK0afytb+T6brN4cvcgsiKwzhvhsNkLt6FmgOTQrEWqEyI3w6KH2PN5Lej7a6Es6uaGC5bT46ZewtcJ2ZdARJ5vHRDmPUkcSiEEIIIYQQQhwmFEVBc0RetMM4pFqnh9k040OqWz4E9eAJqcLKICV7Q5xy5MFncdU1hdFp4M+rPdz0l3Ye/o+b8tqOJcoajcKZxxp4aJWHR1/0cM5xRjQahbe/8HPqAGcrqoqGz6ZMpqK9fEDXDUWS9fv4w5FN3CkqNLgj199uTeSXg4darajp0yLeb2/id2nIthx89ttIez8CKKEA04o2cbEvjSzL5AFfP5pJ0ZbBGXspaCGEEEIIIYQ4jOkWzSG05guIcPJopAnEaijLK2df83roR1IrEFR5fo2PH5xgxKDvPvPrQPUtYdw+laNm6Tn1KIXPtgV47CUPt/84hnibhqX5Bhbl6QEwGRR2lQexxiiMi9fw1BteymtD5E/VsfI4A4rS+1hbc+aw1bFpQPc9FHqNlZ2tFiCyBX4SjXG0Ra7ANNvCIVD0RHq7P0/zXGLYFdlOD2L812Y8i5ZR3fJBt3Mj8f14oJimSk5pVqicMp/3vTvwhbwDfwFGkVhDPEdNWB7tMEYlmbEohBBCCCGEEIcRJc6GZk5OtMMYVg2zA2zKfKsjqdhPb633MylFS25m3/NrLjzRyG8utTAnW8ekFC3nn2Ak0a5hw85gZxuTQcFk6EjSvPWFnxWLDHxcECAcVrnjxzGUVIfYXNz7zLvy7Hw+PYRJRYAJsefhCES+aniiMTGi/bkAhmFFf6AuAdIyIt/xQWRuSCYpYUG34yPt/dgTRVXJKNnExY44cm3TB3TtaPO9zO9j0BqjHcaoJDMWhRBCCCGEEOIwoztuAf6C3dEOI+K8iQp7pu6muWXzgK/9ujCIo13lusddAHxbfLmg2MXDv7B2aavVKJgPyDEoikJKgoY2V/cpdLsrglhMCpNStLy13k9ulg6DXmFaupbSmhD5Od2/djdOmslb7QUDvoehUNBR1p4KRH7mmUUT+SygL17B2Br5CsW+wFyMVES8394oKkzdNAX/nDYcbUWdx0fS+7EvRkc9xzvqmZ41h/dCZbgCjgH3MZLpNQZWZJ4X7TBGLUksCiGEEEIIIcRhRjMpFSUrDbWsOtqhRISqQN08D+Xe9wm1DG7N7S/PMxM6YMLWfz/xAXDmsd1nKT2yysPUdG3nHnVhVaWmIcSSufpubd/6ws/3l3b0oSigqh3JnpBKj0t5neMmszpUhBrpdb59SI87iy8ah2c5q6JaIt5nqw1SIt4reMsmYswcBw37hqH3nml9KtN3z2PrFAdedx0wct6PA5FatpWLYuL4Kn02Xzu2Dq2zEeTYtO8RZ4rsrNuxRJZCCyGEEEIIIcRhSLfkiGiHEBHuCQrbFmym1PUaoeDgN/JLjNUwLn7/P6NBwWjo2IcuHFZpaw8TDHVkXmZN0fLhJj9bS4PUN4dZ9YEPt4/Ofey+VVgZxGzsmB0GkDFeS0FRkJrGENtLg2RN6PqV22dP4b/mJvxh36DvY7D2+WYMW9+BYOSXkNabhy/xGjDkD1vfvTE4VPLqlqIz2ICR8X4cDJ27lUWF2zhfl0eCMWnI/Y0Ep0++KNohjGqSWBRCCCGEEEKIw5BmVg5KwjBsVHeIhLVQtbCVAvtLONoKh3WsFqfKLU+42VPTMYVs2Tw9y48wsOoDH/c956a2Mcw13zd17mH3rbe/2cvuW8fN1WPQKzz4QscMswOXnQZNNt5M1NLmbx3We+lJqu149jgjWLb5Oxy+yC+GrDBEvjL0t9wlkyHWNmz998Zcq5LrWYGi6T7T8ECH4v04VIlVOzm31sMxsfko9K8gzEg0O2khmfbDe0/a4SZLoYUQQgghhBCHvb1793LCCSewdu1aJk6cGO1wDglFo6BduoDgK2uiHcqAObMUihM/x91WOWxj/OhkU+fjRLuGP16/f187RVE4+UgDJx9p6OnSTteeF9PludmocPXZ5m7twhodH6aPp9ZV1O3coeDjKIZjb0Xo2EOwYRhylrs1YU5HG/mOAcIaQvH5aB3rhqf/g4gtgZw5p1PofaXL8UP5fowUnc/NnMLNZI2fwtoYDzWe0bf1wtnZlwxr/xs2bOBHP/oRhYU9/3Hk8ccfZ+PGjTz33HMH7ae/7aJBZiwKIYQQQgghxGFKu3AWWGP6bjhCBE0KexbvY4vhP7idw5dUPJRU4KupuRRFKamYaJ7NjpbhSSoCJBnj8Q/D5MJt4RDDORGuvXQamKJTBTh5i54M+8lRGXs4xNaVclZFA8tt89Epo2f+2mT7dOaOWxzVGC699FIef/zxqMYwVJJYFEIIIYQQQojDlGLQo1syP9ph9EvLjDAFMz6gpvlDhlxlYgQpypnHl44tURvfYDxlWF/NBEPCsPTbDqhxw9I1AKrPQHj87OEboA/pG+2MTzgmauNHmhIKMK1oEz/yTWCyZXK0w+mXlVN/Eu0QsFgsxMXFRTuMIZHEohBCCCGEEGLMeOedd1iyZAnz5s3jjjvuwO/3A/Diiy9y8sknM3PmTI488kjuuusuQt+UbL3pppu4//77ufbaa5kzZw7HHXccr776amef9fX1XHPNNSxYsICZM2dy9tln8/XXXwMdS7CnTZvGe++9x/Lly5k1axZXXHEFra2tndcfbOxI0B49D0wHX0IZTYFYDUWLK9gRfhGf+9BV6j0UarLmsMb5ddTGt+rT2docHtYxLNrh28fTFze8e/e5K2eCdpiWW/fD5I1pxMXPitr4w8HcVMXJpeWcZpmPSTt8y7CHKtUyicWpJ0S0z4qKCi677DLy8/NZunQp//jHPzrPPf/88xx77LHk5+dz8803d/7uefzxx7n44os7261bt46zzz6bOXPmcMYZZ7B+/fpu4/h8Pi644AIuvfRS/H4/N910E/********************************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", "text/plain": ["<Figure size 1400x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Crop distribution visualization saved\n"]}], "source": ["# Target variable analysis\n", "print(\"\\n🎯 TARGET VARIABLE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "print(\"\\nCrop Distribution:\")\n", "crop_counts = data['label'].value_counts()\n", "print(crop_counts)\n", "\n", "print(f\"\\nNumber of unique crops: {data['label'].nunique()}\")\n", "print(f\"Crops: {sorted(data['label'].unique())}\")\n", "\n", "# Visualize crop distribution\n", "plt.figure(figsize=(14, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "crop_counts.plot(kind='bar')\n", "plt.title('Crop Distribution (Count)')\n", "plt.xlabel('Crop')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "crop_counts.plot(kind='pie', autopct='%1.1f%%')\n", "plt.title('Crop Distribution (Percentage)')\n", "plt.ylabel('')\n", "\n", "plt.tight_layout()\n", "plt.savefig('../results/figures/01_crop_distribution.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\n✅ Crop distribution visualization saved\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 FEATURE ANALYSIS\n", "==================================================\n", "\n", "Feature Statistics:\n", "                 N            P            K  temperature     humidity  \\\n", "count  2200.000000  2200.000000  2200.000000  2200.000000  2200.000000   \n", "mean     50.551818    53.362727    48.149091    25.616244    71.481779   \n", "std      36.917334    32.985883    50.647931     5.063749    22.263812   \n", "min       0.000000     5.000000     5.000000     8.825675    14.258040   \n", "25%      21.000000    28.000000    20.000000    22.769375    60.261953   \n", "50%      37.000000    51.000000    32.000000    25.598693    80.473146   \n", "75%      84.250000    68.000000    49.000000    28.561654    89.948771   \n", "max     140.000000   145.000000   205.000000    43.675493    99.981876   \n", "\n", "                ph     rainfall  \n", "count  2200.000000  2200.000000  \n", "mean      6.469480   103.463655  \n", "std       0.773938    54.958389  \n", "min       3.504752    20.211267  \n", "25%       5.971693    64.551686  \n", "50%       6.425045    94.867624  \n", "75%       6.923643   124.267508  \n", "max       9.935091   298.560117  \n", "\n", "📊 Outlier Detection (IQR Method):\n", "  N: 0 outliers (0.00%)\n", "  P: 138 outliers (6.27%)\n", "  K: 200 outliers (9.09%)\n", "  temperature: 86 outliers (3.91%)\n", "  humidity: 30 outliers (1.36%)\n", "  ph: 57 outliers (2.59%)\n", "  rainfall: 100 outliers (4.55%)\n"]}], "source": ["# Feature analysis\n", "print(\"\\n🔍 FEATURE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "\n", "print(\"\\nFeature Statistics:\")\n", "print(data[feature_cols].describe())\n", "\n", "# Check for outliers using IQR method\n", "print(\"\\n📊 Outlier Detection (IQR Method):\")\n", "for col in feature_cols:\n", "    Q1 = data[col].quantile(0.25)\n", "    Q3 = data[col].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "    outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)]\n", "    print(f\"  {col}: {len(outliers)} outliers ({len(outliers)/len(data)*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 FEATURE DISTRIBUTIONS\n", "==================================================\n"]}], "source": ["# Feature distributions\n", "print(\"\\n📊 FEATURE DISTRIBUTIONS\")\n", "print(\"=\" * 50)\n", "\n", "fig, axes = plt.subplots(3, 3, figsize=(18, 15))\n", "axes = axes.ravel()\n", "\n", "for idx, col in enumerate(feature_cols):\n", "    axes[idx].hist(data[col], bins=50, edgecolor='black', alpha=0.7)\n", "    axes[idx].set_title(f'{col} Distribution')\n", "    axes[idx].set_xlabel(col)\n", "    axes[idx].set_ylabel('Frequency')\n", "    axes[idx].grid(True, alpha=0.3)\n", "\n", "# Hide extra subplots\n", "axes[7].axis('off')\n", "axes[8].axis('off')\n", "\n", "plt.tight_layout()\n", "plt.savefig('../results/figures/01_feature_distributions.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"✅ Feature distribution plots saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correlation analysis\n", "print(\"\\n🔗 CORRELATION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate correlation matrix\n", "correlation_matrix = data[feature_cols].corr()\n", "print(\"\\nCorrelation Matrix:\")\n", "print(correlation_matrix)\n", "\n", "# Visualize correlation matrix\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(correlation_matrix, annot=True, fmt='.2f', cmap='coolwarm', \n", "            center=0, square=True, linewidths=1)\n", "plt.title('Feature Correlation Matrix')\n", "plt.tight_layout()\n", "plt.savefig('../results/figures/01_correlation_matrix.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\n✅ Correlation matrix saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data Preprocessing\n", "print(\"\\n🔧 DATA PREPROCESSING\")\n", "print(\"=\" * 50)\n", "\n", "# Separate features and target\n", "X = data[feature_cols].copy()\n", "y = data['label'].copy()\n", "\n", "print(f\"\\nFeatures shape: {X.shape}\")\n", "print(f\"Target shape: {y.shape}\")\n", "print(f\"\\nFeature columns: {list(X.columns)}\")\n", "print(f\"Target classes: {sorted(y.unique())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Label Encoding\n", "print(\"\\n🏷️ LABEL ENCODING\")\n", "print(\"=\" * 50)\n", "\n", "label_encoder = LabelEncoder()\n", "y_encoded = label_encoder.fit_transform(y)\n", "\n", "print(f\"\\nOriginal labels: {sorted(y.unique())}\")\n", "print(f\"Encoded labels: {sorted(set(y_encoded))}\")\n", "print(f\"\\nLabel mapping:\")\n", "for i, crop in enumerate(label_encoder.classes_):\n", "    print(f\"  {crop}: {i}\")\n", "\n", "# Save label encoder\n", "joblib.dump(label_encoder, '../data/processed/label_encoder.pkl')\n", "print(\"\\n✅ Label encoder saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train/Validation/Test Split\n", "print(\"\\n✂️ TRAIN/VALIDATION/TEST SPLIT\")\n", "print(\"=\" * 50)\n", "\n", "# First split: separate test set (15%)\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X, y_encoded, test_size=0.15, random_state=42, stratify=y_encoded\n", ")\n", "\n", "# Second split: separate train and validation (70% train, 15% val)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp\n", ")\n", "\n", "print(f\"\\nDataset split:\")\n", "print(f\"  Total samples: {len(X)}\")\n", "print(f\"  Training set: {len(X_train)} ({len(X_train)/len(X)*100:.1f}%)\")\n", "print(f\"  Validation set: {len(X_val)} ({len(X_val)/len(X)*100:.1f}%)\")\n", "print(f\"  Test set: {len(X_test)} ({len(X_test)/len(X)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature Scaling\n", "print(\"\\n⚖️ FEATURE SCALING\")\n", "print(\"=\" * 50)\n", "\n", "# Initialize scaler\n", "scaler = StandardScaler()\n", "\n", "# Fit on training data only\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"\\nScaled feature ranges (training set):\")\n", "X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=feature_cols)\n", "print(X_train_scaled_df.describe())\n", "\n", "# Save scaler\n", "joblib.dump(scaler, '../data/processed/scaler.pkl')\n", "print(\"\\n✅ Scaler saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save Preprocessed Data\n", "print(\"\\n💾 SAVING PREPROCESSED DATA\")\n", "print(\"=\" * 50)\n", "\n", "# Create dataframes with encoded labels\n", "train_df = X_train.copy()\n", "train_df['label'] = y_train\n", "train_df['label_name'] = label_encoder.inverse_transform(y_train)\n", "\n", "val_df = X_val.copy()\n", "val_df['label'] = y_val\n", "val_df['label_name'] = label_encoder.inverse_transform(y_val)\n", "\n", "test_df = X_test.copy()\n", "test_df['label'] = y_test\n", "test_df['label_name'] = label_encoder.inverse_transform(y_test)\n", "\n", "# Save to CSV\n", "train_df.to_csv('../data/processed/train.csv', index=False)\n", "val_df.to_csv('../data/processed/validation.csv', index=False)\n", "test_df.to_csv('../data/processed/test.csv', index=False)\n", "\n", "print(f\"✅ Training data saved: {len(train_df)} samples\")\n", "print(f\"✅ Validation data saved: {len(val_df)} samples\")\n", "print(f\"✅ Test data saved: {len(test_df)} samples\")\n", "\n", "# Save scaled data as well\n", "train_scaled_df = pd.DataFrame(X_train_scaled, columns=feature_cols)\n", "train_scaled_df['label'] = y_train\n", "train_scaled_df.to_csv('../data/processed/train_scaled.csv', index=False)\n", "\n", "val_scaled_df = pd.DataFrame(X_val_scaled, columns=feature_cols)\n", "val_scaled_df['label'] = y_val\n", "val_scaled_df.to_csv('../data/processed/validation_scaled.csv', index=False)\n", "\n", "test_scaled_df = pd.DataFrame(X_test_scaled, columns=feature_cols)\n", "test_scaled_df['label'] = y_test\n", "test_scaled_df.to_csv('../data/processed/test_scaled.csv', index=False)\n", "\n", "print(f\"✅ Scaled data saved\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary Statistics\n", "print(\"\\n📊 PREPROCESSING SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "summary = {\n", "    'Total Samples': len(data),\n", "    'Features': len(feature_cols),\n", "    'Target Classes': len(label_encoder.classes_),\n", "    'Training Samples': len(X_train),\n", "    'Validation Samples': len(X_val),\n", "    'Test Samples': len(X_test),\n", "    'Missing Values': 0,\n", "    'Duplicates Removed': 0\n", "}\n", "\n", "print(\"\\n📈 Dataset Summary:\")\n", "for key, value in summary.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# Save summary\n", "import json\n", "with open('../data/processed/preprocessing_summary.json', 'w') as f:\n", "    json.dump(summary, f, indent=4)\n", "\n", "print(\"\\n✅ Summary saved to preprocessing_summary.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final Summary and Conclusions\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"📝 EXPLORATORY DATA ANALYSIS - SUMMARY\")\n", "print(\"=\" * 70)\n", "\n", "print(\"\\n1️⃣ DATASET OVERVIEW:\")\n", "print(f\"   Total samples: {len(data)}\")\n", "print(f\"   Features: {len(feature_cols)}\")\n", "print(f\"   Target classes: {len(label_encoder.classes_)}\")\n", "print(f\"   Data quality: ✅ No missing values\")\n", "\n", "print(\"\\n2️⃣ FEATURE CHARACTERISTICS:\")\n", "print(f\"   Soil nutrients: N, P, K\")\n", "print(f\"   Climate factors: temperature, humidity, rainfall\")\n", "print(f\"   Soil property: pH\")\n", "print(f\"   All features show reasonable distributions\")\n", "\n", "print(\"\\n3️⃣ TARGET VARIABLE:\")\n", "print(f\"   Crops: {', '.join(sorted(label_encoder.classes_))}\")\n", "print(f\"   Distribution: Relatively balanced across classes\")\n", "\n", "print(\"\\n4️⃣ DATA PREPROCESSING:\")\n", "print(f\"   ✅ Label encoding applied\")\n", "print(f\"   ✅ Train/Val/Test split: {len(X_train)}/{len(X_val)}/{len(X_test)}\")\n", "print(f\"   ✅ Feature scaling (StandardScaler) applied\")\n", "print(f\"   ✅ All preprocessed data saved\")\n", "\n", "print(\"\\n5️⃣ KEY INSIGHTS:\")\n", "print(f\"   📊 Different crops have distinct feature profiles\")\n", "print(f\"   📊 Features show moderate correlations\")\n", "print(f\"   📊 Data is clean and ready for modeling\")\n", "print(f\"   📊 Balanced class distribution supports classification\")\n", "\n", "print(\"\\n6️⃣ SAVED ARTIFACTS:\")\n", "print(f\"   📁 Preprocessed data: train.csv, validation.csv, test.csv\")\n", "print(f\"   📁 Scaled data: train_scaled.csv, validation_scaled.csv, test_scaled.csv\")\n", "print(f\"   📁 Objects: scaler.pkl, label_encoder.pkl\")\n", "print(f\"   📁 Visualizations: plots saved in results/figures/\")\n", "print(f\"   📁 Summary: preprocessing_summary.json\")\n", "\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"✅ EDA AND PREPROCESSING COMPLETE!\")\n", "print(\"=\" * 70)\n", "print(\"\\n🚀 Ready for model training in subsequent notebooks!\")\n", "print(\"=\" * 70)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}