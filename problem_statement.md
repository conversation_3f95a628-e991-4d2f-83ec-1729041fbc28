# Problem Statement

The problem statement is to implement a Machine Learning 'ML' mini-project based off a open access journal research paper which should be indexed in scopus and or should be published by IEEE/Springer/ACM.
The research paper must be latest as of 2025 and must perform not all but most of the following topics:
1 To implement Linear Regression.
2 To implement Logistic Regression.
3 To implement Ensemble learning (bagging/boosting)
4 To implement multivariate Linear Regression.
5 To implement SVM
6 To implement PCA/SVD/LDA
7 To implement Graph Based Clustering
8 To implement DB Scan
9 To implement CART
10 To implement LDA
The ML project must implement most of the above 10 ML experiments / topics as mentioned in the research paper and must exercise these on the dataset.
Find me a research paper first with this criteria and then I will implement the project.
The research paper must be real with DOI number and must be open access.
The paper must be related to AIML.
The dataset should be available and if possible verified.