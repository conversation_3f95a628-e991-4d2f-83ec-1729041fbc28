{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎯 Notebook 03: Logistic Regression Classification\n", "## Intelligent Agriculture - Crop Recommendation System\n", "\n", "**Objectives:**\n", "1. Implement Binary Logistic Regression\n", "2. Implement Multiclass Logistic Regression\n", "3. Feature importance analysis\n", "4. Model evaluation with classification metrics\n", "5. Crop recommendation system"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully!\n"]}], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.metrics import precision_score, recall_score, f1_score, roc_auc_score\n", "from sklearn.preprocessing import label_binarize\n", "import joblib\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 LOADING PREPROCESSED DATA\n", "==================================================\n", "✅ Data loaded: 1540 train, 330 val, 330 test\n", "\n", "Feature shape: (1540, 7)\n", "Number of classes: 22\n"]}], "source": ["# Load preprocessed data\n", "print(\"📊 LOADING PREPROCESSED DATA\")\n", "print(\"=\" * 50)\n", "\n", "train_data = pd.read_csv('../data/processed/train.csv')\n", "val_data = pd.read_csv('../data/processed/validation.csv')\n", "test_data = pd.read_csv('../data/processed/test.csv')\n", "\n", "# Load preprocessing objects\n", "scaler = joblib.load('../data/processed/scaler.pkl')\n", "label_encoder = joblib.load('../data/processed/label_encoder.pkl')\n", "\n", "print(f\"✅ Data loaded: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test\")\n", "\n", "# Prepare features and labels\n", "feature_cols = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']\n", "X_train = train_data[feature_cols]\n", "X_val = val_data[feature_cols]\n", "X_test = test_data[feature_cols]\n", "y_train = train_data['label']\n", "y_val = val_data['label']\n", "y_test = test_data['label']\n", "\n", "# Scale features\n", "X_train_scaled = scaler.transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"\\nFeature shape: {X_train_scaled.shape}\")\n", "print(f\"Number of classes: {len(label_encoder.classes_)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 MULTICLASS LOGISTIC REGRESSION\n", "==================================================\n", "\n", "Accuracy Scores:\n", "  Training: 0.9727\n", "  Validation: 0.9758\n", "  Test: 0.9758\n"]}], "source": ["# Multiclass Logistic Regression\n", "print(\"\\n🎯 MULTICLASS LOGISTIC REGRESSION\")\n", "print(\"=\" * 50)\n", "\n", "# Train logistic regression model\n", "lr_model = LogisticRegression(max_iter=1000, random_state=42, multi_class='multinomial')\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred = lr_model.predict(X_train_scaled)\n", "y_val_pred = lr_model.predict(X_val_scaled)\n", "y_test_pred = lr_model.predict(X_test_scaled)\n", "\n", "# Accuracy\n", "train_acc = accuracy_score(y_train, y_train_pred)\n", "val_acc = accuracy_score(y_val, y_val_pred)\n", "test_acc = accuracy_score(y_test, y_test_pred)\n", "\n", "print(f\"\\nAccuracy Scores:\")\n", "print(f\"  Training: {train_acc:.4f}\")\n", "print(f\"  Validation: {val_acc:.4f}\")\n", "print(f\"  Test: {test_acc:.4f}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 CLASSIFICATION REPORT (Test Set)\n", "==================================================\n", "              precision    recall  f1-score   support\n", "\n", "       apple       1.00      1.00      1.00        15\n", "      banana       1.00      1.00      1.00        15\n", "   blackgram       0.94      1.00      0.97        15\n", "    chickpea       1.00      1.00      1.00        15\n", "     coconut       0.94      1.00      0.97        15\n", "      coffee       1.00      1.00      1.00        15\n", "      cotton       1.00      1.00      1.00        15\n", "      grapes       1.00      1.00      1.00        15\n", "        jute       0.88      1.00      0.94        15\n", " kidneybeans       1.00      1.00      1.00        15\n", "      lentil       0.93      0.87      0.90        15\n", "       maize       1.00      1.00      1.00        15\n", "       mango       0.94      1.00      0.97        15\n", "   mothbeans       0.93      0.87      0.90        15\n", "    mungbean       1.00      1.00      1.00        15\n", "   muskmelon       1.00      1.00      1.00        15\n", "      orange       1.00      0.93      0.97        15\n", "      papaya       1.00      0.93      0.97        15\n", "  pigeonpeas       1.00      1.00      1.00        15\n", " pomegranate       1.00      1.00      1.00        15\n", "        rice       0.93      0.87      0.90        15\n", "  watermelon       1.00      1.00      1.00        15\n", "\n", "    accuracy                           0.98       330\n", "   macro avg       0.98      0.98      0.98       330\n", "weighted avg       0.98      0.98      0.98       330\n", "\n"]}], "source": ["# Classification Report\n", "print(\"\\n📊 CLASSIFICATION REPORT (Test Set)\")\n", "print(\"=\" * 50)\n", "\n", "print(classification_report(y_test, y_test_pred, \n", "                          target_names=label_encoder.classes_))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔲 CONFUSION MATRIX\n", "==================================================\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABP4AAASlCAYAAAA4SRf0AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/OQEPoAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzde3zP9f//8fuYGeY8htQMM2wOc9qn2hAVFZ+Ub+XcnEKp5JDz+dDJ2YiKEOV8LFTIqTKHZFSYxaJ90sRy2sYOvz/67Z234xz2eu35drteLrt8vV/v021Ps8v38+j1er7d0tPT0wUAAAAAAADApeSwOwAAAAAAAADA3cfgDwAAAAAAAHBBDP4AAAAAAAAAF8TgDwAAAAAAAHBBDP4AAAAAAAAAF8TgDwAAAAAAAHBBDP4AAAAAAAAAF8TgDwAAAAAAAHBBDP4AAABuID093e4E47BmAAAA2QODPwAAsol9+/apT58+ql+/vqpWrapHH31UgwcP1rFjx7LsPWfPnq2HH35YVatW1bRp0+7Ka0ZGRiogIECRkZF35fUy814BAQHatm3bNR8TExPjeMzx48cz/doXL17UmDFjtHr16ps+NiAgQFOmTMn0a98JK99r2bJlt7RuZ86c0Ztvvqldu3Y5jrVt21Zt27a9o46Mv7/LvypXrqyQkBB16NBBUVFRd/T62dGtrj0AAMC1uNsdAAAApPnz52vMmDEKCQlRr169VLx4ccXGxmrmzJn66quvNGfOHFWsWPGuvue5c+f0zjvvqH79+urQoYNKly59V143MDBQCxcuVPny5e/K62VGjhw5tG7dOoWGhl5135o1a27rNf/880/NmTNHb7311k0fu3DhQpUoUeK23ic7q1+/vhYuXKjixYtn6vG//PKLVq5cqebNmzuODR069K60/N///Z+ee+45x+2LFy8qOjpa06dPV/v27bVu3ToVK1bsrrxXdnCraw8AAHAtDP4AALDZ7t27NXr0aLVu3VoDBw50HA8JCdGjjz6qZs2aacCAAVq2bNldfd+///5baWlpevTRR1W7du279rpeXl6qXr36XXu9zKhRo4a+/vprDRs2TO7uzv/vzZo1a1SpUiX98ssvWfb+Vn+/VilSpIiKFClyR69xtwbAJUqUuGqd69Spo/vvv1+dO3fWV199pdatW9+V98oO7sbaAwAAcKkvAAA2mzlzpvLnz6+ePXtedV+RIkXUr18/NWzYUBcuXJAkpaamav78+WratKmqVq2q+vXra+zYsUpOTnY8r1+/fgoPD9fSpUvVqFEjBQUF6emnn9aWLVsk/XMZYYMGDSRJAwYMUEBAgCSpQYMG6tevn1PDlZccJiUladiwYapbt66CgoLUuHFjzZw50/H4a13qu2/fPnXs2FEhISGqUaOGunbtqujo6Kue8/3336tDhw6qVq2aHn74Yb333ntKTU296Ro++eSTSkhI0Pbt252OHzhwQEePHtUTTzxx1XPWr1+vVq1aKTg42PF9zJ8/X5J0/PhxNWzYUJLUv39/x1r169dPL774ooYOHaoaNWroySefVGpqqtPlt927d1eVKlX066+/Ot5rypQpqlSpknbs2HHT7+VuyMzPiCQtX75cTz75pKpUqaL//ve/+v7771W5cmXHkPnKv/tTp06pV69eevjhh1WlShU9/fTTWrFihaR//g7btWsnSWrXrp3j8t4rL/W9ePGiJk6cqIYNG6pq1apq0qSJli9fftvfa4ECBSRJbm5ujmMJCQkaMmSIHnroIVWpUkXPP/+8vv/+e6fnnTt3TkOGDNGDDz6o4OBgvfHGG5o9e7bj30JGe+/evfXaa6+pevXqat++vSQpOTlZ7777rurVq6egoCA1bdr0qjNL9+/frxdffFE1a9ZUcHCwwsPD9eOPPzruv9FaSte+1Pfbb79Vq1atVLNmTcfZwf/73/+cnlO5cmXt3btXL7zwgqpUqaJHHnnE6d8nAAC4tzD4AwDARunp6dq2bZsefPBB5cmT55qPefLJJ/XKK68ob968kqQhQ4borbfe0qOPPqr3339frVu31rx58/Tyyy87fajC/v37NXPmTL322muaOnWqcubMqVdffVV///236tevr4iICElSt27dtHDhwkw3jxkzRlu2bFHfvn01c+ZMNWzYUO+++66WLl16zcdv375dLVu2dDx31KhR+t///qcWLVooJibG6bG9e/dWzZo1NX36dDVp0kQfffSRFi9efNOm8uXLy9/fX+vWrXM6/sUXX6hOnTpXXQK6adMmvfLKKwoMDNS0adM0ZcoU3X///RoxYoT27t2r4sWLO61Pxp8ladeuXfrf//6nqVOnqlevXsqZM6fTaw8bNkx58+Z1XOK6f/9+TZ8+XR06dFCdOnVu+r3cDZn5GVmxYoX69eunGjVqaNq0aWrUqJFefvnlGw5a+/Tpo5iYGA0fPlwffvihKleurL59+2r79u0KDAzUkCFDHO9/vUt8e/furY8//ljPPfecZsyYodDQUPXr10+ff/75Db+ntLQ0paSkOL7Onz+vH374QcOHD1f+/Pkdg9rk5GS9+OKL2rBhg9544w1FRESoRIkS6tSpk9Pw7+WXX9batWv16quvasKECTp//rzGjRt31fuuXbtW+fLl0/vvv69OnTopPT1dr7zyihYsWKD27dvr/fffdwwOMwZ3586dU6dOnVS4cGFNmTJFEyZMUGJiojp27KizZ8/edC2vZcWKFerQoYNKliyp8ePHq3///tqzZ49eeOEF/fXXX07r1KNHDz355JP64IMPVKNGDb377rvaunXrDdcXAAC4Ji71BQDARqdPn1ZycnKm99c7fPiwlixZol69eumll16SJD388MMqXry43nzzTW3ZskX16tWTJJ09e1bLli3TAw88IEnKmzev2rRpo+3bt6tRo0aqVKmSJOmBBx64pUtVd+zYoYcfflhPPfWUpH8uSc6bN6+KFi16zcePGzdOvr6++uCDDxxDstDQUD322GOaPHmyJk2a5Hjsc889p1deeUWS9OCDD2r9+vXatGmTWrRocdOuJ554QnPnznW63HfNmjXq2rXrVY89fPiwnnnmGadLq4ODgxUSEqLIyEhVq1bNaX0qV67seFxKSopGjBhx3T39vL29NXToUL3xxhtavHix5syZowoVKuj111+/6fdwN2T2Z2TSpEl65JFHNGrUKElSWFiYcuXKdc3hV4YdO3bolVde0aOPPirpn0ttCxUqJA8PD3l5eTku6y1fvvw1L/E9dOiQvvzySw0YMEAvvviipH/+nn///XdFRkaqSZMm133vadOmXfUBNB4eHqpVq5bGjBkjHx8fSdLKlSt14MABLVq0SNWqVZMk1a1bV23bttXYsWO1dOlSff/994qMjNSUKVP0+OOPOx7TpEmTq4bRuXLl0vDhw+Xh4SHpn7Putm7dqgkTJujJJ590rF1iYqLGjh2rJk2a6PDhwzp9+rTatWunGjVqSJLKli2rhQsX6vz588qfP/8N1/JKaWlpGjt2rEJDQ53+fjLOOp05c6befPNNSf/8x4SXX37ZsR9izZo19fXXX2vTpk0KCwu77voCAADXxBl/AADYKGMQlpnLWSU5LhXNGLpleOqpp5QzZ06ny2uLFCniGPpJcgyqEhMT76g5JCREixYtUufOnTVv3jwdO3ZMr7zyiurXr3/VYy9cuKB9+/bpiSeecDozrkCBAnrkkUeuuvQ1ODjY6XaJEiUclzjfzJWX++7du1cnTpxwDHYu16lTJ7399ts6f/689u/frzVr1mjGjBmS/rkU9UYKFSp00w/yePLJJ9WoUSMNGTJEx44d09ixY6850MmQmprqdDZbWlrazb7d68rMz0hsbKzi4uLUuHHjqx5zIyEhIZoyZYpee+01LV68WCdPnlTfvn0dw62b2b17tyRd9XcyZcoUjRw58obPff7557VkyRItXrxYI0eOVIECBfTwww8rIiLCMaSVpO+//17FihVTYGCgYz1TU1P1yCOPaP/+/fr777+1fft25cqVyzF0k/75gJiMQd7lypYt6/R39/3338vNzU316tVz+jtr0KCB4uPjFR0dLX9/fxUpUkRdu3bVkCFD9PXXX8vb21t9+vRx/OzcyloeOXJE8fHxVw1GH3jgAQUHB9/w35GHh4eKFCmS6X9HAADAtXDGHwAANipYsKDy5cunuLi46z7mwoULunTpkgoWLKi///5bkq66dNXd3V2FCxd2XEYo6apLhzP2QLuToZIkDRw4UCVKlNCqVas0cuRIjRw5UsHBwRo2bNhVnzx89uxZpaeny9vb+6rX8fb2duqVJE9PT6fbOXLkcLp8+Ub8/PxUqVIlx6f7rlmzRqGhoSpYsOBVjz116pSGDh2q9evXy83NTb6+vqpVq5Yk3fT98uXLl6meZ555Rl9++aXKlCkjPz+/Gz72scce0++//+703LfffjtT73OlzPyMnDp1SpKuOkvzWn9Pl5swYYKmT5+utWvX6ssvv1SOHDn00EMPacSIEbrvvvtu2paQkHDN982M4sWLq0qVKpKkqlWr6v7771f79u3Vo0cPffDBB46f74SEBMXHxyswMPCarxMfH6/Tp0+rUKFCypHD+b+BX6vryr/vhIQEpaenX3fY+eeff6pSpUqaP3++3n//fa1du1YLFy6Up6ennn76aQ0aNEgeHh63tJYZ63a9f0c///yz07E7+XcEAABcC4M/AABsFhoaqsjISCUnJyt37txX3b9o0SK98847WrJkiWOIFR8f7zQcuHTpkk6fPq3ChQvfcc+VZx9eeaaQh4eHunXrpm7duikuLk7ffPONpk2bpl69eumLL75wemz+/Pnl5uamkydPXvU+8fHxKlSo0B33Xi7jssehQ4dq3bp16t279zUf17t3b/3666+aPXu2goOD5eHhocTERC1atOiudCQmJuqtt95ShQoVdOjQIc2aNUudOnW67uPff/99pzMN7+TvMTM/IxlnnV2+N9y1bl8pf/786tOnj/r06aNff/1VGzZs0LRp0zR8+HB98MEHN23L+CCOU6dOOZ01GRMTo4SEBNWsWTNz36T+uUS4VatWmj9/vhYtWqQXXnjB0VimTBmNHTv2ms8rXbq0fHx8dPr0aaWlpTkN/272/We8ft68eTV37txr3u/r6yvpnzMFMz6cJioqSitXrtRnn32mBx54QJ06dbqltcz4d3K9f0d34989AABwTVzqCwCAzTp06KCEhARNnDjxqvvi4+M1a9YslS9fXoGBgY4Ph7hywPbFF18oNTX1lgYn1+Ll5aU//vjD6VjG5ZnSP5/o26hRI82aNUuSVKpUKbVu3VpPPfXUNc9azJs3r4KCgrR27VqngeLZs2e1adOmO+690hNPPKGEhARNnz5df//9t+MDH660e/duPf744woJCXFcxpnxiccZZ0Re+aEdt2LcuHH6448/NGXKFLVp00aTJ0++au+4ywUEBKhKlSqOr8zu+XgtmfkZKVGihB544AF9/fXXTo/56quvrvu6v//+u+rVq+f4AJWyZcuqc+fOeuihhxx/9zdbs4y/740bNzodHzt2rEaPHp2J787ZG2+8IW9vb40fP95xVlydOnX0v//9T0WLFnVa02+//VYfffSRcubMqTp16iglJcWpIz09XevXr7/pe9apU0cXLlxQenq60+sfOnRIU6dOVUpKitatW6f//Oc/io+PV86cOR1nxBYoUEBxcXGZWsvL+fn5qVixYld9AMqxY8f0448/ZvpSawAAcO/hjD8AAGxWvXp1vf7665o4caJiYmLUrFkzFS5cWNHR0Zo5c6aSk5MdQ8Hy5cvrmWee0eTJk5WYmKjatWvrl19+UUREhEJCQu548/5HHnlEM2bM0IwZM1StWjVt3LjR6VNGPT09FRgYqIiICOXKlUsBAQE6cuSIli9frkaNGl3zNXv16qWOHTvqpZdeUqtWrXTp0iV98MEHunjxouODPO6W+++/X1WqVNGMGTP02GOPOT4J+UpVq1bV6tWrFRgYqBIlSuiHH35wXC6asQdi/vz5Jf2zp1u5cuUcHxRxMzt27NC8efP0xhtvqEyZMurRo4e+/vpr9evXTwsWLLijgWKGH3/8UbNnz77qeFhYWKZ+Rtzc3PTaa6+pd+/eGjp0qB577DEdOHBAU6dOlaSrLoGVpPvuu08lSpTQqFGjdO7cOT3wwAPav3+/Nm/erC5dukj6d802bdqkggULXnXpd8WKFdW4cWO99957SkpKUqVKlbRlyxZ98803Tp+cnFn58+fXG2+8oYEDB2rSpEkaOnSonn32Wc2bN0/t27dX165dVbJkSX333Xf68MMP1aZNG+XKlUu1a9fWww8/rIEDB+rkyZMqVaqUlixZooMHDzouGb6eevXqqXbt2nr55Zf18ssvq1y5coqKitLkyZMVFhamIkWKqEaNGkpLS9Mrr7yil156Sfny5dPatWt19uxZPf7445lay8vlyJFDPXv2VP/+/dWrVy/997//1enTpxUREaGCBQuqffv2t7x2AADg3sDgDwCAbKBbt26qXLmy5s+frzFjxujvv/9WyZIlVb9+fcfwIsPo0aPl6+urpUuX6sMPP1Tx4sXVrl07vfzyy9cc2NyKLl266NSpU5o5c6YuXbqk+vXra/To0erWrZvjMSNGjNDEiRM1a9YsxcfHq2jRovq///u/635q7YMPPqiPP/5YkydPVs+ePR2fxPrOO+/I39//jnqv5cknn9S+fftu+EEVb7/9tmN/QkkqU6aMhg8frlWrVmnXrl2S/jn7sX379lq4cKE2b96sb7/99qbvfeHCBfXv318VKlRQx44dJf2zR9yQIUPUrVs3ffTRR9cc7Nyqbdu2adu2bVcdL1y4sMqVK5epn5GmTZvqwoULmjlzppYuXSp/f38NHDhQAwcOvO7ANCIiQuPHj9ekSZN0+vRplSxZUt27d3d8erC/v7+aNGmi+fPna+vWrVedoSZJ7733niIiIjRnzhydPn1a5cqV0+TJk50+aONWNG/eXAsXLtTChQv1wgsvqGLFipo/f77GjRun9957T2fPntV9992nXr16qUOHDo7nTZgwQW+//bbGjRunlJQUNWzYUC1bttSKFStu+H45cuTQBx98oEmTJmnGjBn666+/5OPjo/bt2zsG2cWLF9dHH32kSZMmaeDAgUpMTJS/v7+mTJmi//znP5layys9++yzypcvn2bMmKFXXnlFXl5eCgsLU8+ePa/azxEAACCDWzo7/QIAANxzPv/8c1WuXFlly5Z1HNu0aZO6dOmilStXXnW2niv5/fff9eOPP6phw4ZOH4Tx2muv6dixY1q+fLmNdQAAAHcPZ/wBAADcg1atWqUJEyaoR48eKlmypGJjYzV58mTVqVPHpYd+0j9n7fXr108NGzbU//3f/ylnzpzaunWrvvrqK7311lt25wEAANw1nPEHAABwDzp9+rTGjRunLVu26NSpU/L29lajRo302muvKV++fHbnZbnt27dr6tSp+uWXX5SSkqJy5cqpffv2atKkid1pAAAAdw2DPwAAAAAAAMAF3dkO4AAAAAAAAACyJQZ/AAAAAAAAgAti8AcAAAAAAAC4ID7V9x6QJ7i73Qm35fTOCLsTAAAAAACwhOc9PKExdW5xPYl7ss88gzP+AAAAAAAAABfE4A8AAAAAAABwQQz+AAAAAAAAABd0D19BDgAAAAAAANu5cV5aVmFlAQAAAAAAABfE4A8AAAAAAABwQQz+AAAAAAAAABfE4A8AAAAAAABwQXy4BwAAAAAAAOzj5mZ3gcvijD8AAAAAAADABTH4AwAAAAAAAFwQgz8AAAAAAADABbHHHwAAAAAAAOzjxnlpWYWVBQAAAAAAAFwQgz8AAAAAAADABTH4AwAAAAAAAFwQe/wBAAAAAADAPm5udhe4LM74y2aOHz+ugIAAHT9+3O6Ua/LI5a5diwcorKa/49jYPs2VuCfC6avrC3VtrLy+5ORkDR08QKH/qaWG9UI1Z/Ysu5NuysRmiW6rmdhtYrNEt9Xoto6JzRLdVjOx28RmiW6r0W0dE5slc7sBzvhDpuX2cNecMeEKLF/K6XjFsiU1ePJKfbJqu+PYmfNJVudlyvix7+rn/fv14aw5iouL0+ABfVWqZCk91qix3WnXZWKzRLfVTOw2sVmi22p0W8fEZoluq5nYbWKzRLfV6LaOic2Sud0Agz9kSsWyJTR7TPg1z76t6OejCXPW68RfZ60PuwUXLlzQ8qWLNXX6h6pUOVCVKgcq5nC0Fnw2P9v+sjaxWaLbaiZ2m9gs0W01uq1jYrNEt9VM7DaxWaLbanRbx8RmydxuQOJS3zuye/dutWzZUtWqVVP16tXVuXNn/fnnn1q2bJlatmypsWPHKjg4WPXr19fixYsdz2vbtq0iIiIcz23VqpViYmKu+R5nzpxRnz59VKNGDYWGhmrkyJFKSrL+bLqwmuW1Zech1X9xnNPx/Pk8dZ9PYUXH/ml50606dPCAUlJSVL16sONYcI2a2he1V2lpaTaWXZ+JzRLdVjOx28RmiW6r0W0dE5sluq1mYreJzRLdVqPbOiY2S+Z2G8Uth2t9ZSPZq8YgZ8+eVZcuXfTwww/r888/18yZM/Xbb7/pgw8+kCTt27dPv/zyixYuXKju3btr+PDh2rZtm+P5M2bMUKNGjbRs2TL5+PjopZde0sWLF696n4EDB+rs2bP67LPPNG3aNO3bt08jRoyw7PvM8OHibXpz3DIlJl1yOl7Rz0dpaWnq26mRDq8bqciF/dS6aYjlfZlxMj5ehQoVVi4PD8exokW9lZycrISEBPvCbsDEZoluq5nYbWKzRLfV6LaOic0S3VYzsdvEZoluq9FtHRObJXO7AYlLfW9bUlKSXn75ZbVv315ubm66//779fjjjysqKkqVK1eWm5ub3n33XRUtWlQVKlTQzp07tWjRIoWGhkqS6tatq/DwcEnSyJEjFRYWpm+//Vb+/v9+aMZvv/2m9evXa8eOHcqfP7/jsc2aNVP//v0dx+xUwa+E0tOlQ0dP6P0FmxVW019TB7XQ2XOJWvVNlN15ThKTEuVx2S9qSY7bl64xdM0OTGyW6Laaid0mNkt0W41u65jYLNFtNRO7TWyW6LYa3dYxsVkytxuQGPzdtmLFiqlZs2aaPXu2fvnlFx0+fFgHDx5UjRo1JEm+vr4qWrSo4/FBQUFasGCB43bG4yTJy8tLfn5+iomJcRr8xcTEKC0tTXXrOn9CblpammJjYxUUFJRV316mzV8dqTWb9+n0mQuSpP3RcfL3La7Oz4Vlu8Ff7ty5rzqrMuO2p6enHUk3ZWKzRLfVTOw2sVmi22p0W8fEZoluq5nYbWKzRLfV6LaOic2Sud2AxODvtp04cULNmzdXYGCgHnroIT3//PPatGmT9u7dK0lyd3de2tTUVOXI8e+V1Te7P+NY/vz5tXTp0qve38fH5259K3csY+iX4cCvf6he7Qo21Vxf8eI+Skg4rZSUFMf6nzwZL09PT+UvUMDmumszsVmi22omdpvYLNFtNbqtY2KzRLfVTOw2sVmi22p0W8fEZsncbqNc65NEcVewx99t+vrrr1WwYEHNmDFDL774omrVqqVjx44pPT1dkhQbG6vz5887Hr9//35VqPDvMOzAgQOOP589e1a//fabAgICnN7Dz89PZ8+elZubm3x9feXr66ukpCS9++6719wP0A6Duz2lL6Z3dzpWNaC0Dh09YVPR9QVUrCR3d3dF7f3RcWzPD7sVGFTlqqFrdmFis0S31UzsNrFZottqdFvHxGaJbquZ2G1is0S31ei2jonNkrndgMTg77YVKlRIcXFx+v7773Xs2DF98MEH+uqrrxwDuQsXLmjo0KGKiYnRokWLtG7dOrVq1crx/NWrV2vFihWKiYnRwIEDVapUKYWEOH8oRrly5RQWFqbevXsrKipKP/30k/r3768LFy6oQDb5rwprNu9TWA1/9WjbUH6lvdX5uVC1blJHE+dusDvtKnny5FHTp5tp1Ihh2r8vShs3rNfc2bPUqk07u9Ouy8RmiW6rmdhtYrNEt9Xoto6JzRLdVjOx28RmiW6r0W0dE5slc7sBSXJLzzhFDbckNTVVI0aM0Jo1a+Tm5qYqVaooLCxMU6ZMUZ8+fTR9+nQ98cQTWrBggYoVK6Y33nhDTzzxhCSpbdu2Kl26tI4cOaJffvlFtWrV0siRI1WqVCkdP35cDRs21IYNG1S6dGmdOnVKo0aN0qZNm+Tu7q6wsDANGjRIhQsXznRrnuDuN3/QLUjcE6HHO03S1t3RkqQm9atocLen5P9AccXGndKwqau1cuPeO36f0zsj7vg1rpSYmKjRI4Zp/ddfySu/l8Lbd1SbduF3/X3uJhObJbqtZmK3ic0S3Vaj2zomNkt0W83EbhObJbqtRrd1TGyWrOn2vIc3Y8sT0sfuhLsqMfI9uxMcGPxlgWXLlikiIkIbN2685v1t27ZVnTp19Oqrr1rSc7cHf1bJisEfAAAAAADZ0T09+PtPX7sT7qrE7e/YneDApb4AAAAAAACAC2LwBwAAAAAAALggLvW9B3CpLwAAAAAA2RuX+rqO7HSp7z38YwUAAAAAAADbubnZXeCyuNQXAAAAAAAAcEEM/gAAAAAAAAAXxOAPAAAAAAAAcEHs8QcAAAAAAAD7uHFeWlZhZQEAAAAAAAAXxOAPAAAAAAAAcEEM/gAAAAAAAAAXxB5/AAAAAAAAsI+bm90FLosz/gAAAAAAAAAXxOAPAAAAAAAAcEEM/gAAAAAAAAAXxB5/AAAAAAAAsI8b56VlFVYWAAAAAAAAcEEM/gAAAAAAAAAXxKW+94DTOyPsTrgthWt3tzvhtpi63gAAAAAAwLUw+AMAAAAAAIB93NzsLnBZXOoLAAAAAAAAuCAGfwAAAAAAAIALYvAHAAAAAAAAuCD2+AMAAAAAAIB93DgvLauwsgAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIALYo8/AAAAAAAA2Ic9/rIMKwsAAAAAAAC4IAZ/AAAAAAAAgAti8AcAAAAAAAC4IPb4AwAAAAAAgH1yuNld4LI44w8AAAAAAABwQQz+AAAAAAAAABfE4A8AAAAAAABwQQz+/r/jx48rICBAx48ftzvFCMnJyRo6eIBC/1NLDeuFas7sWXYn3ZBHLnftWjxAYTX9HcfG9mmuxD0RTl9dX6hrY+W1mbbWGei2londJjZLdFuNbuuY2CzRbTUTu01slui2Gt3WMbFZMrfbGG45XOvrNly8eFFNmjRRZGTkVfedPXtWYWFhWrZs2S2/Lh/ugdsyfuy7+nn/fn04a47i4uI0eEBflSpZSo81amx32lVye7hrzphwBZYv5XS8YtmSGjx5pT5Ztd1x7Mz5JKvzbsqktb4c3dYysdvEZoluq9FtHRObJbqtZmK3ic0S3Vaj2zomNkvmdsMMycnJ6tWrl6Kjo695/3vvvac///zztl6bwR9u2YULF7R86WJNnf6hKlUOVKXKgYo5HK0Fn83Pdr/0KpYtodljwuV2jQ8Iqujnowlz1uvEX2etD8skk9b6cnRby8RuE5sluq1Gt3VMbJbotpqJ3SY2S3RbjW7rmNgsmdsNMxw+fFi9evVSenr6Ne/ftWuXtm/frmLFit3W63Op7xXWrVununXrqkaNGhoyZIguXrwoSVq8eLEaN26soKAghYSEaPjw4UpNTZUk9evXT2+99ZZ69OihatWqqV69elqxYoXjNU+cOKHXXntNtWvXVlBQkJ555hnt3r1b0r+XGH/11Vd69NFHVaVKFXXp0kUJCQmO59/ove1w6OABpaSkqHr1YMex4Bo1tS9qr9LS0mzrupawmuW1Zech1X9xnNPx/Pk8dZ9PYUXH3t7E3ComrfXl6LaWid0mNkt0W41u65jYLNFtNRO7TWyW6LYa3dYxsVkytxtm2LFjh0JCQrRw4cKr7rt48aIGDx6sIUOGyMPD47Zen8HfFRYtWqQJEyZo+vTp2rJli2bMmKEdO3Zo1KhR6tmzp9atW6fhw4dryZIl2rBhg+N58+fPV2BgoD7//HM9/vjjGjp0qM6e/edMst69eys1NVULFizQihUr5OPjo2HDhjm97/Tp0zV+/HjNmzdP+/bt08cffyxJmXpvq52Mj1ehQoWV67IfuqJFvZWcnOw0sMwOPly8TW+OW6bEpEtOxyv6+SgtLU19OzXS4XUjFbmwn1o3DbGp8vpMWuvL0W0tE7tNbJbothrd1jGxWaLbaiZ2m9gs0W01uq1jYrNkbrdR3Nxc6+sWtGrVSgMGDFCePHmuum/69OmqXLmyQkNDb3tpGfxdYcCAAapZs6bq1Kmj119/XQsWLFDevHk1evRoPf744ypdurQaN26sypUrO117HRAQoM6dO+v+++/X66+/rqSkJEVHRys9PV2PPvqoBg8erHLlyql8+fJq3bq1Dh8+7PS+r732mqpWrapq1aqpadOm2rdvnyRl6r2tlpiUeNWkOeP2pf9/hmR2V8GvhNLTpUNHT6jZq+9r9vLvNXVQC/33kap2pzkxda3ptpaJ3SY2S3RbjW7rmNgs0W01E7tNbJbothrd1jGxWTK3G2Y7fPiwFixYoP79+9/R67DH3xWqVv138FO5cmWdPHlS999/vzw9PTV58mQdPnxYBw8eVGxsrNPEtUyZMo4/e3l5SZJSUlLk5uamli1bas2aNfrhhx905MgR7d+//6rTgX19fZ2ef+nSP2eoBQUF3fS9rZY7d27HJdAZMm57enrakXTL5q+O1JrN+3T6zAVJ0v7oOPn7Flfn58K06psom+v+Zepa020tE7tNbJbothrd1jGxWaLbaiZ2m9gs0W01uq1jYrNkbjfMlZ6erkGDBum1116Tt7f3Hb0WZ/xdIUeOf5ckY2PFnTt36tlnn9XJkycVFhamyZMnq0aNGk7Py5Ur11WvlZ6errS0NHXo0EGzZs1SqVKl1LFjR7377rtXPfZaz5ekrVu33vS9rVa8uI8SEk4rJSXFcezkyXh5enoqf4ECNpbdmoyhX4YDv/6hUsUL2RNzHaauNd3WMrHbxGaJbqvRbR0TmyW6rWZit4nNEt1Wo9s6JjZL5nbDXHFxcdqzZ4/eeecdBQcHKzg4WHFxcRo6dKg6dep0S6/F4O8Khw4dcvw5KipKJUqU0KpVq9S8eXONGDFCzz33nMqVK6fffvvtup+4crnDhw9r586dmj17trp27ar69es7PoI5M89fvHjxbb93VgmoWEnu7u6K2vuj49ieH3YrMKiK0+A0Oxvc7Sl9Mb2707GqAaV16OgJm4quzdS1pttaJnab2CzRbTW6rWNis0S31UzsNrFZottqdFvHxGbJ3G6juOVwra875OPjo6+++korVqxwfBUvXlyvvfaaRo8efUuvxU/oFUaOHKm9e/fq22+/1eTJkxUeHq5ChQppz549OnjwoKKjo9WvXz/Fx8dfdarvtRQoUEA5cuTQF198od9//13r1q3TlClTJClTz7+T984qefLkUdOnm2nUiGHavy9KGzes19zZs9SqTTvbmm7Vms37FFbDXz3aNpRfaW91fi5UrZvU0cS59n1oyrWYutZ0W8vEbhObJbqtRrd1TGyW6Laaid0mNkt0W41u65jYLJnbDXO5u7vL19fX6cvd3V1FixaVj4/PLb2WW7qdp45lI8ePH1fDhg01dOhQRURE6NKlS3r++efVq1cvnTx5Uv3799fu3bvl5eWlevXqKVeuXDp27Jhmzpypfv36SZLefvttx+sFBARo7ty5jo9knjp1qs6ePSs/Pz916NBBffv21bx581SsWDE1bNhQGzZsUOnSpSVJU6ZM0Y4dO/TJJ5/ozz//vOF7Z0ZSys0fc6sSExM1esQwrf/6K3nl91J4+45q0y78rr5H4drdb/6gW5C4J0KPd5qkrbv/+WCUJvWraHC3p+T/QHHFxp3SsKmrtXLj3jt+n9M7I+74NS5nxVpnBbqtZWK3ic0S3Vaj2zomNkt0W83EbhObJbqtRrd1TGyWrOn2vIc/hSHPo2/f/EEGSVzf77aed/ks6UoNGjRQ9+7d9eyzz97SazL4uwdkxeDPCnd78GeVuz34AwAAAAC4PgZ/ruN2B39Z4R7+sQIAAAAAAIDt3NzsLnBZ7PEHAAAAAAAAuCAGfwAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIAL4sM9AAAAAAAAYB83zkvLKqwsAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAD2cXOzu8BlccYfAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAD2ceO8tKzCygIAAAAAAAAuiMEfAAAAAAAA4IIY/AEAAAAAAAAuiD3+AAAAAAAAYB83N7sLXBaDP2Rbp3dG2J1wWwrX7m53wi0zda0BAAAAAMD1cakvAAAAAAAA4IIY/AEAAAAAAAAuiEt9AQAAAAAAYB83zkvLKqwsAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAD2cXOzu8BlccYfAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAD2ceO8tKzCygIAAAAAAAAuiMEfAAAAAAAA4IIY/AEAAAAAAAAuiD3+AAAAAAAAYB/2+MsyrCwAAAAAAADggowf/B0/flwBAQE6fvz4VfctW7ZMDRo0uOP36Nevn/r163fHr+NKkpOTNXTwAIX+p5Ya1gvVnNmz7E7KFJO6PXK5a9fiAQqr6e84NrZPcyXuiXD66vpCXRsrr8+ktb4c3dYxsVmi22p0W8fEZoluq5nYbWKzRLfV6LaOic2Sud0Al/ritowf+65+3r9fH86ao7i4OA0e0FelSpbSY40a2512Q6Z05/Zw15wx4QosX8rpeMWyJTV48kp9smq749iZ80lW52WKKWt9JbqtY2KzRLfV6LaOic0S3VYzsdvEZoluq9FtHRObJXO7AQZ/uGUXLlzQ8qWLNXX6h6pUOVCVKgcq5nC0Fnw2P1v/0jOlu2LZEpo9Jlxubte4z89HE+as14m/zlofdgtMWesr0W0dE5sluq1Gt3VMbJbotpqJ3SY2S3RbjW7rmNgsmdttlGv9D2DcFcZf6pth3bp1qlu3rmrUqKEhQ4bo4sWLVz1mw4YNatasmapUqaJatWqpZ8+eOn/+vOP+lStXqnHjxqpWrZpatGihn3/++arXOHXqlBo1aqT+/fsrPT1daWlpGjt2rEJCQhQSEqJp06bpscceU2RkpCQpICBAkyZNUkhIiLp27SpJWrx4sRo3bqygoCCFhIRo+PDhSk1NlfTPZcXvvfeeevTooWrVqunJJ5/Uzz//rAkTJqhWrVqqW7eu1q5dmxVLmGmHDh5QSkqKqlcPdhwLrlFT+6L2Ki0tzcayGzOlO6xmeW3ZeUj1XxzndDx/Pk/d51NY0bF/2lSWeaas9ZXoto6JzRLdVqPbOiY2S3RbzcRuE5sluq1Gt3VMbJbM7QYkFxr8LVq0SBMmTND06dO1ZcsWzZgxw+n+3377Ta+//rpatWqltWvXauLEifruu++0aNEiSdLWrVs1cOBAvfjii1q1apWCgoLUpUsXpwFiYmKiunXrpnLlymnUqFFyc3PTjBkztGLFCo0bN04ff/yxNm3apGPHjjm99zfffKPPPvtMvXv31o4dOzRq1Cj17NlT69at0/Dhw7VkyRJt2LDB8fg5c+aoTp06WrVqlQoVKqQXX3xRf/31lxYuXKgGDRpo6NChtv5yORkfr0KFCiuXh4fjWNGi3kpOTlZCQoJtXTdjSveHi7fpzXHLlJh0yel4RT8fpaWlqW+nRjq8bqQiF/ZT66YhNlXemClrfSW6rWNis0S31ei2jonNEt1WM7HbxGaJbqvRbR0TmyVzuwHJhQZ/AwYMUM2aNVWnTh29/vrrWrBggdP9aWlpGjRokJ5//nmVLl1aoaGheuihhxQdHS1JWrhwoZo0aaKWLVvK19dXb775ppo0aaK///5bkpSamqo33nhDHh4emjhxonLmzClJ+vTTT9WjRw+FhoaqcuXKevvtt5Wenu703i+88ILKli2r8uXLK2/evBo9erQef/xxlS5dWo0bN1blypUdHZIUFBSkVq1aydfXV02aNFFiYqIGDRqkcuXKqW3btvr777918uTJrFzOG0pMSpTHZb/wJDluX7rGmZbZhandGSr4lVB6unTo6Ak1e/V9zV7+vaYOaqH/PlLV7rSrmLrWdFvHxGaJbqvRbR0TmyW6rWZit4nNEt1Wo9s6JjZL5nYDkgvt8Ve16r/Dj8qVK+vkyZM6c+aM41iZMmXk4eGh999/X9HR0YqOjtbhw4f19NNPS5KOHDmiFi1aOB7v4eGhvn37Om6vXbtWKSkpaty4seMf+KlTp/Tnn3+qSpUqjseVLVtWBQsWdGq77777HH8OCgqSp6enJk+erMOHD+vgwYOKjY1VaGio4zGlS5d2/NnT01Pe3t7y9PSUJOXOnVuSrnkps1Vy58591ftn3M7ozI5M7c4wf3Wk1mzep9NnLkiS9kfHyd+3uDo/F6ZV30TZXOfM1LWm2zomNkt0W41u65jYLNFtNRO7TWyW6LYa3dYxsVkyt9sobi5zXlq24zIrmyPHv99Kxhl3uXLlchw7cOCAnnrqKR0+fFi1atXS6NGj9eSTTzrud3e/8Qy0ZMmSmjVrlr766it99913Ts+58gy/K29nDOukfy4pfvbZZ3Xy5EmFhYVp8uTJqlGjhtPjr2y5/HvLDooX91FCwmmlpKQ4jp08GS9PT0/lL1DAxrIbM7X7chlDvwwHfv1DpYoXsifmBkxda7qtY2KzRLfV6LaOic0S3VYzsdvEZoluq9FtHRObJXO7AcmFBn+HDh1y/DkqKkolSpRQnjx5HMdWrlyp2rVra9y4cWrVqpWqVq2q2NhYx5DO19dXBw4ccDw+NTVVDRo00O7duyVJNWvW1EMPPaTnn39eI0eO1KVLl1SgQAEVL15cP/30k+N5x44dczrT8EqLFy9W8+bNNWLECD333HMqV66cfvvtt6uGhdlZQMVKcnd3V9TeHx3H9vywW4FBVbLdkPJypnZnGNztKX0xvbvTsaoBpXXo6Ambiq7P1LWm2zomNkt0W41u65jYLNFtNRO7TWyW6LYa3dYxsVkytxuQXGjwN3LkSO3du1fffvutJk+erPDwcKf7CxUqpIMHDyoqKkpHjhzR22+/rX379jlOz23btq1WrVql5cuXKzY2Vm+99ZbS09MVGBjo9Do9evTQqVOn9PHHHzueN3nyZH3//fc6cOCA+vfvL0lyu85HURcqVEh79uzRwYMHFR0drX79+ik+Pt7WS3dvVZ48edT06WYaNWKY9u+L0sYN6zV39iy1atPO7rQbMrU7w5rN+xRWw1892jaUX2lvdX4uVK2b1NHEuRtu/mSLmbrWdFvHxGaJbqvRbR0TmyW6rWZit4nNEt1Wo9s6JjZL5nYDkuSWbtKpZtdw/PhxNWzYUEOHDlVERIQuXbqk559/Xr169dKKFSsUERGhjRs36sKFC+rfv7+2bt2q3Llzq3bt2ipfvry++OILffnll5KkJUuWaPr06YqPj1dQUJCGDBmigIAA9evXT5L09ttvS5Lmz5+vsWPHas2aNSpWrJjefvttrVixQjlz5tRLL72k9957T5999pmCg4MVEBCguXPnKiTkn09f/fPPP9W/f3/t3r1bXl5eqlevnnLlyqVjx45p5syZV73XsmXLHN/D5d/vhg0bnPYCvJGklJs/5lYlJiZq9IhhWv/1V/LK76Xw9h3Vpl343X+ju8yK7sK1u9/8QZmUuCdCj3eapK27//nwlyb1q2hwt6fk/0Bxxcad0rCpq7Vy4947fp/TOyPu+DWuxM+ItUzsNrFZottqdFvHxGaJbquZ2G1is0S31ei2jonNkjXdni7zKQy3Lk+zD+xOuKsSV7xkd4KD8YM/u23ZskVBQUEqUqSIpH8+8OPBBx+8pcFcVsuKwR+u724O/qySFYM/AAAAAEDmMfhzHdlp8HcP/1jdHQsXLtSnn36q3r17y83NTZMmTVKVKlWyzdAPAAAAAAAA9yaX2ePPLkOGDFGOHDnUokULPf/880pLS9PUqVPtzgIAAAAAAMA9jjP+7pCPj4+mTZtmdwYAAAAAAICZ3DgvLauwsgAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIALYo8/AAAAAAAA2MfNze4Cl8UZfwAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIALYo8/AAAAAAAA2MaNPf6yDGf8AQAAAAAAAC6IwR8AAAAAAADgghj8AQAAAAAAAC6IPf4AAAAAAABgG/b4yzqc8QcAAAAAAAC4IAZ/AAAAAAAAgAti8AcAAAAAAAC4IPb4A+6y0zsj7E64ZYVrd7c74baYuNYAAAAAgCuwxV+W4Yw/AAAAAAAAwAUx+AMAAAAAAABcEIM/AAAAAAAAwAWxxx8AAAAAAABs4+bGJn9ZhTP+AAAAAAAAABfE4A8AAAAAAABwQQz+AAAAAAAAABfEHn8AAAAAAACwDXv8ZR3O+AMAAAAAAABcEIM/AAAAAAAAwAUx+AMAAAAAAABcEIM/AAAAAAAAwAXx4R4AAAAAAACwDR/ukXU44w8AAAAAAABwQQz+AAAAAAAAABfE4A8AAAAAAABwQezxBwAAAAAAANuwx1/WuafP+IuMjFRAQMB1758yZYratm1709fJ7ONcSXJysoYOHqDQ/9RSw3qhmjN7lt1JmWJit2nNHrnctWvxAIXV9HccG9unuRL3RDh9dX2hro2V12faemcwsdvEZoluq9FtHRObJbqtZmK3ic0S3Vaj2zomNkvmdgOc8XcDHTp0uOcGepk1fuy7+nn/fn04a47i4uI0eEBflSpZSo81amx32g2Z2G1Sc24Pd80ZE67A8qWcjlcsW1KDJ6/UJ6u2O46dOZ9kdV6mmLTelzOx28RmiW6r0W0dE5sluq1mYreJzRLdVqPbOiY2S+Z2Awz+biBfvnx2J2RLFy5c0PKlizV1+oeqVDlQlSoHKuZwtBZ8Nj9b/9Izsduk5oplS2j2mHBd6wztin4+mjBnvU78ddb6sFtg0npfzsRuE5sluq1Gt3VMbJbotpqJ3SY2S3RbjW7rmNgsmdsNSPfQpb6xsbHq2LGjgoODVb9+fc2dO9dx32effaawsDAFBwerf//+unjxoqSrL+HdsmWLnnnmGVWrVk3//e9/9f3331/1PsnJyWrZsqU6dOigixcvql+/fho1apS6du2qqlWrqlmzZvrhhx8cjz9z5oz69OmjGjVqKDQ0VCNHjlRS0r9nQm3YsEHNmjVTlSpVVKtWLfXs2VPnz5/PiiXKtEMHDyglJUXVqwc7jgXXqKl9UXuVlpZmY9mNmdhtUnNYzfLasvOQ6r84zul4/nyeus+nsKJj/7SpLPNMWu/LmdhtYrNEt9Xoto6JzRLdVjOx28RmiW6r0W0dE5slc7uN4uZiX9nIPTH4S05OVocOHZQvXz4tWrRIQ4YM0YQJE3ThwgVJ0pdffqmZM2cqIiJC69at09KlS696jejoaHXr1k2PPfaYVq5cqSZNmujll19WfHy84zFpaWnq2bOn0tLSFBERIQ8PD0nSggULVL58eS1fvly1a9fWSy+9pFOnTkmSBg4cqLNnz+qzzz7TtGnTtG/fPo0YMUKS9Ntvv+n1119Xq1attHbtWk2cOFHfffedFi1alNVLdkMn4+NVqFBh5fr/358kFS3qreTkZCUkJNgXdhMmdpvU/OHibXpz3DIlJl1yOl7Rz0dpaWnq26mRDq8bqciF/dS6aYhNlTdm0npfzsRuE5sluq1Gt3VMbJbotpqJ3SY2S3RbjW7rmNgsmdsNSPfIpb7btm3TqVOnNGbMGHl5ecnf31+DBg1Sjhz/zD2HDh0qPz8/VahQQQ899JAOHDhw1WssWbJENWrU0MsvvyxJeumll3ThwgWdOXPG8ZiRI0cqNjZW8+bNU968eR3Hy5cvr969e0uS+vfvr40bN2rNmjWqW7eu1q9frx07dih//vyO12jWrJn69++vtLQ0DRo0SM8//7wkqXTp0nrooYcUHR2dNQuVSYlJiY6hZoaM25f+/9mS2ZGJ3SY2X6mCXwmlp0uHjp7Q+ws2K6ymv6YOaqGz5xK16psou/OcmLreJnab2CzRbTW6rWNis0S31UzsNrFZottqdFvHxGbJ3G5AukcGf0eOHJGfn5+8vLwcx5o3b67IyEhJ0gMPPOA4nj9/fselvle+RmBgoNOxHj16OP68Z88e7dy5U9WqVVPBggWdHlejRg3Hn3PkyKHKlSsrJiZG9913n9LS0lS3rvOnm6alpSk2NlZBQUHy8PDQ+++/r+joaEVHR+vw4cN6+umnb30R7qLcuXNftUYZtz09Pe1IyhQTu01svtL81ZFas3mfTp/55wzb/dFx8vctrs7PhWW7wZ+p621it4nNEt1Wo9s6JjZLdFvNxG4TmyW6rUa3dUxslsztBqR7ZPDn7n7jbzNnzpxOt9PT02/5NfLly6eIiAh16dJFS5Ys0XPPPXfd56ampipHjhxKTU1V/vz5r3lpsY+Pjw4cOKCWLVuqQYMGqlWrlsLDwzVnzpwbdliheHEfJSScVkpKiuN7O3kyXp6enspfoIDNdddnYreJzdeSMfTLcODXP1SvdgWbaq7P1PU2sdvEZoluq9FtHRObJbqtZmK3ic0S3Vaj2zomNkvmdpvE7VqfEom74p7Y469MmTKKjY1VYmKi49g777yjUaNGZfo1fH19r7oEuEWLFvriiy8kSRUqVFDt2rXVrVs3jRs3zuk6/19++cXx59TUVB04cEABAQHy8/PT2bNn5ebmJl9fX/n6+iopKUnvvvuuLl68qJUrV6p27doaN26cWrVqpapVqyo2Nvaag0krBVSsJHd3d0Xt/dFxbM8PuxUYVMVx+XR2ZGK3ic1XGtztKX0xvbvTsaoBpXXo6Ambiq7P1PU2sdvEZoluq9FtHRObJbqtZmK3ic0S3Vaj2zomNkvmdgPSPTL4Cw0Nlbe3t4YMGaKYmBht2LBBCxYsUK9evTL9Gi1bttSuXbv08ccfKzY2VjNmzFB0dLRq1arl9LgXX3xRBQsW1Pjx4x3HduzYoVmzZunXX3/V6NGjlZiYqMaNG6tcuXIKCwtT7969FRUVpZ9++kn9+/fXhQsXVKBAARUqVEgHDx5UVFSUjhw5orffflv79u275qXIVsqTJ4+aPt1Mo0YM0/59Udq4Yb3mzp6lVm3a2dp1MyZ2m9h8pTWb9ymshr96tG0ov9Le6vxcqFo3qaOJczfYnXYVU9fbxG4TmyW6rUa3dUxslui2mondJjZLdFuNbuuY2CyZ2w1Iklu63aePWSQmJkYjRozQnj175O3trc6dO6ts2bJq166dDh486Hhcv379JElvv/22pkyZoh07duiTTz6RJH3zzTcaN26cYmNj5e/vr/79+6t27dpXPW7Tpk3q1q2bFi1apPnz5+vvv/9Wamqqtm/frsqVK2vYsGGqWLGiJOnUqVMaNWqUNm3aJHd3d4WFhWnQoEEqXLiwLly4oP79+2vr1q3KnTu3ateurfLly+uLL77Ql19+menvPSnlbq3ivxITEzV6xDCt//oreeX3Unj7jmrTLvzuv9FdZmK3Fc2Fa3e/+YNuQeKeCD3eaZK27v7ng2ia1K+iwd2ekv8DxRUbd0rDpq7Wyo177/h9Tu+MuOPXuJKJPyOSmd0mNkt0W41u65jYLNFtNRO7TWyW6LYa3dYxsVmyptvzntiM7doKtZ5nd8JdlTC/jd0JDvfM4M8ulw8S7ZIVgz+4lrs9+LNKVgz+AAAAAMAO9/Lgr3Cb+XYn3FWn57W2O8HhnrjUFwAAAAAAALjXMPgDAAAAAAAAXNA9fCKpNey8xBcAAAAAAAD3LgZ/AAAAAAAAsI2bm5vdCS6LS30BAAAAAAAAF8TgDwAAAAAAAHBBDP4AAAAAAAAAF8QefwAAAAAAALANe/xlHc74AwAAAAAAAFwQgz8AAAAAAADABTH4AwAAAAAAAFwQe/wBAAAAAADAPmzxl2U44w8AAAAAAABwQQz+AAAAAAAAABfE4A8AAAAAAABwQezxBwAAAAAAANu4ubHJX1bhjD8AAAAAAADABXHGHwCd3hlhd8JtKVy7u90Jt8XU9QYAAAAAmIUz/gAAAAAAAAAXxBl/AAAAAAAAsA17/GUdzvgDAAAAAAAAXBCDPwAAAAAAAMAFMfgDAAAAAAAAXBB7/AEAAAAAAMA27PGXdTjjDwAAAAAAAHBBDP4AAAAAAAAAF8TgDwAAAAAAAHBB7PEHAAAAAAAA+7DFX5bhjD8AAAAAAADABTH4AwAAAAAAAFwQgz8AAAAAAADABbHHHwAAAAAAAGzj5sYmf1mFM/4AAAAAAAAAG128eFFNmjRRZGSk49iPP/6oFi1aKDg4WI0aNdLixYtv+XUZ/AEAAAAAAAA2SU5OVs+ePRUdHe04Fh8fr86dO6tOnTpavny5XnvtNY0cOVKbNm26pdfmUl8AAAAAAADABocPH1avXr2Unp7udHz9+vXy9vZWz549JUllypRRZGSkVq9erfr162f69TnjzzBr167VX3/9ZXeGkpOTNXTwAIX+p5Ya1gvVnNmz7E7KFBO7TWyWzOv2yOWuXYsHKKymv+PY2D7Nlbgnwumr6wt1bay8PtPWWzKzWaLbanRbx8RmiW6rmdhtYrNEt9Xoto6JzZK53aZwc3Nzqa9bsWPHDoWEhGjhwoVOx8PCwvTWW29d9fhz587d0utzxp9Bfv/9d/Xo0UMbNmywO0Xjx76rn/fv14ez5iguLk6DB/RVqZKl9Fijxnan3ZCJ3SY2S2Z15/Zw15wx4QosX8rpeMWyJTV48kp9smq749iZ80lW52WKSeudwcRmiW6r0W0dE5sluq1mYreJzRLdVqPbOiY2S+Z2I/tr1arVNY+XLl1apUuXdtz+66+/9MUXX+jVV1+9pddn8GeQK0/7tMuFCxe0fOliTZ3+oSpVDlSlyoGKORytBZ/Nz9a/9EzsNrFZMqu7YtkSmj0mXNf6jzIV/Xw0Yc56nfjrrPVht8Ck9c5gYrNEt9Xoto6JzRLdVjOx28RmiW6r0W0dE5slc7vhOpKSkvTqq6/K29tbL7zwwi09956/1Dc2NlYdO3ZUcHCw6tevr7lz50qSYmJi1LFjR9WoUUNhYWGKiIhQWlqa43krV65U48aNVa1aNbVo0UI///yz475ly5bpiSeeUNWqVfXss89q586djvsaNGig+fPn6/nnn1eVKlX09NNPa//+/ZKk48ePKyAgQMePH3c8fsqUKWrbtq0kqWHDho7/u2zZsqxblJs4dPCAUlJSVL16sONYcI2a2he112mNshsTu01slszqDqtZXlt2HlL9F8c5Hc+fz1P3+RRWdOyfNpVlnknrncHEZoluq9FtHRObJbqtZmK3ic0S3Vaj2zomNkvmdsM1nD9/Xl26dNHRo0c1Y8YM5cmT55aef08P/pKTk9WhQwfly5dPixYt0pAhQzRhwgStXLlSrVq1UvHixbV48WINHTpU8+bNcwwFt27dqoEDB+rFF1/UqlWrFBQUpC5duujixYtatmyZRo4cqS5dumjFihV66KGH9NJLL+nEiROO950yZYpeeuklrVq1Svnz59eoUaMy1Zvxsc2LFy/Wk08+efcXJJNOxserUKHCyuXh4ThWtKi3kpOTlZCQYFvXzZjYbWKzZFb3h4u36c1xy5SYdMnpeEU/H6Wlpalvp0Y6vG6kIhf2U+umITZV3phJ653BxGaJbqvRbR0TmyW6rWZit4nNEt1Wo9s6JjZL5nabxO49+ezc4+9Gzp07p44dOyo6Olpz5sxRmTJlbvk17ulLfbdt26ZTp05pzJgx8vLykr+/vwYNGqSEhATlyZNHI0eOlLu7u8qVK6f4+HhNnTpV4eHhWrhwoZo0aaKWLVtKkt58803lypVLf//9tz755BO1bdtWzZo1kyT17t1bO3fu1Lx589SrVy9J0jPPPKNHH31UktS+fXu9/vrrmeotUqSI4/96enre5dXIvMSkRHlc9gtPkuP2pYsX7UjKFBO7TWyWzO2+XAW/EkpPlw4dPaH3F2xWWE1/TR3UQmfPJWrVN1F25zkxcb1NbJbothrd1jGxWaLbaiZ2m9gs0W01uq1jYrNkbjfMlpaWpu7du+v48eP65JNPVK5cudt6nXt68HfkyBH5+fnJy8vLcax58+YaOnSoAgMD5e7+7/IEBwcrPj5eZ86c0ZEjR9SiRQvHfR4eHurbt6+kfy4RfuWVV5zep3r16oqJiXHcvnxC6+XlpUuXnM80yu5y586ti1f8csu4bedA8mZM7DaxWTK3+3LzV0dqzeZ9On3mgiRpf3Sc/H2Lq/NzYdlu8GfiepvYLNFtNbqtY2KzRLfVTOw2sVmi22p0W8fEZsncbphtyZIlioyM1Pvvv68CBQooPj5ekpQrVy4VKlQo069zTw/+Lh/sXS537txXHcu4bj81NfW6z7vec1NTU52u+8+VK9c1n3ut00FTUlKu+152KV7cRwkJp5WSkuJYi5Mn4+Xp6an8BQrYXHd9Jnab2CyZ232ljKFfhgO//qF6tSvYVHN9Jq63ic0S3Vaj2zomNkt0W83EbhObJbqtRrd1TGyWzO2G2b788kulpaWpS5cuTsfr1KmjTz75JNOvc0/v8VemTBnFxsYqMTHRceydd97Rp59+qp9++snpTLw9e/aoSJEiKlSokHx9fXXgwAHHfampqWrQoIF2794tPz8/7d271+l99u7dKz8/v5v2ZAwEz58/7zh2+Qd93M3rxO9EQMVKcnd3V9TeHx3H9vywW4FBVZQjR/b9kTKx28Rmydzuyw3u9pS+mN7d6VjVgNI6dPTEdZ5hHxPX28RmiW6r0W0dE5sluq1mYreJzRLdVqPbOiY2S+Z2m8TuPfmyyx5/Bw8eVEjIP3vLz5w5UwcPHrzq61aGftI9PvgLDQ2Vt7e3hgwZopiYGG3YsEELFizQxIkTdfHiRcfx9evXa8qUKWrZsqXc3NzUtm1brVq1SsuXL1dsbKzeeustpaenKzAwUOHh4Zo3b55WrFihI0eOaOzYsTpw4ID+7//+76Y93t7eKlmypGbOnKljx45p2bJl2rRpk+P+jE9uOXDggNNw0Gp58uRR06ebadSIYdq/L0obN6zX3Nmz1KpNO9uaMsPEbhObJXO7L7dm8z6F1fBXj7YN5VfaW52fC1XrJnU0ce4Gu9OuYuJ6m9gs0W01uq1jYrNEt9VM7DaxWaLbanRbx8RmydxuQJLc0tPT0+2OsFNMTIxGjBihPXv2yNvbW507d1bLli31888/a/To0YqKilKRIkXUokULdenSxTHNX7JkiaZPn674+HgFBQVpyJAhCggIkCTNnTtXs2fPVnx8vCpVqqQ+ffqodu3akqQGDRqoe/fuevbZZyVJkZGRateunQ4ePChJ+vbbbzVy5Ej9/vvvevDBB1W7dm1t2bLFMdHt06eP1q5dq969eys8PDxT32NSFlwtnJiYqNEjhmn911/JK7+Xwtt3VJt2meuxk4ndJjZL1nQXrt395g+6BYl7IvR4p0naujtaktSkfhUN7vaU/B8orti4Uxo2dbVWbtx7k1e5udM7I+74Na5k4s+Jic0S3Vaj2zomNkt0W83EbhObJbqtRrd1TGyWrOn2vIc3YyvVZZndCXdV3Ixn7U5wuOcHf/eCrBj8AdnB3R78WSUrBn8AAAAAzMbgz3Vkp8HfPfxjBQAAAAAAANtlj480cEn39B5/AAAAAAAAgKti8AcAAAAAAAC4IAZ/AAAAAAAAgAti8AcAAAAAAAC4ID7cAwAAAAAAALZxc+PTPbIKZ/wBAAAAAAAALojBHwAAAAAAAOCCGPwBAAAAAAAALog9/gAAAAAAAGAb9vjLOpzxBwAAAAAAALggBn8AAAAAAACAC2LwBwAAAAAAALgg9vgDAAAAAACAbdjjL+twxh8AAAAAAADgghj8AQAAAAAAAC6IwR8AAAAAAADggtjjD4CxTu+MsDvhthSu3d3uhNti6noDAAAAyObY4i/LcMYfAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAC2cXNjk7+swhl/AAAAAAAAgAti8AcAAAAAAAC4IAZ/AAAAAAAAgAtijz8AAAAAAADYhj3+sg5n/AEAAAAAAAAuiMEfAAAAAAAA4IIY/AEAAAAAAAAuiD3+AAAAAAAAYBv2+Ms6nPEHAAAAAAAAuCAGfwAAAAAAAIALYvAHAAAAAAAAuCD2+AMAAAAAAIBt2OMv63DGHwAAAAAAAOCCGPxlsfT0dA0ePFjVq1dXw4YNderUKbVp00ZVqlRR37597c67bcnJyRo6eIBC/1NLDeuFas7sWXYnZYqJ3SY2S3RbxSOXu3YtHqCwmv6OY2P7NFfinginr64v1LWx8tpMW+sMdFuLbuuY2CzRbTUTu01slui2Gt3WMbFZMrcb4FLfLHbgwAEtWrRIH3zwgQICArRq1SodPXpUK1asUOHChe3Ou23jx76rn/fv14ez5iguLk6DB/RVqZKl9Fijxnan3ZCJ3SY2S3RbIbeHu+aMCVdg+VJOxyuWLanBk1fqk1XbHcfOnE+yOu+mTFrry9FtLbqtY2KzRLfVTOw2sVmi22p0W8fEZsncboDBXxY7e/asJKlu3bpyc3PTuXPnVKZMGZUrV87mstt34cIFLV+6WFOnf6hKlQNVqXKgYg5Ha8Fn87P1Lz0Tu01slui2QsWyJTR7TLiutRVGRT8fTZizXif+Omt9WCaZtNaXo9tadFvHxGaJbquZ2G1is0S31ei2jonNkrndRmGLvyzDpb63IDY2Vh07dlRwcLDq16+vuXPnSpJiYmLUsWNH1ahRQ2FhYYqIiFBaWpoiIyPVtm1bSVLFihU1ZcoUTZkyRTt37lRAQIAiIyOVnp6uqVOnKjQ0VLVq1VLXrl0VFxfneM8zZ86oT58+qlGjhkJDQzVy5EglJdl75s6hgweUkpKi6tWDHceCa9TUvqi9SktLs7HsxkzsNrFZotsKYTXLa8vOQ6r/4jin4/nzeeo+n8KKjv3TprLMMWmtL0e3tei2jonNEt1WM7HbxGaJbqvRbR0TmyVzuwGJwV+mJScnq0OHDsqXL58WLVqkIUOGaMKECVq5cqVatWql4sWLa/HixRo6dKjmzZunuXPnKjg4WFOmTJEkbdu2TR06dFCHDh0UHBysbdu2KTg4WPPmzdPq1as1btw4LVy4UEWLFlWHDh106dIlSdLAgQN19uxZffbZZ5o2bZr27dunESNG2LkUOhkfr0KFCiuXh4fjWNGi3kpOTlZCQoJ9YTdhYreJzRLdVvhw8Ta9OW6ZEpMuOR2v6OejtLQ09e3USIfXjVTkwn5q3TTEpsrrM2mtL0e3tei2jonNEt1WM7HbxGaJbqvRbR0TmyVzuwGJS30zbdu2bTp16pTGjBkjLy8v+fv7a9CgQUpISFCePHk0cuRIubu7q1y5coqPj9fUqVMVHh6uggULSpKKFSsmScqbN69y5crluP3RRx9p6NChCgn553+YjxgxQqGhodq6davKly+v9evXa8eOHcqfP78kaeTIkWrWrJn69+/vOGa1xKREeVz2C0+S4/alixftSMoUE7tNbJbotlMFvxJKT5cOHT2h9xdsVlhNf00d1EJnzyVq1TdRduc5mLrWdFuLbuuY2CzRbTUTu01slui2Gt3WMbFZMrcbkBj8ZdqRI0fk5+cnLy8vx7HmzZtr6NChCgwMlLv7v0sZHBys+Ph4nTlz5oavef78ef3xxx964403lCPHvydfJiUl6ejRo3Jzc1NaWprq1nX+NM60tDTFxsYqKCjoLn13tyZ37ty6eMUvt4zbnp6ediRliondJjZLdNtp/upIrdm8T6fPXJAk7Y+Ok79vcXV+LixbDf5MXWu6rUW3dUxslui2mondJjZLdFuNbuuY2CyZ220St2ttXo67gsFfJl0+2Ltc7ty5rzqWcY1/amrqDV8z4/5JkybJz8/P6b6CBQtq165dyp8/v5YuXXrVc318fDLVnRWKF/dRQsJppaSkONbl5Ml4eXp6Kn+BArZ13YyJ3SY2S3TbLWPol+HAr3+oXu0KNtVcm6lrTbe16LaOic0S3VYzsdvEZoluq9FtHRObJXO7AYk9/jKtTJkyio2NVWJiouPYO++8o08//VQ//fSTY08+SdqzZ4+KFCmiQoUK3fA1CxQooKJFiyo+Pl6+vr7y9fVVyZIl9d577znOMDx79qzc3Nwc9yclJendd9+96r82WCmgYiW5u7srau+PjmN7ftitwKAqTmcuZjcmdpvYLNFtp8HdntIX07s7HasaUFqHjp6wqejaTF1ruq1Ft3VMbJbotpqJ3SY2S3RbjW7rmNgsmdsNSAz+Mi00NFTe3t4aMmSIYmJitGHDBi1YsEATJ07UxYsXHcfXr1+vKVOmqGXLlpk6VTU8PFwTJ07Uxo0bdfToUQ0aNEg//PCDypYtq3LlyiksLEy9e/dWVFSUfvrpJ/Xv318XLlxQARv/q0KePHnU9OlmGjVimPbvi9LGDes1d/YstWrTzramzDCx28RmiW47rdm8T2E1/NWjbUP5lfZW5+dC1bpJHU2cu8HuNCemrjXd1qLbOiY2S3RbzcRuE5sluq1Gt3VMbJbM7QYkyS09PT3d7ghTxMTEaMSIEdqzZ4+8vb3VuXNntWzZUj///LNGjx6tqKgoFSlSRC1atFCXLl2UI0cORUZGql27djp48KAkacqUKdqxY4c++eQTSf9c7jt58mQtXbpU586dU1BQkAYOHKhKlSpJkk6dOqVRo0Zp06ZNcnd3V1hYmAYNGqTChQtnujsp5e6vRWJiokaPGKb1X38lr/xeCm/fUW3ahd/9N7rLTOw2sVmi+0YK1+5+8wfdgsQ9EXq80yRt3R0tSWpSv4oGd3tK/g8UV2zcKQ2bulorN+694/c5vTPijl/jcvyMWItua5nYbWKzRLfVTOw2sVmi22p0W8fEZsmabs97eDO2cr3W2p1wV8WMe8LuBAcGf/eArBj8Abh9d3vwZ5W7PfgDAAAA8C8Gf64jOw3+uNQXAAAAAAAAcEEM/gAAAAAAAAAXdA+fSAoAAAAAAAC7ZeKzUXGbOOMPAAAAAAAAcEEM/gAAAAAAAAAXxOAPAAAAAAAAcEHs8QcAAAAAAADbuLHJX5bhjD8AAAAAAADABTH4AwAAAAAAAFwQgz8AAAAAAADABbHHHwAAAAAAAGzDFn9ZhzP+AAAAAAAAABfE4A8AAAAAAABwQQz+AAAAAAAAABfEHn8AAAAAAACwjRub/GUZzvgDAAAAAAAAXBCDPwAAAAAAAMAFMfgDAAAAAAAAXBB7/AGAxU7vjLA74bYUrt3d7oTbYup6AwAAAPcKtvjLOpzxBwAAAAAAALggBn8AAAAAAACAC2LwBwAAAAAAALgg9vgDAAAAAACAbXLkYJO/rMIZfwAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIALYo8/AAAAAAAA2MaNLf6yDGf8AQAAAAAAAC6IwR8AAAAAAADgghj8AQAAAAAAAC6IwR8AAAAAAADggvhwDwAAAAAAANjGjU/3yDKc8QcAAAAAAAC4IAZ/AAAAAAAAgAti8AcAAAAAAAC4IAZ/WSw9PV3z58933L548aIWLVpkY9HdkZycrKGDByj0P7XUsF6o5syeZXdSppjYbWKzRLfVTOv2yOWuXYsHKKymv+PY2D7Nlbgnwumr6wt1bay8NtPWOgPd1jKx28RmiW6rmdhtYrNEt9Xoto6JzZK53aZwc3Otr+yED/fIYjt37tSIESPUunVrSdIXX3yh6dOn6/nnn7e57M6MH/uuft6/Xx/OmqO4uDgNHtBXpUqW0mONGtuddkMmdpvYLNFtNZO6c3u4a86YcAWWL+V0vGLZkho8eaU+WbXdcezM+SSr827KpLW+HN3WMrHbxGaJbquZ2G1is0S31ei2jonNkrndAIO/LJaenn7D2ya6cOGCli9drKnTP1SlyoGqVDlQMYejteCz+dn6l56J3SY2S3RbzaTuimVLaPaY8Gv+V7CKfj6aMGe9Tvx11vqwTDJprS9Ht7VM7DaxWaLbaiZ2m9gs0W01uq1jYrNkbjcgcanvLYmNjVXHjh0VHBys+vXra+7cuZKkmJgYdezYUTVq1FBYWJgiIiKUlpam48ePq127dpKkgIAARUZGqn///vr9998VEBCg48ePKy0tTR999JEaNmyoqlWrqm3btjp48KDjPQMCArRy5Uo1adJEQUFBatWqlY4dO2bL95/h0MEDSklJUfXqwY5jwTVqal/UXqWlpdlYdmMmdpvYLNFtNZO6w2qW15adh1T/xXFOx/Pn89R9PoUVHfunTWWZY9JaX45ua5nYbWKzRLfVTOw2sVmi22p0W8fEZsncbkBi8JdpycnJ6tChg/Lly6dFixZpyJAhmjBhglauXKlWrVqpePHiWrx4sYYOHap58+Zp7ty5KlmypKZMmSJJ2rZtm4KDgzVgwACVKFFC27ZtU8mSJTV16lTNmjVLAwYM0PLly3XfffepU6dOunDhguO9p0yZooEDB2rZsmU6ffq0Jk6caNMq/ONkfLwKFSqsXB4ejmNFi3orOTlZCQkJ9oXdhIndJjZLdFvNpO4PF2/Tm+OWKTHpktPxin4+SktLU99OjXR43UhFLuyn1k1DbKq8PpPW+nJ0W8vEbhObJbqtZmK3ic0S3Vaj2zomNkvmdpvEzc3Npb6yEy71zaRt27bp1KlTGjNmjLy8vOTv769BgwYpISFBefLk0ciRI+Xu7q5y5copPj5eU6dOVXh4uAoWLChJKlasmCQpf/78ypkzp4oVK6b09HTNmzdPPXv2VMOGDSVJI0eO1GOPPaZVq1apRYsWkqT27dvrwQcflCS1bNnS6cNC7JCYlCiPy37hSXLcvnTxoh1JmWJit4nNEt1WM7X7chX8Sig9XTp09ITeX7BZYTX9NXVQC509l6hV30TZnedg6lrTbS0Tu01slui2mondJjZLdFuNbuuY2CyZ2w1IDP4y7ciRI/Lz85OXl5fjWPPmzTV06FAFBgbK3f3fpQwODlZ8fLzOnDlzw9f866+/lJCQoGrVqjmO5cqVS0FBQYqJiXEc8/X1dfzZy8tLly45nyljtdy5c+viFb/cMm57enrakZQpJnab2CzRbTVTuy83f3Wk1mzep9Nn/jnbeX90nPx9i6vzc2HZavBn6lrTbS0Tu01slui2mondJjZLdFuNbuuY2CyZ2w1IXOqbaZcP9i6XO3fuq45lXOOfmpp6w9e81nMznnf5PgG5cuXKbKYlihf3UULCaaWkpDiOnTwZL09PT+UvUMDGshszsdvEZoluq5nafaWMoV+GA7/+oVLFC9kTcx2mrjXd1jKx28RmiW6rmdhtYrNEt9Xoto6JzZK53YDE4C/TypQpo9jYWCUmJjqOvfPOO/r000/1008/OZ2Ft2fPHhUpUkSFChW66truy2/nz59f3t7e+vHHHx3HLl26pJ9++kl+fn5Z983coYCKleTu7q6ovT86ju35YbcCg6ooR47s+yNlYreJzRLdVjO1+3KDuz2lL6Z3dzpWNaC0Dh09YVPRtZm61nRby8RuE5sluq1mYreJzRLdVqPbOiY2S+Z2m8TuPflceY8/fkIzKTQ0VN7e3hoyZIhiYmK0YcMGLViwQBMnTtTFixcdx9evX68pU6aoZcuWcnNzU548eSRJ+/fvV3JysvLkyaO///5bR48eVUpKisLDwzV58mRt3LhRMTExGjx4sJKTk/Xkk0/a/B1fX548edT06WYaNWKY9u+L0sYN6zV39iy1atPO7rQbMrHbxGaJbquZ2n25NZv3KayGv3q0bSi/0t7q/FyoWjepo4lzN9id5sTUtabbWiZ2m9gs0W01E7tNbJbothrd1jGxWTK3G5Akt/T09HS7I0wRExOjESNGaM+ePfL29lbnzp3VsmVL/fzzzxo9erSioqJUpEgRtWjRQl26dFGOHDl08eJFde3aVTt27ND48eNVp04ddejQQdHR0fr0009VuXJlTZ48WUuWLNG5c+ccn/xboUIFSVJAQIDmzp2rkJB/Pt1y2bJlioiI0MaNGzPdnZRy88fcqsTERI0eMUzrv/5KXvm9FN6+o9q0C7/7b3SXmdhtYrNEt9Ws6C5cu/vNH3QLEvdE6PFOk7R1d7QkqUn9Khrc7Sn5P1BcsXGnNGzqaq3cuPeO3+f0zog7fo3L8TNiLbqtY2KzRLfVTOw2sVmi22p0W8fEZsmabs97+FMYqg3NXv/B/07tHd7Q7gQHBn/3gKwY/AG499ztwZ9V7vbgDwAAAMgKDP5cR3Ya/N3DP1YAAAAAAACwWzbbFs+lsMcfAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAC2cWOTvyzDGX8AAAAAAACAC2LwBwAAAAAAALggBn8AAAAAAACAC2KPPwAAAAAAANiGLf6yDmf8AQAAAAAAAC6IwR8AAAAAAADgghj8AQAAAAAAAC6IPf4AAAAAAABgGzc2+csynPEHAAAAAAAAuCAGfwAAAAAAAIALYvAHAAAAAAAAuCD2+AMAAAAAAIBt2OIv63DGHwAAAAAAAOCCOOMPAJApp3dG2J1wWwrX7m53wi0zda0BAAAAZC+c8QcAAAAAAAC4IM74AwAAAAAAgG3c2OQvy3DGHwAAAAAAAOCCGPwBAAAAAAAALojBHwAAAAAAAOCC2OMPAAAAAAAAtmGLv6zDGX8AAAAAAACAC2LwBwAAAAAAALggBn8AAAAAAACAC2KPPwAAAAAAANjGjU3+sgxn/AEAAAAAAAA2unjxopo0aaLIyEjHsWPHjik8PFzVq1fXk08+qW3btt3y6zL4AwAAAAAAAGySnJysnj17Kjo62nEsPT1dr7zyiry9vbV06VI9/fTT6t69u+Li4m7ptbnUFwAAAAAAALDB4cOH1atXL6Wnpzsd3759u44dO6YFCxYob968KleunL7//nstXbpUr776aqZfnzP+AAAAAAAAYBs3N9f6uhU7duxQSEiIFi5c6HR87969qly5svLmzes4VrNmTf3444+39Pqc8QcAAAAAAADYoFWrVtc8Hh8fr+LFizsdK1q0qP74449ben3O+AMAAAAAAACykcTERHl4eDgd8/Dw0MWLF2/pdRj84bYkJydr6OABCv1PLTWsF6o5s2fZnZQpJnab2CzRbTUTu01r9sjlrl2LByispr/j2Ng+zZW4J8Lpq+sLdW2svD7T1jsD3dYxsVmi22omdpvYLNFtNbqtY2KzZG43zJU7d+6rhnwXL16Up6fnLb0Ol/ritowf+65+3r9fH86ao7i4OA0e0FelSpbSY40a2512QyZ2m9gs0W01E7tNas7t4a45Y8IVWL6U0/GKZUtq8OSV+mTVdsexM+eTrM7LFJPW+3J0W8fEZoluq5nYbWKzRLfV6LaOic2Sud2mcLvVjfHuAT4+Pjp8+LDTsZMnT151+e/NMPjDLbtw4YKWL12sqdM/VKXKgapUOVAxh6O14LP52fqXnondJjZLdFvNxG6TmiuWLaHZY8KvuUlvRT8fTZizXif+Omt92C0wab0vR7d1TGyW6Laaid0mNkt0W41u65jYLJnbDbNVq1ZNH3zwgZKSkhxn+e3evVs1a9a8pdfhUt8bOHbsmMLDw1WtWjU1bdpUM2fOVIMGDbRs2TK1aNFCr7zyimrWrKlVq1bp3Llz6t+/vx588EEFBQWpcePGWr9+veO1AgICtHjxYj366KMKDg5Wr169dP78ecf9u3bt0rPPPquqVauqadOm+vLLLx33xcXFqUOHDgoODtaDDz6okSNH6tKlS5auxeUOHTyglJQUVa8e7DgWXKOm9kXtVVpamm1dN2Nit4nNEt1WM7HbpOawmuW1Zech1X9xnNPx/Pk8dZ9PYUXH/mlTWeaZtN6Xo9s6JjZLdFvNxG4TmyW6rUa3dUxslszthtnq1KmjkiVLqn///oqOjtYHH3ygqKgo/d///d8tvQ6Dv+tISUlRly5dVKBAAS1dulQvvfSSIiIiHPfv2bNH5cuX16JFixQaGqrRo0fryJEjmjVrlj7//HPVqlVLAwcOdLoee9KkSRo0aJDmzp2rQ4cOaciQIZL++aSWLl266Nlnn9Xq1avVqVMn9evXT7t27ZIkjRw5Unnz5tWKFSs0depUffnll1q0aJG1C3KZk/HxKlSosHJdtslk0aLeSk5OVkJCgm1dN2Nit4nNEt1WM7HbpOYPF2/Tm+OWKTHJ+T+4VPTzUVpamvp2aqTD60YqcmE/tW4aYlPljZm03pej2zomNkt0W83EbhObJbqtRrd1TGyWzO2G2XLmzKlp06YpPj5ezz77rFatWqWpU6eqVKlSN3/yZbjU9zq2b9+u//3vf1q0aJG8vLxUvnx5HTp0SF988YWkf64/79atm+N0y9q1a6t9+/aqUKGCJKlDhw5avHix/vrrL5UsWVKS1LlzZ9WvX1+SNHDgQHXo0EHDhg3T/Pnz9dBDD6lNmzaSJF9fX/3yyy+aM2eOatWqpd9//12BgYEqVaqUfH199cEHH6hAgQIWr8i/EpOu/ckyknTpFj9dxkomdpvYLNFtNRO7TWy+UgW/EkpPlw4dPaH3F2xWWE1/TR3UQmfPJWrVN1F25zkxdb3pto6JzRLdVjOx28RmiW6r0W0dE5slc7tNwhZ//zh48KDTbV9fX82bN++OXpPB33UcPHhQfn5+8vLychyrXr26Y/BXtGhRp09SadasmdavX69Fixbp119/1U8//SRJSk1NdTymRo0ajj8HBQUpNTVVR44c0a+//qpvvvlGwcH/njZ86dIl+fn5SZI6deqkAQMG6Ouvv1bdunX15JNPqnLlylnzjWfC9T5ZRtItf7qMlUzsNrFZottqJnab2Hyl+asjtWbzPp0+c0GStD86Tv6+xdX5ubBsN/gzdb3pto6JzRLdVjOx28RmiW6r0W0dE5slc7sBicHfdeXMmVPp6elOxy6/nTt3bqf73nzzTe3Zs0dPP/20WrZsqWLFiumFF15wekyuXLkcf87YByBHjhxKSUlR06ZN1bVrV6fHu7v/89fz3//+Vw8++KDWr1+vTZs26bXXXlPnzp31xhtv3Pk3ehuKF/dRQsJppaSkOBpPnoyXp6en8tt4JuLNmNhtYrNEt9VM7Dax+Voyhn4ZDvz6h+rVrmBTzfWZut50W8fEZoluq5nYbWKzRLfV6LaOic2Sud2AxB5/1+Xv76+jR4/q3LlzjmMZZ/Fd6dy5c/r88881YcIEvfbaa3rsscf0999/S3IeFv7yyy+OP+/fv1+5cuWSn5+f/Pz8FBsbK19fX8fXhg0btHr1aknShAkT9Ndff6lly5aaMWOGevTooa+++iorvu1MCahYSe7u7ora+6Pj2J4fdiswqIpy5Mi+P1ImdpvYLNFtNRO7TWy+0uBuT+mL6d2djlUNKK1DR0/YVHR9pq433dYxsVmi22omdpvYLNFtNbqtY2KzZG43IDH4u64HH3xQJUuW1ODBgxUTE6N169Zp7ty513ysh4eH8uTJo6+++krHjx/X1q1bNWLECElyOh148uTJ2rFjh/bu3atRo0bpmWeeUb58+dSqVSvt379fEyZM0NGjR7V69WqNHz/esWHjr7/+qhEjRujAgQOKjo7W5s2bbb3UN0+ePGr6dDONGjFM+/dFaeOG9Zo7e5ZatWlnW1NmmNhtYrNEt9VM7Dax+UprNu9TWA1/9WjbUH6lvdX5uVC1blJHE+dusDvtKqauN93WMbFZottqJnab2CzRbTW6rWNis2Rut0nc3Nxc6is7cUu/8npWOMTExGjw4MGKiopS2bJlFRISoi1btqhLly6KiIjQxo0bHY9dv3693nnnHf35558qXbq02rdvr4kTJ6pfv35q0qSJAgIC9PLLL2vlypU6c+aMnnrqKQ0cONCxIeh3332nsWPH6tChQ/Lx8VH79u0dH/bx119/afjw4fr++++VkpKi+vXra/DgwSpSpEimvo+klLu/NomJiRo9YpjWf/2VvPJ7Kbx9R7VpF3733+guM7HbxGaJbquZ2G1Vc+Ha3W/+oExK3BOhxztN0tbd0ZKkJvWraHC3p+T/QHHFxp3SsKmrtXLj3jt+n9M7I27+oFtk4s+IRLeVTGyW6Laaid0mNkt0W41u65jYLFnT7XkPb8b28Htb7U64q77tE2Z3ggODv+v466+/9PPPPyss7N+/rI8++kibN2/WJ598csuvFxAQoLlz5yokJORuZmZKVgz+AMAUd3PwZ5WsGPwBAAAge2Pw5zqy0+CPS31voFu3bvr000/1+++/67vvvtOcOXPUuHFju7MAAAAAAACAm7qH58k3VrRoUU2cOFGTJk3SW2+9JW9vb7Vp00atWrWyOw0AAAAAAMBlZLNt8VwKg78bePTRR/Xoo4/eldc6ePDgXXkdAAAAAAAAIDO41BcAAAAAAABwQQz+AAAAAAAAABfEpb4AAAAAAACwjRub/GUZzvgDAAAAAAAAXBCDPwAAAAAAAMAFMfgDAAAAAAAAXBCDPwAAAAAAAMAF8eEeAAAAAAAAsA0f7pF1OOMPAAAAAAAAcEEM/gAAAAAAAAAXxOAPAAAAAAAAcEHs8QcAAAAAAADbsMVf1uGMPwAAAAAAAMAFMfgDAAAAAAAAXBCDPwAAAAAAAMAFsccfAMClnd4ZYXfCLStcu7vdCbfFxLUGAACA/dzY5C/LcMYfAAAAAAAA4IIY/AEAAAAAAAAuiMEfAAAAAAAA4ILY4w8AAAAAAAC2YYu/rMMZfwAAAAAAAIALYvAHAAAAAAAAuCAGfwAAAAAAAIALYo8/AAAAAAAA2MaNTf6yDGf8AQAAAAAAAC6IwR8AAAAAAADgghj8AQAAAAAAAC6IPf4AAAAAAABgG7b4yzqc8QcAAAAAAAC4IAZ/AAAAAAAAgAti8AcAAAAAAAC4IPb4AwAAAAAAgG1ysMlfluGMPwAAAAAAAMAFMfizwPHjxxUQEKDjx4/f9LHff/+9YmJiLKi6M8nJyRo6eIBC/1NLDeuFas7sWXYnZYqJ3SY2S3RbzcRuE5sl87o9crlr1+IBCqvp7zg2tk9zJe6JcPrq+kJdGyuvz7T1zmBit4nNEt1WM7HbxGaJbqvRbR0TmyVzuwEu9bVAyZIltW3bNhUpUuSmjw0PD9fcuXNVrlw5C8pu3/ix7+rn/fv14aw5iouL0+ABfVWqZCk91qix3Wk3ZGK3ic0S3VYzsdvEZsms7twe7pozJlyB5Us5Ha9YtqQGT16pT1Ztdxw7cz7J6rxMMWm9L2dit4nNEt1WM7HbxGaJbqvRbR0TmyVzuwEGfxbImTOnihUrZnfGXXPhwgUtX7pYU6d/qEqVA1WpcqBiDkdrwWfzs/UvPRO7TWyW6Laaid0mNktmdVcsW0Kzx4TrWtulVPTz0YQ563Xir7PWh90Ck9b7ciZ2m9gs0W01E7tNbJbothrd1jGxWTK32yRs8Zd1uNTXApdf6hsQEKDIyEjHfcuWLVODBg0kyfF/27VrpylTpkiSdu3apWeffVZVq1ZV06ZN9eWXX1r/DVzh0MEDSklJUfXqwY5jwTVqal/UXqWlpdlYdmMmdpvYLNFtNRO7TWyWzOoOq1leW3YeUv0Xxzkdz5/PU/f5FFZ07J82lWWeSet9ORO7TWyW6Laaid0mNkt0W41u65jYLJnbDUgM/rKVJUuWSJKmTJmiDh06KD4+Xl26dNGzzz6r1atXq1OnTurXr5927dpla+fJ+HgVKlRYuTw8HMeKFvVWcnKyEhIS7Au7CRO7TWyW6Laaid0mNktmdX+4eJveHLdMiUmXnI5X9PNRWlqa+nZqpMPrRipyYT+1bhpiU+WNmbTelzOx28RmiW6rmdhtYrNEt9Xoto6JzZK53YDEpb7ZSsYegAULFlS+fPn04Ycf6qGHHlKbNm0kSb6+vvrll180Z84c1apVy7bOxKREeVz2C0+S4/alixftSMoUE7tNbJbotpqJ3SY2S+Z2X66CXwmlp0uHjp7Q+ws2K6ymv6YOaqGz5xK16psou/OcmLreJnab2CzRbTUTu01slui2Gt3WMbFZMrcbkBj8ZWu//vqrvvnmGwUH/3s68aVLl+Tn52djlZQ7d25dvOKXW8ZtT09PO5IyxcRuE5sluq1mYreJzZK53ZebvzpSazbv0+kzFyRJ+6Pj5O9bXJ2fC8t2gz9T19vEbhObJbqtZmK3ic0S3Vaj2zomNkvmdpvEjU3+sgyDP5ulpqZe976UlBQ1bdpUXbt2dTru7m7vX1vx4j5KSDitlJQUR8vJk/Hy9PRU/gIFbG27ERO7TWyW6Laaid0mNkvmdl8pY+iX4cCvf6he7Qo21VyfqettYreJzRLdVjOx28RmiW6r0W0dE5slc7sBiT3+LJcrVy6dP3/ecfvYsWPXfayfn59iY2Pl6+vr+NqwYYNWr15tRep1BVSsJHd3d0Xt/dFxbM8PuxUYVEU5cmTfHykTu01slui2mondJjZL5nZfbnC3p/TF9O5Ox6oGlNahoydsKro+U9fbxG4TmyW6rWZit4nNEt1Wo9s6JjZL5nYDEoM/y1WpUkXz5s3T0aNHtWHDBi1btszp/rx58yo6Olpnz55Vq1attH//fk2YMEFHjx7V6tWrNX78eJUqVcqm+n/kyZNHTZ9uplEjhmn/viht3LBec2fPUqs27WztuhkTu01slui2mondJjZL5nZfbs3mfQqr4a8ebRvKr7S3Oj8XqtZN6mji3A12p13F1PU2sdvEZoluq5nYbWKzRLfV6LaOic2Sud2AJLmlp6en2x3h6o4fP66GDRtqw4YNOnPmjAYNGqRDhw6pSpUqeuaZZzR9+nRt3LhRkjR+/HjNnj1bLVq00IABA/Tdd99p7NixOnTokHx8fNS+fXvHh31kVlLK3f+eEhMTNXrEMK3/+it55fdSePuOatMu/O6/0V1mYreJzRLdVjOx28RmyZruwrW73/xBtyBxT4Qe7zRJW3dHS5Ka1K+iwd2ekv8DxRUbd0rDpq7Wyo177/h9Tu+MuOPXuBI/J9YxsVmi22omdpvYLNFtNbqtY2KzZE235z28GdsT70fanXBXre0WYneCA4M/C8TGxurxxx/Xli1b5OPjY/n7Z8XgDwCQde724M8qWTH4AwAAuFcw+HMd2Wnwx6W+WezEiRPatm2bcuXKpcKFC9udAwAAAAAAgHvEPTxPtsYnn3yizz77TC+//LI8PDzszgEAAAAAAMA9gsFfFuvdu7d69+5tdwYAAAAAAEC25ObmZneCy+JSXwAAAAAAAMAFMfgDAAAAAAAAXBCDPwAAAAAAAMAFsccfAAAAAAAAbMMWf1mHM/4AAAAAAAAAF8TgDwAAAAAAAHBBDP4AAAAAAAAAF8QefwAAAAAAALCNm9jkL6twxh8AAAAAAADgghj8AQAAAAAAAC6IwR8AAAAAAADggtjjDwAAAAAAALbJwRZ/WYYz/gAAAAAAAAAXxOAPAAAAAAAAcEFc6gsAQDZzemeE3Qm3pXDt7nYn3BZT1xsAAAC4GQZ/AAAAAAAAsI2bG5v8ZRUu9QUAAAAAAABcEIM/AAAAAAAAwAUx+AMAAAAAAABcEHv8AQAAAAAAwDZs8Zd1OOMPAAAAAAAAcEEM/gAAAAAAAAAXxOAPAAAAAAAAcEHs8QcAAAAAAADb5GCTvyzDGX8AAAAAAACAC2LwBwAAAAAAALggBn8AAAAAAACAC2LwBwAAAAAAALggPtwDAAAAAAAAtuGzPbIOZ/wBAAAAAAAALojBHwAAAAAAAOCCGPwBAAAAAAAALijbDP6OHz+ugIAAHT9+3Ol4ZGSkAgICrvu8KVOmqG3btlmdpwYNGmjZsmVZ/j6mSE5O1tDBAxT6n1pqWC9Uc2bPsjspU0zsNrFZottqJnab2CzRbRWPXO7atXiAwmr6O46N7dNciXsinL66vlDXxsrrM229JTObJbqtZmK3ic0S3Vaj2zomNkvmdpvCzc3Npb6yk2z/4R7BwcHatm2b3Rm4wvix7+rn/fv14aw5iouL0+ABfVWqZCk91qix3Wk3ZGK3ic0S3VYzsdvEZoluK+T2cNecMeEKLF/K6XjFsiU1ePJKfbJqu+PYmfNJVudliknrncHEZoluq5nYbWKzRLfV6LaOic2Sud1Ath/8eXh4qFixYnZn4DIXLlzQ8qWLNXX6h6pUOVCVKgcq5nC0Fnw2P1v/0jOx28RmiW6rmdhtYrNEtxUqli2h2WPCr/nJbhX9fDRhznqd+Ous9WG3wKT1zmBis0S31UzsNrFZottqdFvHxGbJ3G5AykaX+l7pk08+Ua1atTR79mynS30PHz6sli1bqlq1amrXrp1Onz7tuG/ZsmVq27atJk+erJCQENWqVUtvvfWW0tPTHY9ZsGCBGjRooODgYLVt21YHDx6UJK1atUohISFKSUlxPPbLL79U/fr1Hc+Pjo5Ws2bNVKVKFXXs2FFxcXGOx/7vf/9T165dVa1aNTVo0EARERFKTU113L948WI1btxYQUFBCgkJ0fDhwx339+vXT2+99ZZ69OihatWqqV69elqxYoXjud9//72efvppValSRQ0bNtSCBQvu0irfnkMHDyglJUXVqwc7jgXXqKl9UXuVlpZmY9mNmdhtYrNEt9VM7DaxWaLbCmE1y2vLzkOq/+I4p+P583nqPp/Cio7906ayzDNpvTOY2CzRbTUTu01slui2Gt3WMbFZMrcbkLLp4G/dunUaP368pk+frkqVKjmOX7x4US+99JLuv/9+LVu2TI0aNdLChQudnrtnzx4dOXJEn332mQYPHqy5c+fqu+++kyRt3LhRERERGjx4sJYvX66aNWuqXbt2+vvvv9WwYUMlJSVp+/Z/Lx9au3atnnjiCcf12Z999pk6deqkpUuXKiUlRX379pUkpaenq3v37ipatKiWL1+ut956S6tXr9b06dMlSTt27NCoUaPUs2dPrVu3TsOHD9eSJUu0YcMGx3vNnz9fgYGB+vzzz/X4449r6NChOnv2rFJTU9WjRw81btxYa9eu1euvv67hw4fr8OHDWbP4mXAyPl6FChVWLg8Px7GiRb2VnJyshIQE27puxsRuE5sluq1mYreJzRLdVvhw8Ta9OW6ZEpMuOR2v6OejtLQ09e3USIfXjVTkwn5q3TTEpsobM2m9M5jYLNFtNRO7TWyW6LYa3dYxsVkyt9skbm6u9ZWdZLvB365du9S/f39NmDBBtWrVcrrvu+++U0JCgoYNG6Zy5cqpdevWevTRR50ek5qaqpEjR6ps2bJ6+umnVbFiRe3bt0+S9NFHH6lLly565JFHVKZMGfXo0UP33XefVq1apXz58umRRx7RunXrJEmJiYnavHmznnrqKcdrt2zZUk2aNFGFChU0evRo7dixQzExMdq+fbvi4uIc7xsSEqK+fftq7ty5kqS8efNq9OjRevzxx1W6dGk1btxYlStXVnR0tOO1AwIC1LlzZ91///16/fXXlZSUpOjoaJ09e1YJCQny9vZW6dKl9d///lcff/yxrZc/JyYlyuOyX3iSHLcvXbxoR1KmmNhtYrNEt9VM7DaxWaLbThX8Sig9XTp09ISavfq+Zi//XlMHtdB/H6lqd9pVTFxvE5sluq1mYreJzRLdVqPbOiY2S+Z2A1I23ONvyJAhSk1NVcmSJa+67/DhwypTpozy5s3rOFalShVt3rzZcbto0aLy8vJy3Pby8nJcvhsTE6P33ntP48ePd9yfnJyso0ePSpKaNGmiQYMGadiwYdq0aZOKFy+uoKAgx2OrVv33f1yULl1ahQoV0q+//qoTJ04oISFBNWvWdNyflpampKQknT59WkFBQfL09NTkyZN1+PBhHTx4ULGxsQoNDXU8vkyZMk7NkpSSkqJChQqpZcuWGjRokKZNm6ZHHnlEzZs3V8GCBTO9pndb7ty5dfGKX24Ztz09Pe1IyhQTu01slui2mondJjZLdNtp/upIrdm8T6fPXJAk7Y+Ok79vcXV+Lkyrvomyuc6ZiettYrNEt9VM7DaxWaLbanRbx8RmydxuQMqGg7+ePXvqhx9+0IgRIzR//vyr7r98vz5JypUrl9PtK6fwlz8nNTVVAwYM0IMPPuh0f8agrW7dukpNTdXOnTv15Zdf6oknnnB6XM6cOZ1up6WlKVeuXEpJSVHZsmU1bdq0q947f/782rp1q1555RU1a9ZMYWFheuWVVzR8+PAbfh+Xdw8bNkytW7fW+vXrtX79ei1cuFDTpk1TvXr1rnqOFYoX91FCwmmlpKTI3f2fH6GTJ+Pl6emp/AUK2NKUGSZ2m9gs0W01E7tNbJbotlvG0C/DgV//UL3aFWyquT4T19vEZoluq5nYbWKzRLfV6LaOic2Sud2AlA0v9X300UfVt29f7d+/3+kDLiTJ399fR48e1dmz/36a3y+//JLp1/bz89Mff/whX19fx9f06dP1448/SvpnaPjYY4/p66+/1rfffut0ma8kHTp0yPHno0eP6syZM/Lz85Ofn5/i4uJUpEgRx+seP35ckydPlpubmxYvXqzmzZtrxIgReu6551SuXDn99ttvVw0xryU+Pl7Dhw+Xr6+vunXrpqVLl+o///mPNm7cmOnv+24LqFhJ7u7uitr7o+PYnh92KzCoinLkyHY/Ug4mdpvYLNFtNRO7TWyW6LbT4G5P6Yvp3Z2OVQ0orUNHT9hUdH0mrreJzRLdVjOx28RmiW6r0W0dE5slc7tNksPNzaW+spNs+RN63333qVOnTnrvvfechnwPPfSQSpYsqYEDByomJkbLli3TmjVrMv267du315w5c7RixQr99ttveu+997R27VqVK1fO8ZgmTZpoyZIlKlGihPz9/Z2e//HHH+urr77SgQMH1L9/fz3yyCPy9fVVaGio7rvvPvXp00cHDx7Url27NHjwYOXJk0c5c+ZUoUKFtGfPHh08eFDR0dHq16+f4uPjrzpV+FoKFiyor7/+WmPGjNFvv/2mnTt36sCBA6pcuXKmv++7LU+ePGr6dDONGjFM+/dFaeOG9Zo7e5ZatWlnW1NmmNhtYrNEt9VM7DaxWaLbTms271NYDX/1aNtQfqW91fm5ULVuUkcT5264+ZMtZuJ6m9gs0W01E7tNbJbothrd1jGxWTK3G5Akt/TMnHZmgePHj6thw4basGGDSpcuraSkJD3xxBPy8vLSoUOH/h979x7fc/3/f/z+ZrOtNtEccoopNuczKSuNRIhEIUQHIjooZ8thDoVQm/JxnFOI5pCQU9JBTjlNjnMcxYgis5nt94ef93dzHN57vfZ8u10/l10ue7/e773fN8/22erh9Xq+tXv3bknSkSNH1LdvX23evFmBgYGqXLmyoqOjNW3aNEVFRSkiIiLN2XCtW7dW1apV1aVLF0nS1KlTFRkZqZMnT+rRRx9Vt27d0lz6e+nSJdWoUUNt2rRRx44dncdDQkLUpEkTLV68WLGxsXryySc1cOBAPfjgg86usLAwrVu3Tvfdd5/q1q2rHj16yNvbWydOnFCvXr20adMm+fr66qmnnpKnp6eOHDmiiRMnqmfPnpKkjz/+2Pl6gYGBmjp1qqpVq6Zt27ZpyJAh2rVrl+6//341bdpU7777brr/ZuFC0h3+Q7mJ+Ph4DR7YXyuWL5Ovn6/atntdrdq0df0LuZiJ3SY2S3RbzcRuE5slum8mZ5XOt37QbYjfHKE6b3ymnzZdfjOsBjXLKLRjfRV7OI8OHftb/cd8qwWrtt7165zeEHHXz3E1E79PTGyW6Laaid0mNkt0W41u65jYLFnT7Z3pNmOzzstTNtud4FKzX61gd4JTphn8ZRbnzp3TE088oUWLFqlQoUJ257hERgz+AAC4mqsHf1bJiMEfAADA7WLw5z4y0+DvHv62SislJUXff/+9li1bpgoVKrjN0A8AAAAAACAzy1y74rkXBn//n8Ph0PDhw5U1a1Z9+eWXducAAAAAAAAAd4XBXyorV2a+DcIBAAAAAACAO5Ep39UXAAAAAAAAwN3hjD8AAAAAAADYxuFgl7+Mwhl/AAAAAAAAgBti8AcAAAAAAAC4IQZ/AAAAAAAAgBtijz8AAAAAAADYJgtb/GUYzvgDAAAAAAAA3BCDPwAAAAAAAMANMfgDAAAAAAAA3BB7/AEAAAAAAMA2Dgeb/GUUzvgDAAAAAAAA3BCDPwAAAAAAAMANMfgDAAAAAAAA3BB7/AEAAAAAAMA2bPGXcRj8AS52Nj7J7oTb5ufDjwIAd+/0hgi7E+6If/PJdifckVOz2tmdcM8w8Xe7xO93AADApb4AAAAAAACAW2LwBwAAAAAAALghzv8HAAAAAACAbRxs8pdhOOMPAAAAAAAAcEMM/gAAAAAAAAA3xOAPAAAAAAAAcEPs8QcAAAAAAADbZGGLvwzDGX8AAAAAAACAG2LwBwAAAAAAANjgzz//VIcOHVSxYkWFhIQoMjLSpc/Ppb4AAAAAAACADd577z3lz59fUVFR2rdvnz788EMVKFBAzzzzjEuenzP+AAAAAAAAYBuHw+FWH+n1zz//aMuWLerYsaOKFCmi2rVrKzg4WGvXrnXZ2jL4AwAAAAAAACzm7e0tHx8fRUVF6eLFi9q/f79+//13lShRwmWvweAPAAAAAAAAsJiXl5c++ugjzZ49W+XKlVO9evX05JNPqlmzZi57jXTt8bdhw4Z0P2GVKlXuOAYAAAAAAAC4V8TExOjpp59Wu3bttHfvXoWFhal69ep6/vnnXfL86Rr8tW7dWg6HQykpKTd9nMPh0M6dO10SBgAAAAAAAPeX/l3x3MvatWs1d+5c/fjjj/L29laZMmV0/Phxffnll9YO/lauXOmSFwMAAAAAAAAgRUdHq3DhwvL29nYeK1mypMaOHeuy10jX4K9AgQLXHEtMTFRsbKwefvhhpaSkyNPT02VRAAAAAAAAgDvLkyePDh06pMTERGXLlk2StH//fhUsWNBlr3Hbb+6RkpKiESNGqEqVKmrQoIH+/PNP9ejRQ3369NHFixddFmaq2NhYBQYGKjY29q6eJyUlRTNmzHDe7tmzp3r27ClJCg8PV+vWre/q+e9WQkKC+oX2Vo3HKqvWUzU0JXKSrT3pZWq3dHnY3vqlRvp943q7U9LF1LWm2zomNkt0W8207mweWbRhZGMFl3rIeax2ufz6bUQjnZzRWr+NaKQ6Fa79C9XMwLS1vsLU7iv4/Z7xTGyW6LYa3dYxsVkytxuZW0hIiDw9PdW3b18dOHBAq1at0tixY10680nXGX+pTZs2TQsWLFC/fv00cOBASVLt2rU1YMAA5cqVS++//77L4u5lGzZs0MCBA/XKK69Ikvr06WNzUVojRwzTH9HRGj9pio4dO6bQ3j2UP19+PfNsXbvTbsrU7oSEBA3o210H9u+zOyXdTF1ruq1jYrNEt9VM6vbyzKrJ7z2lkg/ndB4r+pCfZnavpQEzN2nR+sNqWLWwZnWvpfLvROlw3Dkba69l0lqnZmq3xO93q5jYLNFtNbqtY2KzZG63KbI47s1d/vz8/BQZGanBgweradOmevDBB9WxY0e9/PLLLnuN2x78zZ49Wx999JGeeeYZhYWFSZKee+45eXp6aujQoQz+XOTqN1Lx8/OzqeRa58+f17xv5mjM2PEqUbKUSpQspZh9ezVr5oxM/UPP1O4D+/dpQN/ut3xznczE1LWm2zomNkt0W82k7qCCD2jyu0/JcdW/tBbwv1+Tl+9WxKI/JEnhi3ao+4vlVPnRXJlq8GfSWqdmarfE73ermNgs0W01uq1jYrNkbjfM8Oijj2ry5MkZ9vy3falvbGysSpQocc3xoKAgxcXFuSTKXfz777/q1q2bKlasqBo1aigsLEwXLlyQJK1bt04hISH66quvFBwcrPLly6tbt27OvRPbtGkjSQoMDNS6devSXOprtz27dykpKUnly1dwHqtQsZK2b9uq5ORkG8tuztTuLb9vVMVKVfW/yV/ZnZJupq413dYxsVmi22omddco+ZDW7PhLT/delOb4Tzv+UvfIy5dwemR1qE1IMXl5ZtHGfSftyLwhk9Y6NVO7JX6/W8XEZoluq9FtHRObJXO7AekOBn8FChTQ9u3brzm+Zs0aFSpUyCVR7qJPnz46e/asZs6cqS+++ELbt293Xh4tSSdOnND333+vCRMmKDw8XMuWLdP8+fOVL18+hYeHS5J+/vlnVahQ4UYvYYuTcXHKkSOnPP//xpOS5O+fSwkJCTpz5ox9YbdgavcLTZvrnQ96ytvbx+6UdDN1rem2jonNEt1WM6l7wrLd6hG5XvGJl657f9GH/HTqqzb6slMNfTx3a6Y6208ya61TM7Vb4ve7VUxslui2Gt3WMbFZMrcbkO7gUt/XX39dAwYMUFxcnFJSUrR27VrNnj1b06ZNyzRnpGUGycnJWrFihdavX++8TDcsLEyNGzdWr169JEkXL15U3759VaxYMQUGBio4OFjbt2/XSy+9pAceeECSlDt3btv+DDcSfyHe+W4zV1y5fTEx0Y6kdDG120SmrjXd1jGxWaLbaqZ2X8/Jfy/oyZ7fqmrxPPr41SqK+fNfLVh3yO4sJ1PX2tRuU5m43iY2S3RbjW7rmNgsmdttknt0iz9L3Pbg78UXX1RSUpK+/PJLXbhwQR999JEefPBBvffee2rRokVGNBppzZo1Sk5O1pNPPpnmeHJysg4d+r9/0S9cuLDzc19fXyUlJVnWeKe8vLyUeNUPtyu3vb297UhKF1O7TWTqWtNtHRObJbqtZmr39fx7/qK2HvhbWw/8raCCOfTWcyUy1eDP1LU2tdtUJq63ic0S3Vaj2zomNkvmdgPSHQz+JOnll1/Wyy+/rL///lspKSny9/d3dZfxkpKS5Ofnp2+++eaa+/LmzautW7dK0jV/a2DCBs958uTVmTOnlZSUJA+Py99CJ0/GydvbW37Zs9tcd2OmdpvI1LWm2zomNkt0W83U7tRKFMyhnH5e+nXnceexXbFn9GSph2ysupapa21qt6lMXG8TmyW6rUa3dUxslsztBqQ72ONPkuLi4vTZZ5/po48+0uDBgzV27Fj9+++/rm4zWnBwsM6ePSuHw6HChQurcOHCunDhgoYNG3bN3xRcz9XvCpiZBAaVkIeHh7Zt3eI8tvn3TSpVuoyyZLmjbylLmNptIlPXmm7rmNgs0W01U7tTe65yIUW89XiaYxWK+mvX0TP2BN2AqWttarepTFxvE5sluq1Gt3VMbJbM7QakOxj8rVu3Ts8884wWLFggh8OhCxcu6KuvvlKdOnW0a9eujGg0kpeXl4KDg/Xhhx9q27Zt2rFjh3r16qXz588rezr+RsDH5/JGz9HR0UpISMjo3Nvi4+Ojho0aa9DA/orevk2rVq7Q1MhJatmqjd1pN2Vqt4lMXWu6rWNis0S31UztTm3mmhg9lOM+hbWqrEceyq72dYPU/MlH9GnUtW+UZidT19rUblOZuN4mNkt0W41u65jYLJnbbRKHw+FWH5mJI+U2ry198cUXVbJkSfXv319Zs2aVdPna9p49e+rEiROaPn16hoSaIjY2VrVq1dLKlSt13333adCgQVq9erU8PDwUHBysvn37KmfOnFq3bp3atGmj3bt3O7/2ypujfPzxx0pMTNRbb72l9evXa+TIkVq1apXzvvDwcK1fv17Tpk1LV9OFDNg2MD4+XoMH9teK5cvk6+ertu1eV6s2bV3/Qi5mRffZ+Izbp7FG5VL6fOxkVaxc1aXP6+dzR1f93xTfI9YysdvEZoluq1nR7d98skuf77+57VS33xL9tOMvSVKVYrk1rF1VlS78oA6dOKePZmzU4o1H7vp1Ts1qd9fPkRrfIzeWkb/bJX6/ZzQTmyW6rUa3dUxslqzp9nb9j21jtJ+zw+4ElxrXrJTdCU63PfgrU6aMFi5cqICAgDTH9+3bpyZNmmjbtm0uDcTdy4jBH24so//jICNkxH8YAIApXD34s4qrB3+4MRN/t0v8fgcA0zD4cx+ZafB325f6BgQEaM+ePdccP3TokAoUKOCSKAAAAAAAAAB3J13z5A0bNjg/r1+/vj766COdPHlSFStWVJYsWbRjxw59+umn6tKlS4aFAgAAAAAAwP1ksm3x3Eq6LvUNCgqSw+HQrR7qcDi0c+dOl8XBNbjU11omXg7EpUAA7mVc6otbMfF3u8TvdwAwzb18qW+Hue51qe//mmaeS33T9W21cuXKjO4AAAAAAAAA4ELpGvyld+++hISEu4oBAAAAAAAA4Bq3fSLp6dOnNXbsWO3Zs0eXLl2SJKWkpOjixYvat2+fNm7c6PJIAAAAAAAAuKcsbPKXYW77XX0HDBig+fPnK2fOnNq4caPy5s2r//77T1u2bFH79u0zohEAAAAAAADAbbrtM/7Wrl2rTz75RDVr1tTu3bv1+uuvKygoSKGhodq3b19GNAIAAAAAAAC4Tbd9xt9///2nwMBASVLRokW1a9cuSVKrVq20bt0619YBAAAAAAAAuCO3PfjLmzevjh49KkkqUqSIdu/eLUny8fHRP//849o6AAAAAAAAAHfktgd/derUUa9evbRp0yY9/vjjmjdvnpYuXarPP/9chQsXzohGAAAAAAAAuCmHw70+MpPb3uPv/fffV1JSko4dO6aGDRuqTp06eu+99+Tn56fPPvssIxoBAAAAAAAA3CZHSkpKyt0+yZkzZ+Tr6ysPj9ueI8ICF5LsLri3nI03b8H9fPj/LoB7l3/zyXYn3JFTs9rZnXDPMPF3u8TvdwAwjfc9/GO7U9Qfdie41BdNStqd4HTbl/peT44cObR582bVqlXLFU8HAAAAAAAA4C65bJ584cIFHTt2zFVPBwAAAAAAgHuAI7NtjOdGXHLGHwAAAAAAAIDMhcEfAAAAAAAA4IYY/AEAAAAAAABuKF17/EVERNzyMYcOHbrrGMAd8A56AGAWU98dN2eVznYn3LbTG27975SZEb/bAQDIWJyVlnHS9W8xUVFR6XqyfPny3VUMAAAAAAAAANdI1+Bv1apVGd0BAAAAAAAAwIU4mxIAAAAAAABwQ2xYAgAAAAAAANs4HA67E9wWZ/wBAAAAAAAAbojBHwAAAAAAAOCG7mrwl5iY6KoOAAAAAAAAAC50R3v8zZw5U+PHj9dff/2l77//XhMmTFDevHnVqVMnV/cBAAAAAADAjWVhi78Mc9tn/H377bf69NNP9cILL8jT01OS9Mgjj2js2LGaNGmSywMBAAAAAAAA3L7bHvxNmjRJffr0UZcuXZQly+Uvb9OmjT766CPNnj3b5YEAAAAAAAAAbt9tD/4OHDigypUrX3O8WrVq+vPPP10SBQAAAAAAAODu3PYef7ly5dKBAwdUqFChNMc3b96sPHnyuCwMAAAAAAAA7o89/jLObZ/x9/LLL2vgwIFauXKlJGn//v2aOXOmBg8erCZNmrg8EAAAAAAAAMDtu+0z/t58802dPXtWXbt2VUJCgjp06CAPDw81b95cb731VkY0AgAAAAAAALhNtz34k6SuXbuqY8eO2rdvn1JSUlS0aFH5+vq6ug0AAAAAAADAHbrtwd+xY8ecn/v7+0uS/v33X/3777+SpPz587soDQAAAAAAAO7O4WCTv4xy23v8hYSEqFatWjf8QPqEhIQoKirK7ow7lpCQoH6hvVXjscqq9VQNTYmcZHdSupjYbWKzRLfVTOw2sVmi22p0Z7xsnh7aOKe3gisVcx4b0e1FxW+OSPPx1stP2lh5YyatdWp0W8fEZoluq9FtHRObJXO7gds+42/q1Klpbl+6dEkHDhxQZGSkevbs6bIwdzd37lzdd999dmfcsZEjhumP6GiNnzRFx44dU2jvHsqfL7+eebau3Wk3ZWK3ic0S3VYzsdvEZoluq9GdsbyyeWjKkLYq9WjaKzaCiuZT6OcLNG3hb85j//53weq8dDFlra9Gt3VMbJbothrd1jGxWTK3G7jtwV/VqlWvOVa9enUVKlRI4eHhCgkJcUmYu3vwwQftTrhj58+f17xv5mjM2PEqUbKUSpQspZh9ezVr5oxM/UPPxG4TmyW6rWZit4nNEt1WoztjBRV9SJFD2up6V9YEBeTVqCkrdPzUWevDboMpa301uq1jYrNEt9Xoto6JzZK53YB0B5f63kiRIkW0a9cuVz1dphQbG6vAwECtXr1aISEhqlChggYNGqQ9e/aoSZMmKl++vDp06KBz584pMTFRQ4cOVXBwsEqVKqWQkBDNnj3b+VxXLvW98pxXf7Ru3VqSlJiYqEGDBqlatWqqVq2aPvzwQ505c8amFbhsz+5dSkpKUvnyFZzHKlSspO3btio5OdnGspszsdvEZoluq5nYbWKzRLfV6M5YwZUe1ZoNe1Tz1U/THPe731sF8ubU3kMnbCpLP1PW+mp0W8fEZoluq9FtHRObJXO7TZLF4V4fmcldvbnHFefOndP//vc/FSxY0CVRmd24ceP0xRdfaN++ffrggw+0Zs0a9evXT97e3urUqZPmzp2rc+fOafXq1QoPD5e/v7/mzZunsLAw1apVS7ly5XI+V758+fTzzz87b+/du1ft27dXu3btJEkjR45UdHS0xo8fLy8vL40aNUrvvvuupkyZYvmf+4qTcXHKkSOnPLNlcx7z98+lhIQEnTlzJtOezWhit4nNEt1WM7HbxGaJbqvRnbHGz/n5useDAvIqOTlZPd54Vs8+UVKn/vlPn0//QTO+XWdx4a2ZstZXo9s6JjZLdFuNbuuY2CyZ2w1IdzD4CwkJuebdVlJSUnTfffdp+PDhLgvLzDp16qSgoCAFBQVpyJAhql+/vp544glJly973r9/v5588kk99thjKl++vCTprbfe0pgxY3Tw4ME0g7+sWbMqd+7cki4PUAcMGKA2bdooJCRE8fHxmj59ur755hsFBgZKkoYNG6Zq1app9+7dzmNWi78Qr2ypfuBJct6+mJhoR1K6mNhtYrNEt9VM7DaxWaLbanTbo3jAQ0pJkfYcPK4vZ/2o4ErFNKZvc509F6+FP2yzOy8NU9eabuuY2CzRbTW6rWNis2RuNyC54M09JMnT01PFixfX/fff75KozK5QoULOz729vVWgQIE0txMTE1W7dm398ssv+vjjj7V//3798ccfki6/GcqN9OrVS/7+/uratask6ciRI7p48aKaN2+e5nHJyck6ePCgbYM/Ly8vJV71w+3KbW9vbzuS0sXEbhObJbqtZmK3ic0S3Vaj2x4zvl2nxT9u1+l/z0uSovceU7HCefRms+BMN/gzda3pto6JzRLdVqPbOiY2S+Z2A9IdDv7ef/99PfLIIxnRY4SsWbOmuZ0ly7VbJY4aNUpz5sxRkyZN1LhxY/Xr1++mb3wyYcIEbdy4UfPnz5eHx+V/LFeGhF999dU17wDs7+9/t3+MO5YnT16dOXNaSUlJztaTJ+Pk7e0tv+zZbeu6FRO7TWyW6Laaid0mNkt0W41u+1wZ+l2xa/9feqpKcZtqbszUtabbOiY2S3RbjW7rmNgsmdttkuu92Rhc47bf3OO3336Tl5dXRrS4lVmzZik0NFQffvihnnvuOcXHx0u6fFn01datW6fRo0drxIgRyps3r/N4oUKFlDVrVp05c0aFCxdW4cKF5evrq6FDh+rUqVOW/VmuFhhUQh4eHtq2dYvz2ObfN6lU6TLXHYJmFiZ2m9gs0W01E7tNbJbothrd9gjtWF/fje2c5ljZwILac/C4TUU3Zupa020dE5sluq1Gt3VMbJbM7QakOxj8vfDCCxoxYoT27t17zamu+D85cuTQDz/8oCNHjmjjxo3q3r27JF2zZsePH1fXrl3Vrl07lShRQnFxcc4PX19fNWvWTP3799e6deu0b98+de/eXYcOHbL1jVR8fHzUsFFjDRrYX9Hbt2nVyhWaGjlJLVu1sa0pPUzsNrFZottqJnab2CzRbTW67bH4x+0KrlhM77WupYCCufRmsxp6pUFVjZ660u60a5i61nRbx8RmiW6r0W0dE5slc7sBSXKkXO8UtJuoU6eODh8+fM0bfFyxc+dOl4RlRrGxsapVq5ZWrlzpHLyFhISoc+fOatKkiSSpZ8+ekuQc2B06dEh58+ZVs2bNtHz5ctWuXVsdOnRwfp10eW+/69m9e7fi4+P1ySefaMmSJbp48aKqVKmivn37ptln8FYuJN3Nn/r64uPjNXhgf61Yvky+fr5q2+51tWrT1vUv5GImdpvYLNFtNRO7TWyW6LYa3TeWs0rnWz8oneI3R6jOG5/pp017JUkNapZRaMf6KvZwHh069rf6j/lWC1ZtvevXOb0h4q6f42p8j1jLxG4TmyW6rUa3dUxslqzp9r7tzdjcR/fvdtud4FLD6tvzngzXc9uDv3nz5t30/hdeeOGuguB6GTH4AwAA9nLl4M8qGTH4AwDAXdzLg7+ei/fYneBSHz+XefZGTte3VYkSJfTzzz/L39+fwR4AAAAAAABggHTt8XebJwUCAAAAAAAAsBlvPwMAAAAAAAC4oXRfQb5kyRL5+vre8nGNGze+mx4AAAAAAADcQzgrLeOke/A3aNCgWz7G4XAw+AMAAAAAAAAygXQP/n755Rf5+/tnZAsAAAAAAAAAF0nX2ZQOhyOjOwAAAAAAAAC4ULrO+ONdfQEAAAAAAJARON8s46TrjL8XXnhBXl5eGd0CAAAAAAAAwEXSdcbf0KFDM7oDAAAAAAAAgAvxjskAAAAAAACAG0r3u/oCAAAAAAAArpaFTf4yDGf8AQAAAAAAAG6IwR8AAAAAAADghhj8AQAAAAAAAG6IPf4AAAAAAABgG7b4yzic8QcAAAAAAAC4IQZ/AAAAAAAAgBviUl8AAAADnd4QYXfCbctZpbPdCXfExLUGAACQGPwBAAAAAADARlnY4y/DcKkvAAAAAAAA4IYY/AEAAAAAAABuiMEfAAAAAAAA4IbY4w8AAAAAAAC2yeJgk7+Mwhl/AAAAAAAAgBti8AcAAAAAAAC4IQZ/AAAAAAAAgBtijz8AAAAAAADYhi3+Mg5n/AEAAAAAAABuiMEfAAAAAAAA4IYY/AEAAAAAAABuiD3+AAAAAAAAYJss7PGXYTjjDwAAAAAAAHBDDP4AAAAAAAAAN8TgDwAAAAAAAHBDDP5wRxISEtQvtLdqPFZZtZ6qoSmRk+xOShcTu01slui2mondJjZLdFuNbuuY1pzN00Mb5/RWcKVizmMjur2o+M0RaT7eevlJGytvzLT1vsLEbhObJbqtRrd1TGyWzO0GeHMP3JGRI4bpj+hojZ80RceOHVNo7x7Kny+/nnm2rt1pN2Vit4nNEt1WM7HbxGaJbqvRbR2Tmr2yeWjKkLYq9Wj+NMeDiuZT6OcLNG3hb85j//53weq8dDFpvVMzsdvEZoluq9FtHRObJXO7TeEQ7+6RURj84badP39e876ZozFjx6tEyVIqUbKUYvbt1ayZMzL1Dz0Tu01slui2mondJjZLdFuNbuuY1BxU9CFFDmkrx3X++yAoIK9GTVmh46fOWh92G0xa79RM7DaxWaLbanRbx8RmydxuQOJS3xuKjY1VYGCgVq9erZCQEFWoUEGDBg3Snj171KRJE5UvX14dOnTQuXPnlJiYqKFDhyo4OFilSpVSSEiIZs+e7XyukJAQzZgxQy+99JLKlCmjRo0aKTo62nn/kSNH1LZtW5UrV04NGzbUxIkTFRIS4rx/8+bNatGihcqXL6+QkBDNnDnT0rW42p7du5SUlKTy5Ss4j1WoWEnbt21VcnKyjWU3Z2K3ic0S3VYzsdvEZoluq9FtHZOagys9qjUb9qjmq5+mOe53v7cK5M2pvYdO2FSWfiatd2omdpvYLNFtNbqtY2KzZG43IDH4u6Vx48bpiy++UFhYmKZNm6bOnTvrgw8+0MSJE7VlyxbNnTtX48aN0+rVqxUeHq6lS5eqcePGCgsL08mTJ53PEx4ervbt22vhwoXy8/PToEGDJElJSUnq0KGDsmfPrm+++Ubt27dXRESE8+tiYmL06quvqkqVKoqKilKXLl30ySefaPny5ZavxRUn4+KUI0dOeWbL5jzm759LCQkJOnPmjG1dt2Jit4nNEt1WM7HbxGaJbqvRbR2TmsfP+VndP41S/IWLaY4HBeRVcnKyerzxrPYtDdO62T31SsNqNlXenEnrnZqJ3SY2S3RbjW7rmNgsmdsNSFzqe0udOnVSUFCQgoKCNGTIENWvX19PPPGEJKl69erav3+/nnzyST322GMqX768JOmtt97SmDFjdPDgQeXKlUuS9MILL6h27dqSpHbt2undd9+VJP3222/6888/9fXXX8vX11ePPvqo9uzZo++++06S9PXXX6tkyZLq2rWrJKlo0aKKiYnRhAkT9Mwzz1i5FE7xF+KVLdUPPEnO2xcTE+1IShcTu01slui2mondJjZLdFuNbuuY2Hy14gEPKSVF2nPwuL6c9aOCKxXTmL7NdfZcvBb+sM3uvDRMXW8Tu01slui2Gt3WMbFZMrfbJFnY4i/DMPi7hUKFCjk/9/b2VoECBdLcTkxMVO3atfXLL7/o448/1v79+/XHH39Iki5duuR8bJEiRZyf+/r66uLFy39LvXv3bgUEBMjX19d5f/ny5Z2Dv5iYGJUtWzZNU4UKFTRr1izX/SFvk5eXlxKv+uF25ba3t7cdSeliYreJzRLdVjOx28RmiW6r0W0dE5uvNuPbdVr843ad/ve8JCl67zEVK5xHbzYLznSDP1PX28RuE5sluq1Gt3VMbJbM7QYkLvW9paxZs6a5nSXLtUs2atQodevWTR4eHmrcuHGa/f2u8PT0vOHzp6SkpDmW+raXl9c1X5OcnJxmqGi1PHny6syZ00pKSnIeO3kyTt7e3vLLnt22rlsxsdvEZoluq5nYbWKzRLfV6LaOic3Xc2Xod8Wu/X8pf54c9sTchKnrbWK3ic0S3Vaj2zomNkvmdgMSgz+XmDVrlkJDQ/Xhhx/queeeU3x8vCRdM9C7nmLFiungwYM6d+6c89iOHTucnwcEBGjr1q1pvmbz5s0KCAhwUf3tCwwqIQ8PD23buuX/mn7fpFKly1x3MJpZmNhtYrNEt9VM7DaxWaLbanRbx8Tmq4V2rK/vxnZOc6xsYEHtOXjcpqIbM3W9Tew2sVmi22p0W8fEZsncbkBi8OcSOXLk0A8//KAjR45o48aN6t69uyRdcyrw9VSvXl358uVTaGioYmJitHTpUk2dOtV5f8uWLbVz506NHDlSBw4c0Lx58/TVV1/plVdeybA/z634+PioYaPGGjSwv6K3b9OqlSs0NXKSWrZqY1tTepjYbWKzRLfVTOw2sVmi22p0W8fE5qst/nG7gisW03utaymgYC692ayGXmlQVaOnrrQ77RqmrreJ3SY2S3RbjW7rmNgsmdttkiwO9/rITBwp6Tkt7R4UGxurWrVqaeXKlSpYsKAkKSQkRJ07d1aTJk0kST179pQkNWvWTP3799ehQ4eUN29eNWvWTMuXL1ft2rXVoUOHa75u3bp1atOmjXbv3i3p8j5+oaGh2rZtm4oWLapq1appzZo1+v777yVJa9eu1bBhw7R3717lz59fr732mpo3b57uP8uFpFs/5nbFx8dr8MD+WrF8mXz9fNW23etq1aat61/IxUzsNrFZottqJnab2CzRbTW6rWNFc84qnW/9oNsQvzlCdd74TD9t2itJalCzjEI71lexh/Po0LG/1X/Mt1qwaustnuXWTm+IuOvnuJqJ3yOSmd0mNkt0W41u65jYLFnT7X0PvwvDsB9i7E5wqe5PP2J3ghODP5udOnVKf/zxh4KDg53HJkyYoB9//FHTpk1zyWtkxOAPAADgdrl68GeVjBj8AQBwNQZ/7iMzDf641DcT6Nixo7766isdPXpUv/76q6ZMmaK6devanQUAAAAAAACD3cPz5MzB399fo0eP1meffaahQ4cqV65catWqlVq2bGl3GgAAAAAAQIZzODLZxnhuhMFfJlC7dm3Vrl3b7gwAAAAAAAC4ES71BQAAAAAAANwQgz8AAAAAAADADXGpLwAAAAAAAGyThS3+Mgxn/AEAAAAAAABuiMEfAAAAAAAA4IYY/AEAAAAAAABuiD3+AAAAAAAAYBsHe/xlGM74AwAAAAAAANwQgz8AAAAAAADADTH4AwAAAAAAANwQe/wBAAAAAADANlnY5C/DcMYfAAAAAAAA4IYY/AEAAAAAAABuiMEfAAAAAAAA4IbY4w8AAAAAAAC2ycIWfxmGwR8AALinnY1Psjvhjvj5mPevcYfXjLY74Y74N59sd8IdOTWrnd0JAADAZlzqCwAAAAAAALghBn8AAAAAAACAGzLvGhEAAAAAAAC4DQd7/GUYzvgDAAAAAAAA3BCDPwAAAAAAAMAGiYmJGjBggKpUqaLHH39cI0eOVEpKisuen0t9AQAAAAAAABsMGjRI69at08SJE/Xff//p/fffV/78+dW8eXOXPD+DPwAAAAAAANgmi+7NTf7OnDmjb775RpMnT1bZsmUlSa+99pq2bt3K4A8AAAAAAAAw1aZNm+Tr66uqVas6j7Vv396lr8EefwAAAAAAAIDFjhw5ogIFCmj+/PmqW7euatWqpTFjxig5Odllr8EZfwAAAAAAAIDFzp8/r0OHDmnWrFkaOnSo4uLi9NFHH8nHx0evvfaaS16DwR8AAAAAAABs47g3t/iTh4eHzp07p08//VQFChSQJB07dkwzZ8502eCPS30BAAAAAAAAi+XOnVteXl7OoZ8kBQQE6M8//3TZazD4AwAAAAAAACxWrlw5JSQk6MCBA85j+/fvTzMIvFsM/gAAAAAAAACLFS1aVDVr1lSvXr20a9cu/fTTTxo3bpxatGjhstdgjz8AAAAAAADYJss9usefJI0YMUJhYWFq0aKFfHx89Morr6h169Yue34GfwAAAAAAAIAN/Pz8NGzYsAx7fi71BQAAAAAAANzQPT34O3XqlJYsWeK8HRgYqHXr1l33sevWrVNgYKBVaZleQkKC+oX2Vo3HKqvWUzU0JXKS3UnpYmK3ic0S3VYzsdvEZoluq5naLUmJiYlq/VIj/b5xvd0p6WLyWkvmrHc2jyzaMLKxgks95DxWu1x+/TaikU7OaK3fRjRSnQqu29Db1Uz8PjGxWaLbanRbx8Rmydxu4J6+1HfEiBFKSUlRvXr17E4xzsgRw/RHdLTGT5qiY8eOKbR3D+XPl1/PPFvX7rSbMrHbxGaJbquZ2G1is0S31UztTkhI0IC+3XVg/z67U9LN1LWWzFlvL8+smvzeUyr5cE7nsaIP+Wlm91oaMHOTFq0/rIZVC2tW91oq/06UDseds7H2+kz8PjGxWaLbanRbx8RmydxuU2Rx3MOb/GWwe3rwl5KSYneCkc6fP69538zRmLHjVaJkKZUoWUox+/Zq1swZmfqHnondJjZLdFvNxG4TmyW6rWZq94H9+zSgb3ej/j3D1LWWzFnvoIIPaPK7T8lx1X/YFPC/X5OX71bEoj8kSeGLdqj7i+VU+dFcmW7wZ+L3iYnNEt1Wo9s6JjZL5nYDkoGX+sbGxiowMFCrV69WSEiIKlSooEGDBmnPnj1q0qSJypcvrw4dOujcucv/ohQVFaV69eqpbNmyatKkiTZs2CBJCg8P17x58zRv3jyFhIQ4n3/jxo1q2LChypQpo1atWuno0aNpXn/atGmqVq2aqlWrplGjRqX5l8zly5frueeeU7ly5dS0aVOtX/9/l5qcO3dOvXr1UvXq1VW6dGnVrVtXK1ascN4fGBioBQsWqEGDBipdurRatmypI0eOOO8fOXKkatSoobJly6p169bau3evaxf2NuzZvUtJSUkqX76C81iFipW0fdtWJScn29Z1KyZ2m9gs0W01E7tNbJbotpqp3Vt+36iKlarqf5O/sjsl3Uxda8mc9a5R8iGt2fGXnu69KM3xn3b8pe6Rl/+d0SOrQ21CisnLM4s27jtpR+ZNmfh9YmKzRLfV6LaOic2Sud2AZODg74px48bpiy++UFhYmKZNm6bOnTvrgw8+0MSJE7VlyxbNnTtXUVFRCgsLU4cOHTR//nw9/vjjat++vY4fP67XXntN9erVU7169TR37lzn886ZM0d9+/bV3Llz9c8//2jEiBFpXnfhwoWaPHmyhgwZoq+++krz5s2TJO3atUs9evRQx44dtXDhQj3//PN68803dejQIUnS4MGDdeDAAU2aNEmLFi1S5cqV1adPHyUmJjqfOzw8XH369FFUVJROnz6t0aNHS7o8UJw9e7ZGjx6tRYsWKVeuXOrVq1cGr/CNnYyLU44cOeWZLZvzmL9/LiUkJOjMmTO2dd2Kid0mNkt0W83EbhObJbqtZmr3C02b650Pesrb28fulHQzda0lc9Z7wrLd6hG5XvGJl657f9GH/HTqqzb6slMNfTx3a6Y7208y8/vExGaJbqvRbR0TmyVzuwHJ4MFfp06dFBQUpAYNGsjf31/169fXE088oUqVKql69erav3+/pk2bptatW6tx48YqWrSoPvzwQxUvXlzTp0/X/fffL29vb3l7e+vBBx90Pm/Hjh1VrVo1BQYGqmnTptq1a1ea1x0yZIhKliypWrVq6dVXX9WsWbMkSRMnTtRLL72khg0bqnDhwmrTpo2efPJJzZw5U5JUpUoVDRw4UCVKlFCRIkX02muv6cyZMzp16pTzudu1a6fq1aurePHiatGihaKjoyVJR48elaenp/Lnz6+HH35YoaGh6tmzZ0Yv8Q3FX4hXtlQ/8CQ5b19MNcjMbEzsNrFZottqJnab2CzRbTVTu03EWtvv5L8X9GTPb/Xe+LXq81J5NapW2O6ka5j4fWJis0S31ei2jonNkrndJnE43OsjMzF2j79ChQo5P/f29laBAgXS3E5MTFRMTIzefvvtNF9Xvnx5xcTE3PB5H374Yefnfn5+SkhIcN6+7777VKxYMeftkiVLavLkyZKkmJgYLVmyRLNnz3bef/HiRdWoUUOS1LhxY61YsUJff/219u/frx07dkiSLl36v7/1LVz4//4Fz9fXVxcvXpQk1a9fX9OnT1etWrVUvnx51a5dW02bNr3Z8mQoLy+vNGcqSnLe9vb2tiMpXUzsNrFZottqJnab2CzRbTVTu03EWtvv3/MXtfXA39p64G8FFcyht54roQXrDtmdlYaJ3ycmNkt0W41u65jYLJnbDUgGD/6yZs2a5naWLNeevOjl5XXNsUuXLt30GvzrPc8VV2/GnJycLE9PT+fzvvnmm2rcuHGax1z5IdC9e3dt3rxZjRo1UosWLZQ7d269/PLLaR575bmuljt3bi1ZskS//PKLfvjhB02cOFFff/215s+fLx8f6y9ryZMnr86cOa2kpCR5eFz+Fjp5Mk7e3t7yy57d8p70MrHbxGaJbquZ2G1is0S31UztNhFrbZ8SBXMop5+Xft153HlsV+wZPVnqIRurrs/E7xMTmyW6rUa3dUxslsztBiSDL/VNj4CAAG3dujXNsa1btyogIEDStYO8W/nvv//SvNnH9u3bVbRoUedrxcbGqnDhws6P2bNna82aNTp37pwWLVqkUaNG6Z133tEzzzyjf/75R1L63ll49erVmjNnjmrWrKkBAwZowYIFOnjwoPbs2XNb/a4SGFRCHh4e2rZ1i/PY5t83qVTpMjcdnNrNxG4TmyW6rWZit4nNEt1WM7XbRKy1fZ6rXEgRbz2e5liFov7adfSMPUE3YeL3iYnNEt1Wo9s6JjZL5nYDkpsP/tq2bavp06dr/vz5OnDggEaMGKFdu3Y5L5P18fHR0aNHdfz48Vs802VZsmRRjx49tHPnTi1ZskRTp05V27Ztna+1ePFiTZ06VYcPH1ZkZKQiIyNVpEgRZcuWTT4+Plq2bJliY2P1008/aeDAgZJ0zenC15OcnKxhw4Zp+fLlio2NVVRUlHx8fFSkSJE7Wpe75ePjo4aNGmvQwP6K3r5Nq1au0NTISWrZqo0tPellYreJzRLdVjOx28RmiW6rmdptItbaPjPXxOihHPcprFVlPfJQdrWvG6TmTz6iT6O22512DRO/T0xslui2Gt3WMbFZMrfbJFkcDrf6yEwcKek55SwTiY2NVa1atbRy5UoVLFhQkhQSEqLOnTurSZMmkuR844uPP/5YU6dOVWRkpOLi4lSiRAl169ZNVapUkXT57L+3335bFy9e1G+//aagoCBNnTpV1apVkyRFRUUpIiJCq1at0rp16/TOO+/ojTfe0P/+9z95eXnp9ddf12uvveZs++677xQeHq7Y2Fg9/PDD6tKli+rVqydJWrFihT755BOdOHFCBQsWVLt27TR69Gj17NlTDRo0UGBg4A1fW5ImTZqk6dOnKy4uTkWLFlWPHj30+ONp/3b4Ri4k3e2qXys+Pl6DB/bXiuXL5Ovnq7btXlerNm1d/0IuZmK3ic0S3VYzsdvEZoluq1nRfTY+A35R/n81KpfS52Mnq2Llqi5/bj8f1+7YYvpaSxm33kXaTXPp8/03t53q9luin3b8JUmqUiy3hrWrqtKFH9ShE+f00YyNWrzxyF2/zqlZ7e76Oa5m4s8SE5sluq1Gt3VMbJas6fY2djO2uzdx/WG7E1zq9aoP3/pBFjFu8IfblxGDPwAA3EVGD6MyiqsHf1Ywda1dPfizSkYM/gAAGYfBn/vITIM/t77UFwAAAAAAALhX3cPzZAAAAAAAANgtk22L51Y44w8AAAAAAABwQwz+AAAAAAAAADfE4A8AAAAAAABwQ+zxBwAAAAAAANtwVlrGYW0BAAAAAAAAN8TgDwAAAAAAAHBDDP4AAAAAAAAAN8QefwAAAAAAALCNw+GwO8FtccYfAAAAAAAA4IYY/AEAAAAAAABuiMEfAAAAAAAA4IYY/AEAAAAAAABuiDf3AAAAAAAAgG14a4+Mwxl/AAAAAAAAgBti8AcAAAAAAAC4IS71BQAA9zQ/H/51yCqmrvWpWe3sTrgjOat0tjvhjpzeEGF3AgAAbsPMf/sCAAAAAACAW8jiYJe/jMKlvgAAAAAAAIAbYvAHAAAAAAAAuCEGfwAAAAAAAIAbYo8/AAAAAAAA2IYd/jIOZ/wBAAAAAAAAbojBHwAAAAAAAOCGGPwBAAAAAAAAbog9/gAAAAAAAGAbB5v8ZRjO+AMAAAAAAADcEIM/AAAAAAAAwA0x+AMAAAAAAADcEHv8AQAAAAAAwDYONvnLMJzxBwAAAAAAALghBn8AAAAAAACAG2LwBwAAAAAAALgh9vgDAAAAAACAbTgrLeOwti4QFRWlkJAQuzMslZCQoH6hvVXjscqq9VQNTYmcZHdSupjYbWKzRLfVTOw2sVmi22p0W8fEZoluq2Tz9NDGOb0VXKmY89iIbi8qfnNEmo+3Xn7SxsrrM22tr6DbWnRbx8RmydxugDP+cEdGjhimP6KjNX7SFB07dkyhvXsof778eubZunan3ZSJ3SY2S3RbzcRuE5sluq1Gt3VMbJbotoJXNg9NGdJWpR7Nn+Z4UNF8Cv18gaYt/M157N//Llidd0smrXVqdFuLbuuY2CyZ2w0w+MNtO3/+vOZ9M0djxo5XiZKlVKJkKcXs26tZM2dk6h96Jnab2CzRbTUTu01slui2Gt3WMbFZotsKQUUfUuSQtnI4rnNfQF6NmrJCx0+dtT4snUxa69Tothbd1jGxWTK3G5AMv9Q3NjZWgYGBWr16tUJCQlShQgUNGjRIe/bsUZMmTVS+fHl16NBB586dU8+ePdWzZ880Xx8YGKh169ZJkkJCQjRjxgy99NJLKlOmjBo1aqTo6GjnY48cOaK2bduqXLlyatiwoSZOnHjN5b0jR45UxYoVFRwcrGnTpqW5b9asWc7G1q1ba/fu3c77jh8/rnfeeUdVqlRR6dKl9cILL2jTpk1p/ozLli1T7dq1VaZMGXXo0EFnzpxx5VLelj27dykpKUnly1dwHqtQsZK2b9uq5ORk27puxcRuE5sluq1mYreJzRLdVqPbOiY2S3RbIbjSo1qzYY9qvvppmuN+93urQN6c2nvohE1l6WPSWqdGt7Xoto6JzZK53SZxOBxu9ZGZGD34u2LcuHH64osvFBYWpmnTpqlz58764IMPNHHiRG3ZskVz585N1/OEh4erffv2Wrhwofz8/DRo0CBJUlJSkjp06KDs2bPrm2++Ufv27RUREZHma48ePardu3dr9uzZ6tq1qz755BPnUHHVqlWKiIhQaGio5s2bp0qVKqlNmzb6559/JEkffvihLl26pFmzZmn+/PnKmzev+vfvn+b5x44dq5EjR2r69Onavn27Jk+efJerdudOxsUpR46c8syWzXnM3z+XEhISbB1I3oqJ3SY2S3RbzcRuE5sluq1Gt3VMbJbotsL4OT+r+6dRir9wMc3xoIC8Sk5OVo83ntW+pWFaN7unXmlYzabKGzNprVOj21p0W8fEZsncbkByk0t9O3XqpKCgIAUFBWnIkCGqX7++nnjiCUlS9erVtX///nQ9zwsvvKDatWtLktq1a6d3331XkvTbb7/pzz//1Ndffy1fX189+uij2rNnj7777jvn13p5eenjjz9Wzpw5VaxYMa1fv16zZs1StWrVNGHCBHXo0EFPP/20JOm9997TmjVrtHDhQrVq1Uq1a9fWs88+q4ceekiS9Morr6h9+/Zp2t555x2VLVtWktSwYUNt3779Llbs7sRfiFe2VD/wJDlvX0xMtCMpXUzsNrFZottqJnab2CzRbTW6rWNis0S3nYoHPKSUFGnPweP6ctaPCq5UTGP6NtfZc/Fa+MM2u/OcTF1ruq1Ft3VMbJbM7QYkNxn8FSpUyPm5t7e3ChQokOZ2Yjr/j1ikSBHn576+vrp48fLfbO7evVsBAQHy9fV13l++fPk0g79ChQopZ86cztslS5bUnDlzJEkxMTEaPny4Ro4c6bw/ISFBBw8elMPhUIsWLbR48WL9/vvvOnDggKKjo685Xbhw4cLXbbODl5fXNWt65ba3t7cdSeliYreJzRLdVjOx28RmiW6r0W0dE5sluu0049t1Wvzjdp3+97wkKXrvMRUrnEdvNgvOVIM/U9eabmvRbR0TmyVzuwHJTQZ/WbNmTXM7S5Zrr2B2OBxKSUlx3k5KSrrmMZ6enjd8/tRfK+ma21e/ZnJysvP5Ll26pN69e6t69eppHuPr66vk5GS99tpr+vfff/Xcc88pJCREFy9eVOfOndPVZoc8efLqzJnTSkpKkofH5W+hkyfj5O3tLb/s2W2uuzETu01slui2mondJjZLdFuNbuuY2CzRbbcrQ78rdu3/S09VKW5TzfWZutZ0W4tu65jYLJnbbZLMtSuee3GLPf7Sw9PTU//995/z9pEjR9L9tcWKFdPBgwd17tw557EdO3akecyRI0cUHx/vvL1t2zYVLVpUkhQQEKC//vpLhQsXdn6MHTtWW7Zs0b59+7RhwwZFRkbqrbfeUs2aNXXixOVNkq8eLmYWgUEl5OHhoW1btziPbf59k0qVLnPdoWtmYWK3ic0S3VYzsdvEZoluq9FtHRObJbrtFNqxvr4bm/YvqssGFtSeg8dtKro+U9eabmvRbR0TmyVzuwHpHhr8lSlTRr/88ovWrl2rPXv2aODAgek+i6569erKly+fQkNDFRMTo6VLl2rq1KlpHpOQkKAePXpo7969mjVrlr7//nu9+uqrki7vFzhlyhTNnz9fhw8f1vDhw7VkyRI98sgjyp49u7JkyaLvvvtOR48e1dKlSxUeHi5J15xKnFn4+PioYaPGGjSwv6K3b9OqlSs0NXKSWrZqY3faTZnYbWKzRLfVTOw2sVmi22p0W8fEZoluOy3+cbuCKxbTe61rKaBgLr3ZrIZeaVBVo6eutDstDVPXmm5r0W0dE5slc7sBSXKkZNbTytIhNjZWtWrV0sqVK1WwYEFJUkhIiDp37qwmTZpIknr27ClJGjhwoPr166elS5fKz89P7777rsaMGaOhQ4eqWrVq13zdunXr1KZNG+3evVvS5X36QkNDnWfyVatWTWvWrNH333+vqKgoTZ8+XZUqVdLcuXOVM2dOvffee3r++eedrVOnTlVkZKROnjypRx99VN26dXNe+jt79myNGTNGZ8+eVUBAgF577TX16NFD06dPV+7cua/5M4aHh2v9+vWaNm1autbpwrVXNd+1+Ph4DR7YXyuWL5Ovn6/atntdrdq0df0LuZiJ3SY2S3RbzcRuE5sluq1Gt3VMbJbovpmcVTrf+kG3IX5zhOq88Zl+2rRXktSgZhmFdqyvYg/n0aFjf6v/mG+1YNXWu36d0xsi7vo5UuN7xFp0W8vEbhObJWu6vd1iM7Y7M2fLMbsTXKpZ+fx2JzgZPfizyqlTp/THH38oODjYeWzChAn68ccf0z18s1NGDP4AAACQubl68GcVVw/+AMAU9/Lgb+7WP+1OcKmm5fLZneB0z1zqe7c6duyor776SkePHtWvv/6qKVOmqG7dunZnAQAAAAAAANd1D8+T08/f31+jR4/WZ599pqFDhypXrlxq1aqVWrZsaXcaAAAAAAAAcF0M/tKpdu3aql27tt0ZAAAAAAAAQLow+AMAAAAAAIBt2Icu47C2AAAAAAAAgBti8AcAAAAAAAC4IQZ/AAAAAAAAgBtijz8AAAAAAADYxuFw2J3gtjjjDwAAAAAAAHBDDP4AAAAAAAAAN8TgDwAAAAAAAHBD7PEHAAAAAAAA27DDX8bhjD8AAAAAAADADTH4AwAAAAAAANwQgz8AAAAAAADADbHHHwAAAAAAAGzjYJO/DMMZfwAAAAAAAIAb4ow/AAAAwA2d3hBhd8IdyVmls90Jd8TU9QYAuDfO+AMAAAAAAADcEGf8AQAAAAAAwDZZxCZ/GYUz/gAAAAAAAAA3xOAPAAAAAAAAcEMM/gAAAAAAAAA3xB5/AAAAAAAAsI2DLf4yDGf8AQAAAAAAAG6IwR8AAAAAAADghhj8AQAAAAAAAG6IPf4AAAAAAABgG4fY5C+jcMYfAAAAAAAA4IYY/AEAAAAAAABuiMEfAAAAAAAA4IbY4w8AAAAAAAC2cbDFX4bhjD8AAAAAAADADTH4AwAAAAAAANwQgz8AAAAAAADADTH4SyUwMFDr1q3L8NcJCQlRVFRUhr9ORkpISFC/0N6q8Vhl1XqqhqZETrI7KV1M7DaxWaLbaiZ2m9gs0W01uq1jYrNEt9VM687m6aGNc3oruFIx57ER3V5U/OaINB9vvfykjZXXZ9paX0G3tUzsNrFZMrcb4M09cEdGjhimP6KjNX7SFB07dkyhvXsof778eubZunan3ZSJ3SY2S3RbzcRuE5sluq1Gt3VMbJbotppJ3V7ZPDRlSFuVejR/muNBRfMp9PMFmrbwN+exf/+7YHXeLZm01qnRbS0Tu01slsztNkUW8e4eGYXBH27b+fPnNe+bORozdrxKlCylEiVLKWbfXs2aOSNT/9AzsdvEZoluq5nYbWKzRLfV6LaOic0S3VYzqTuo6EOKHNL2uu8SGRSQV6OmrNDxU2etD0snk9Y6NbqtZWK3ic2Sud2AZMilvrGxsQoMDNTq1asVEhKiChUqaNCgQdqzZ4+aNGmi8uXLq0OHDjp37px69uypnj17pvn61Jfwrl27Vo0aNVKZMmVUq1YtzZo167qvuWLFCpUtW1Y//fTTbb3+FbNmzXI+tnXr1tq9e/d1Xyc5OVkTJkxQrVq1VLZs2WseGxgYqAULFqhBgwYqXbq0WrZsqSNHjtztkt6VPbt3KSkpSeXLV3Aeq1CxkrZv26rk5GQby27OxG4TmyW6rWZit4nNEt1Wo9s6JjZLdFvNpO7gSo9qzYY9qvnqp2mO+93vrQJ5c2rvoRM2laWPSWudGt3WMrHbxGbJ3G5AMmTwd8W4ceP0xRdfKCwsTNOmTVPnzp31wQcfaOLEidqyZYvmzp1706+/dOmS3nvvPdWtW1dLlizRu+++qwEDBmjfvn1pHvf777+rW7du+vjjjxUcHHzbr79q1SpFREQoNDRU8+bNU6VKldSmTRv9888/1zSNGTNGkyZNUu/evTVv3jwVKFBAb7zxhs6fP+98THh4uPr06aOoqCidPn1ao0ePvotVvHsn4+KUI0dOeWbL5jzm759LCQkJOnPmjH1ht2Bit4nNEt1WM7HbxGaJbqvRbR0TmyW6rWZS9/g5P6v7p1GKv3AxzfGggLxKTk5Wjzee1b6lYVo3u6deaVjNpsobM2mtU6PbWiZ2m9gsmdsNSIYN/jp16qSgoCA1aNBA/v7+ql+/vp544glVqlRJ1atX1/79+2/69WfPntWZM2eUK1cuFSxYUM8//7wmT56s3LlzOx+zf/9+dezYUT169NBzzz13R68/YcIEdejQQU8//bSKFCmi9957TwUKFNDChQvTPF9KSoqmT5+ud999V7Vq1dIjjzyisLAwZc2aNc1j27Vrp+rVq6t48eJq0aKFoqOj73Yp70r8hXhlS/UDT5Lz9sXERDuS0sXEbhObJbqtZmK3ic0S3Vaj2zomNkt0W83U7tSKBzyklBRpz8HjatzlS0XOW6sxfZvr+afL2p2WhqlrTbe1TOw2sVkyt9skDod7fWQmRu3xV6hQIefn3t7eKlCgQJrbibf4P1yOHDnUokUL9e3bV1988YWefvppvfjii3rggQecjxk8eLCSkpKUL1++O379mJgYDR8+XCNHjnTen5CQoIMHD6Z5vlOnTunMmTMqV66c85inp6dKly6tmJgY57HChQs7P/f19dXFi2n/5tJqXl5e16z1ldve3t52JKWLid0mNkt0W83EbhObJbqtRrd1TGyW6Laaqd2pzfh2nRb/uF2n/718dU303mMqVjiP3mwWrIU/bLO57v+YutZ0W8vEbhObJXO7AcmwwV/WrFnT3M6S5doTFh0Oh1JSUpy3k5KS0tzfv39/vfLKK1qxYoVWrFih2bNn64svvtBTTz0lSWrevLk8PT01aNAgVa9ePc1UPz2vL12+pLh3796qXr16muO+vr5pbnt5ed3w61PvE+Dp6Xndx9klT568OnPmtJKSkuThcflb6OTJOHl7e8sve3ab627MxG4TmyW6rWZit4nNEt1Wo9s6JjZLdFvN1O6rXRn6XbFr/196qkpxm2quz9S1pttaJnab2CyZ2w1Ihl3qmx6enp7677//nLdTvxFGXFycBgwYoMKFC6tjx4765ptv9Nhjj2nVqlXOxzzzzDN6++23FR8fr3Hjxt1RQ0BAgP766y8VLlzY+TF27Fht2bIlzeP8/PyUK1euNMcvXryoHTt2KCAg4I5e2wqBQSXk4eGhbVu3OI9t/n2TSpUuc8NhaGZgYreJzRLdVjOx28RmiW6r0W0dE5sluq1mandqoR3r67uxndMcKxtYUHsOHrep6PpMXWu6rWVit4nNkrndgOSGg78yZcrol19+0dq1a7Vnzx4NHDjQecbcAw88oOXLl2vIkCE6fPiwNmzYoF27dqlkyZJpnsPX11ddu3bV+PHjFRsbe9sN7dq105QpUzR//nwdPnxYw4cP15IlS/TII49c89i2bdvq888/16pVqxQTE6PQ0FAlJCRcs79gZuLj46OGjRpr0MD+it6+TatWrtDUyElq2aqN3Wk3ZWK3ic0S3VYzsdvEZoluq9FtHRObJbqtZmp3aot/3K7gisX0XutaCiiYS282q6FXGlTV6Kkr7U5Lw9S1pttaJnab2CyZ220Su/fkc+c9/hwpqa+LzaRiY2NVq1YtrVy5UgULFpQkhYSEqHPnzmrSpIkkqWfPnpKkgQMHql+/flq6dKn8/Pz07rvvasyYMRo6dKiqVaumbdu2aciQIdq1a5fuv/9+NW3aVO+++66yZMmiwMBATZ06VdWqVVNKSopeeukl+fv7q2/fvul+/Y8//liSNHXqVEVGRurkyZN69NFH1a1bN+elv6m/9tKlS/r88881d+5cnTt3ThUqVFDv3r1VvPjlyw1SN0lSVFSUIiIi0pyleCsXkm79mNsVHx+vwQP7a8XyZfL181Xbdq+rVZu2rn8hFzOx28RmiW6rmdhtYrNEt9Xoto6JzRLdVrOiO2eVzrd+0G2I3xyhOm98pp827ZUkNahZRqEd66vYw3l06Njf6j/mWy1YtfWuX+f0hoi7fo7U+B6xFt3WMbFZsqbb26jN2Fxr2c44uxNcqk6J3Ld+kEWMGPzh7mTE4A8AAADICK4e/FnF1YM/APceBn/uIzMN/tzuUl8AAAAAAAAAhr2rLwAAAAAAANyLQ5lsYzw3whl/AAAAAAAAgBti8AcAAAAAAAC4IQZ/AAAAAAAAgBtijz8AAAAAAADYJgtb/GUYzvgDAAAAAAAA3BCDPwAAAAAAAMANMfgDAAAAAAAA3BB7/AEAAAAAAMA2DrHJX0bhjD8AAAAAAADADTH4AwAAAAAAANwQgz8AAAAAAADADbHHHwAAAAAAAGzjYIu/DMMZfwAAAAAAAIAbYvAHAAAAAAAAuCEGfwAAAAAAAIAbYo8/AAAAAAAA2MYhNvnLKAz+ABjrbHyS3Ql3xM+HH70AANzI6Q0RdifckZzPDrE74bad/r633QkAgAzGpb4AAAAAAACAzdq3b6+ePXu69DkZ/AEAAAAAAAA2+u677/Tjjz+6/Hm53gwAAAAAAAC2yXKPb/F35swZDRs2TGXKlHH5czP4AwAAAAAAAGzyySefqFGjRjpx4oTLn5tLfQEAAAAAAAAbrF27Vhs3blSnTp0y5PkZ/AEAAAAAAAAWS0hIUL9+/fTRRx/J29s7Q16DS30BAAAAAABgG4fuzU3+IiIiVLp0aQUHB2fYazD4AwAAAAAAACz23Xff6eTJk6pQoYIkKTExUZL0/fffa/PmzS55DQZ/AAAAAAAAgMWmTZumpKQk5+0RI0ZIkj788EOXvQaDPwAAAAAAAMBiBQoUSHP7/vvvlyQVLlzYZa/B4A8AAAAAAAC2cdybW/xZgsEfAAAAAAAAYLOPP/7Y5c+ZxeXPCAAAAAAAAMB2DP4AAAAAAAAAN8SlvgAAAAAAALANW/xlHM74AwAAAAAAANwQgz/ckYSEBPUL7a0aj1VWradqaErkJLuT0sXEbhObJXO7r0hMTFTrlxrp943r7U5JFxPX28RmiW6r0W0dE5sluq1mYrdpzdk8s2rjhDcVXO7ha+7Lfr+XYmZ3Uatny9hQlj6mrfcVdFvHxGbJ3G6AS31xR0aOGKY/oqM1ftIUHTt2TKG9eyh/vvx65tm6dqfdlIndJjZL5nZLl3+pD+jbXQf277M7Jd1MXG8TmyW6rUa3dUxslui2mondJjV7eWbVlD6NVSog93XvH/Tm08qfy8/iqttj0nqnRrd1TGyWzO0GGPzhtp0/f17zvpmjMWPHq0TJUipRspRi9u3VrJkzMvUPPRO7TWyWzO2WpAP792lA3+5KSUmxOyXdTFxvE5sluq1Gt3VMbJbotpqJ3SY1BxXOpcjejeS4wUZXj5cuqKcrFNGfp85ZG3YbTFrv1Oi2jonNkrndJslyox9+uGtc6nsDf/31l959911VrVpV1apV06BBg5SYmKioqCg1b95cb7/9tipVqqSFCxfq3Llz6tWrl6pXr67SpUurbt26WrFihfO5AgMDtWDBAjVo0EClS5dWy5YtdeTIEef90dHReumll1S2bFk1b95cn332mVq3bu28f/ny5XruuedUrlw5NW3aVOvX23vp4Z7du5SUlKTy5Ss4j1WoWEnbt21VcnKyjWU3Z2K3ic2Sud2StOX3japYqar+N/kru1PSzcT1NrFZottqdFvHxGaJbquZ2G1Sc3DZh7VmyyHV7DLlmvuyeWbVmK7P6b3Pv1fixSQb6tLHpPVOjW7rmNgsmdsNSAz+risxMVGvvvqq4uPjNW3aNI0ePVqrV6/WsGHDJEmbN2/Wo48+qq+//lo1atTQ4MGDdeDAAU2aNEmLFi1S5cqV1adPHyUmJjqfMzw8XH369FFUVJROnz6t0aNHS5LOnj2rN954Q6VKldL8+fPVoEEDjRs3zvl1u3btUo8ePdSxY0ctXLhQzz//vN58800dOnTI0jVJ7WRcnHLkyCnPbNmcx/z9cykhIUFnzpyxretWTOw2sVkyt1uSXmjaXO980FPe3j52p6SbiettYrNEt9Xoto6JzRLdVjOx26Tm8d/+ru5frlB8wrWDve4tH9fWfce1ctMBG8rSz6T1To1u65jYLJnbDUhc6ntdP/30k44fP66vv/5aDzzwgCTpo48+UseOHdWrVy85HA517NhR3t7ekqQqVaqoXbt2Kl68uCTptdde05w5c3Tq1Cnly5dPktSuXTtVr15dktSiRQvNmDFDkrR48WLdd9996tu3r7JmzaqiRYvq999/V1xcnCRp4sSJeumll9SwYUNJUps2bbRhwwbNnDlTPXv2tG5RUom/EK9sqX7gSXLevphq2JnZmNhtYrNkbrepTFxvE5sluq1Gt3VMbJbotpqJ3SY2Xy2ocC690bCiqr45we6UWzJ1vem2jonNkrndgMTg77piYmJUpEgR59BPkipWrKikpCQlJSXJ39/fOfSTpMaNG2vFihX6+uuvtX//fu3YsUOSdOnSJedjChcu7Pzc19dXFy9elCTt3r1bpUqVUtasWZ33ly9fXsuXL3e2LFmyRLNnz3bef/HiRdWoUcPFf+r08/LySnM2oyTn7dTrktmY2G1is2Rut6lMXG8TmyW6rUa3dUxslui2mondJjZf7Yuu9RQWuUYnTv9nd8otmbredFvHxGbJ3G6TsMNfxmHwdx1eXl7XHLsyxEtOTr7m/u7du2vz5s1q1KiRWrRoody5c+vll19O8xhPT8/rvlbWrFmveROB1LcvXbqkN998U40bN07zGDt/uOTJk1dnzpxWUlKSPDwufwudPBknb29v+WXPblvXrZjYbWKzZG63qUxcbxObJbqtRrd1TGyW6Laaid0mNqf2cJ7sql66kMo8klcfv1VLknSfl6fC36unpjVLqnGv2bd4BmuZut50W8fEZsncbkBij7/rCggI0MGDB9Ncq79lyxZ5eHgoS5a0S3bu3DktWrRIo0aN0jvvvKNnnnlG//zzjySl611BixUrpp07d6bZEPTKGYNXWmJjY1W4cGHnx+zZs7VmzZq7/FPeucCgEvLw8NC2rVucxzb/vkmlSpe5Zn0yExO7TWyWzO02lYnrbWKzRLfV6LaOic0S3VYzsdvE5tSOnjyrUq2/VLX2E50ff546q7DINer06Xd2513D1PWm2zomNkvmdgMSg7/reuKJJ1SoUCF1795du3fv1m+//aawsDA1aNBA2a+a5mfLlk0+Pj5atmyZYmNj9dNPP2ngwIGSdM2pwNdTv359nTt3TkOHDtWBAwf09ddfa/Hixc7727Ztq8WLF2vq1Kk6fPiwIiMjFRkZqSJFirj0z3w7fHx81LBRYw0a2F/R27dp1coVmho5SS1btbGtKT1M7DaxWTK321QmrreJzRLdVqPbOiY2S3RbzcRuE5tTu5Scov3HTqf5SLqUrBNn/tOxk+fszruGqetNt3VMbJbM7QYkyZGSntPS7kFHjhxRWFiY1q1bp/vvv18NGzZU165d9d133ykiIkKrVq1yPnbFihX65JNPdOLECRUsWFDt2rXT6NGj1bNnTzVo0ECBgYGaOnWqqlWrJkmKiopK8xybN2/WgAEDtG/fPpUpU0ZFihTRiRMnNHHiREnSd999p/DwcMXGxurhhx9Wly5dVK9evXT/WS5c+8Zgdy0+Pl6DB/bXiuXL5Ovnq7btXlerNm1d/0IuZmK3ic2SNd1n4zPgmzuVGpVL6fOxk1WxclWXPq+fj+t3WTDx+8TEZoluq9FtHRObJbqtZmK3Vc05nx3isueKX9lbdbpO109bD19z364ZnTRo6k+a/v32u36d09/3vuvnuJqJ3yMS3VYysVmyptv7Ht6M7beYM3YnuNRjj+SwO8GJwZ/Njhw5ouPHj6ty5crOYwMGDFB8fLw+/vhjl7xGRgz+gMwgowd/GSUjBn8AAMBerhz8WSUjBn8A7hyDP/eRmQZ/XOprs3Pnzqldu3ZaunSpjh49qmXLlmnBggWqW7eu3WkAAAAAAAAw2D08T84cSpQooY8++kgjR47Un3/+qfz586tXr16qWbOm3WkAAAAAAAAwGIO/TKBZs2Zq1qyZ3RkAAAAAAACWc8hhd4Lb4lJfAAAAAAAAwA0x+AMAAAAAAADcEIM/AAAAAAAAwA2xxx8AAAAAAABs42CLvwzDGX8AAAAAAACAG2LwBwAAAAAAALghBn8AAAAAAACAG2KPPwAAAAAAANiGLf4yDmf8AQAAAAAAAG6IwR8AAAAAAADghhj8AQAAAAAAAG6IwR8AAAAAAADghnhzDwAAAAAAANiHd/fIMJzxBwAAAAAAALghBn8AAAAAAACAG3KkpKSk2B2BjHUhye4CAAAAAJlNzmeH2J1wR05/39vuhHvK2Xjz/oPSz8fMXc28zcx2iQ0H/rE7waWqBDxgd4LTPfxtBQAAAAAAALs52OQvw3CpLwAAAAAAAOCGGPwBAAAAAAAAbojBHwAAAAAAAOCG2OMPAAAAAAAAtnGwxV+G4Yw/AAAAAAAAwA0x+AMAAAAAAADcEIM/AAAAAAAAwA2xxx8AAAAAAABswxZ/GYcz/gAAAAAAAAA3xOAPAAAAAAAAcEMM/gAAAAAAAAA3xB5/AAAAAAAAsA+b/GUYzvgDAAAAAAAA3BCDPwAAAAAAAMANMfgDAAAAAAAA3BCDP9yRhIQE9QvtrRqPVVatp2poSuQku5PSxcRuE5sluq1mYreJzRLdVqPbOiY2S3RbzcRuE5sl87qzeWbVxglvKrjcw9fcl/1+L8XM7qJWz5axoSx9TFvvK0ztlqTExES1fqmRft+43u6UdDF5rU3gcLP/ZSa8uQfuyMgRw/RHdLTGT5qiY8eOKbR3D+XPl1/PPFvX7rSbMrHbxGaJbquZ2G1is0S31ei2jonNEt1WM7HbxGbJrG4vz6ya0qexSgXkvu79g958Wvlz+VlcdXtMWu/UTO1OSEjQgL7ddWD/PrtT0s3UtQYY/OG2nT9/XvO+maMxY8erRMlSKlGylGL27dWsmTMy9Q89E7tNbJbotpqJ3SY2S3RbjW7rmNgs0W01E7tNbJbM6g4qnEuRvRvJcYMTXB4vXVBPVyiiP0+dszbsNpi03qmZ2n1g/z4N6NtdKSkpdqekm6lrDUhc6ptGbGysAgMD9e233yo4OFiVK1fWoEGDlJSUpJSUFI0dO1YhISEqXbq0atSooYiICOfXtm7dWhEREWrRooXKlSunli1bKiYmxnn/pk2bnPeVL19eb775pk6cOCFJqlOnjiZPnpympWHDhpozZ44kac6cOapbt65Kly6tatWqacCAAbp06ZIFK3J9e3bvUlJSksqXr+A8VqFiJW3ftlXJycm2dd2Kid0mNkt0W83EbhObJbqtRrd1TGyW6Laaid0mNktmdQeXfVhrthxSzS5Trrkvm2dWjen6nN77/HslXkyyoS59TFrv1Ezt3vL7RlWsVFX/m/yV3SnpZupaAxKDv+uKiIjQqFGjFBERoWXLlik8PFzz58/XlClTNHjwYC1dulRvv/22wsPDtWPHDufX/e9//9Ozzz6rqKgo5c2bV+3bt1diYqLOnj2rDh066IknntCiRYs0ceJEHT58WOPGjZMk1a9fX99//73zeWJiYnTgwAHVqVNH69ev16BBg9S1a1ctXbpUAwYM0Ny5c7Vy5UrL1+WKk3FxypEjpzyzZXMe8/fPpYSEBJ05c8a2rlsxsdvEZoluq5nYbWKzRLfV6LaOic0S3VYzsdvEZsms7vHf/q7uX65QfMK1g73uLR/X1n3HtXLTARvK0s+k9U7N1O4XmjbXOx/0lLe3j90p6WbqWpvE4XCvj8yEwd91dOvWTZUrV9Zjjz2md999V19//bXy5cunoUOHqnr16ipYsKBatGih3Llza+/evc6ve/LJJ9W2bVs98sgjCgsL099//61ffvlFFy5cUKdOnfT222+rUKFCqlSpkurUqeP82gYNGmjLli3666+/JElLlixRjRo19MADD+i+++7T4MGDVadOHRUsWFB169ZVyZIl07yu1eIvxCtbqh94kpy3LyYm2pGULiZ2m9gs0W01E7tNbJbothrd1jGxWaLbaiZ2m9gsmdudWlDhXHqjYUV1/3KF3Sm3ZOp6m9ptItYaJmOPv+uoWLGi8/PSpUvr77//VvHixXXkyBF9+umniomJ0c6dOxUXF5fmtN7UX+fr66uAgADFxMTo6aefVuPGjRUZGamdO3dq37592r17t/PxjzzyiAIDA7V06VK1bdtWS5YsUYcOHZyv7+3trc8//9z5dYcOHVKNGjUsWo1reXl5KfGqH25Xbnt7e9uRlC4mdpvYLNFtNRO7TWyW6LYa3dYxsVmi22omdpvYLJnbndoXXespLHKNTpz+z+6UWzJ1vU3tNhFrDZNxxt91eHp6Oj+/MtibO3eu2rZtq4SEBNWpU0eRkZF66KGH0nydh0faOeqlS5eUJUsWHT9+XM8//7x+++03lSpVSr1791a7du3SPLZ+/fpatmyZYmJiFBsbq1q1akmSfvrpJzVp0kQnT55UcHCwPv/88zQDRjvkyZNXZ86cVlLS/53Of/JknLy9veWXPbuNZTdnYreJzRLdVjOx28RmiW6r0W0dE5sluq1mYreJzZK53Vc8nCe7qpcupI/fqqW4RR8qbtGHKpTnAYW/V0/zh75sd941TF1vU7tNxFrDZAz+rmPnzp3Oz6Ojo5UnTx7nvn69e/dW48aNlTNnTp06dSrNOxHt2rXL+fnZs2d1+PBhBQYGavny5XrggQf0v//9T6+++qoqV66sI0eOpPnaBg0aaOvWrZo/f76eeuop3X///ZIuv7HHiy++qIEDB6pZs2Z65JFHdPjwYVvfASkwqIQ8PDy0besW57HNv29SqdJllCVL5v2WMrHbxGaJbquZ2G1is0S31ei2jonNEt1WM7HbxGbJ3O4rjp48q1Ktv1S19hOdH3+eOquwyDXq9Ol3duddw9T1NrXbRKx1xnO42UdmwnfodQwePFjbt2/Xr7/+qs8++0yvvPKKcubMqbVr1+rAgQOKjo7W+++/r4sXL6Y53ffbb7/V/PnzFRMToz59+ih//vyqVq2acuTIoWPHjmnt2rU6cuSIxo0bp2XLlqX52vz586ts2bKaMmWK6tev7zyeI0cObd68Wbt379bevXvVs2dPxcXFXXOasZV8fHzUsFFjDRrYX9Hbt2nVyhWaGjlJLVu1sa0pPUzsNrFZottqJnab2CzRbTW6rWNis0S31UzsNrFZMrf7ikvJKdp/7HSaj6RLyTpx5j8dO3nO7rxrmLrepnabiLWGyRwpdp46lslcucS2a9eumjJlipKTk9WiRQt16dJFBw4cUO/evbVz5075+/urXr16Onz4sB588EENHDhQrVu3VsGCBXXgwAHt3LlTlStXVlhYmPLnz69Lly5p4MCBWrx4sRwOh8qUKaPg4GCFh4dr7dq1zk1Bp02bplGjRmnt2rXy8vKSJJ04cUK9evXSpk2b5Ovrq6eeekqenp46cuSIJk6cmK4/14Vr32DrrsXHx2vwwP5asXyZfP181bbd62rVpq3rX8jFTOw2sVmi22omdpvYLNFtNbqtY2KzRLfVTOw2sVmypjvns0Nc+nzxK3urTtfp+mnr4Wvu2zWjkwZN/UnTv99+169z+vved/0cV+P75MbOxmfAf1D+fzUql9LnYyerYuWqLn1ePx/Xv52BFWvtfQ+/C8PWw2ftTnCpcg/72Z3gxOAvlSuDv5UrV6pgwYK39bWtW7dW1apV1aVLlzt+/VGjRumvv/7SJ598csfPcT0ZMfgDAAAAYDZXD/6skhGDP9xYRg7+MkpGDP6swODPfWSmwd89/G2VeezatUs7d+7UV199pS+//NLuHAAAAAAAAOtkto3x3Ah7/GUC0dHRGjBggJo1a6bKlSvbnQMAAAAAAAA3wKW+9wAu9QUAAABwNS71RXpwqa917ulLfY+42aW+hTLPpb6c8QcAAAAAAAC4oXt4ngwAAAAAAAC7OdjkL8Nwxh8AAAAAAADghhj8AQAAAAAAAG6IwR8AAAAAAADghtjjDwAAAAAAALZxsMVfhuGMPwAAAAAAAMANMfgDAAAAAAAA3BCDPwAAAAAAAMANsccfAAAAAAAAbMMWfxmHM/4AAAAAAAAAN8TgDwAAAAAAAHBDDP4AAAAAAAAAN8QefwAAAAAAALAPm/xlGM74AwAAAAAAANyQIyUlJcXuCGSsC0l2FwAAAACAa+Ss0tnuhDtyekOE3QnI5Lzv4Wsyo4+eszvBpUoX8LU7wYkz/gAAAAAAAAA3dA/PkwEAAAAAAGA3B5v8ZRjO+AMAAAAAAADcEIM/AAAAAAAAwA0x+AMAAAAAAADcEHv8AQAAAAAAwDYOtvjLMJzxBwAAAAAAALghBn8AAAAAAACAG2LwBwAAAAAAALgh9vgDAAAAAACAbdjiL+Nwxh8AAAAAAADghhj8AQAAAAAAAG6IwR8AAAAAAADghtjjDwAAAAAAAPZhk78Mwxl/AAAAAAAAgBti8AcAAAAAAAC4IQZ/AAAAAAAAgBsyfvAXHh6u1q1b251xz0lISFC/0N6q8Vhl1XqqhqZETrI7KV1M7DaxWaLbaiZ2m9gs0W01uq1jYrNEt9VM7DaxWaLbKtk8PbRxTm8FVyrmPDai24uK3xyR5uOtl5+0sfLGTFtvycxmydxuUzjc7H+ZifFv7vHaa68x+LPByBHD9Ed0tMZPmqJjx44ptHcP5c+XX888W9futJsysdvEZoluq5nYbWKzRLfV6LaOic0S3VYzsdvEZoluK3hl89CUIW1V6tH8aY4HFc2n0M8XaNrC35zH/v3vgtV56WLSel9hYrNkbjdg/ODv/vvvtzvhnnP+/HnN+2aOxowdrxIlS6lEyVKK2bdXs2bOyNQ/9EzsNrFZottqJnab2CzRbTW6rWNis0S31UzsNrFZotsKQUUfUuSQtnJc58ScoIC8GjVlhY6fOmt92G0wab2vMLFZMrcbkAy51Dc2NlaBgYH69ttvFRwcrMqVK2vQoEFKSkq65lLfn3/+WQ0bNlTZsmX1xhtvKCwsTD179nTeP2vWLIWEhKhChQpq3bq1du/e7bwvISFBw4cP11NPPaXy5cvrrbfe0p9//pmmYdmyZapdu7bKlCmjDh066MyZM5KkqKgotWjRQiNGjFCFChVUs2ZNzZkzx/ncKSkpGjNmjGrUqKHKlSvrrbfe0rFjx5z379u3T6+//roqVKigMmXKqGXLloqJiXHeP3LkSNWoUUNly5ZV69attXfvXpevc3rt2b1LSUlJKl++gvNYhYqVtH3bViUnJ9vWdSsmdpvYLNFtNRO7TWyW6LYa3dYxsVmi22omdpvYLNFtheBKj2rNhj2q+eqnaY773e+tAnlzau+hEzaVpZ9J632Fic2Sud2AZMjg74qIiAiNGjVKERERWrZsmcLDw9Pcf+TIEXXs2FH16tXT/PnzVaZMGc2YMcN5/6pVqxQREaHQ0FDNmzdPlSpVUps2bfTPP/9Ikvr166fly5frk08+0axZs5SUlKROnTql+T/y2LFjNXLkSE2fPl3bt2/X5MmTnfdt375dO3fu1OzZs9W5c2cNGDBAP//8syRp+vTp+vbbb/Xpp59q9uzZ8vf312uvvaaLFy8qOTlZb731lgoUKKAFCxZo1qxZunTpkoYPHy5JWr58uWbPnq3Ro0dr0aJFypUrl3r16pVh63wrJ+PilCNHTnlmy+Y85u+fSwkJCc5BaGZkYreJzRLdVjOx28RmiW6r0W0dE5sluq1mYreJzRLdVhg/52d1/zRK8RcupjkeFJBXycnJ6vHGs9q3NEzrZvfUKw2r2VR5cyat9xUmNkvmdgOSYZf6duvWTZUrV5YkvfvuuxoxYoRatGjhvH/OnDkqW7asOnXq5HzMr7/+6rx/woQJ6tChg55++mlJ0nvvvac1a9Zo4cKFev7557VgwQKNHz9ejz32mCRpxIgRqlmzpn755RcFBARIkt555x2VLVtWktSwYUNt377d+fwOh0PDhg2Tv7+/ihcvrg0bNujrr79WjRo1NGHCBPXr10/Vql3+pTFw4EDVqFFDP/30kx577DE1b95cLVu21H333SdJeuGFFzRhwgRJ0tGjR+Xp6an8+fMrf/78Cg0N1f79+12/wOkUfyFe2VL9wJPkvH0xMdGOpHQxsdvEZoluq5nYbWKzRLfV6LaOic0S3VYzsdvEZoluOxUPeEgpKdKeg8f15awfFVypmMb0ba6z5+K18IdtduelYeJ6m9gsmdttkutddg/XMGrwV7FiRefnpUuX1t9//63Tp087j+3evVtlypRJ8zXly5d3ntEXExOj4cOHa+TIkc77ExISdPDgQR08eFDJyckqV66c874cOXIoICBAMTExzsFf4cKFnff7+vrq4sX/+xuiwoULy9/fP03jrFmz9N9//+mvv/7S+++/ryxZ/u8kywsXLujgwYMKCQlRixYtNH/+fEVHR2v//v36448/lCtXLklS/fr1NX36dNWqVUvly5dX7dq11bRp0ztbRBfw8vJS4lU/3K7c9vb2tiMpXUzsNrFZottqJnab2CzRbTW6rWNis0S31UzsNrFZottOM75dp8U/btfpf89LkqL3HlOxwnn0ZrPgTDf4M3G9TWyWzO0GJMMGf56ens7Pr1x+m3qQljVrVqWkpKT5mtS3L126pN69e6t69eppHuPr66u4uLjrvualS5fSXOqbuuFqHh5pl/PSpUvKkiWLLl26JEn67LPPnAPEKx544AH9999/atq0qXLmzKmQkBA1aNBA+/fv16RJl98ePHfu3FqyZIl++eUX/fDDD5o4caK+/vprzZ8/Xz4+PjfsySh58uTVmTOnlZSU5PwznzwZJ29vb/llz255T3qZ2G1is0S31UzsNrFZottqdFvHxGaJbquZ2G1is0S33a4M/a7Ytf8vPVWluE01N2biepvYLJnbDUiG7fG3c+dO5+fR0dHKkyePcuTI4TxWrFgx7dixI83XpL4dEBCgv/76S4ULF3Z+jB07Vlu2bFGhQoXk4eGhLVu2OB9/+vRpHTp06Jph3Y0cOnRI//33X5rG4sWLK3v27PL391dcXJzzdfPly6fhw4frwIEDWr9+vU6cOKGpU6fqjTfe0OOPP65jx445h5arV6/WnDlzVLNmTQ0YMEALFizQwYMHtWfPnttZPpcJDCohDw8Pbdu6xXls8++bVKp0mTSD2MzGxG4TmyW6rWZit4nNEt1Wo9s6JjZLdFvNxG4TmyW67RTasb6+G9s5zbGygQW15+Bxm4puzMT1NrFZMrcbkAwb/A0ePFjbt2/Xr7/+qs8++0yvvPJKmvtfeuklbdmyRePGjdOBAwc0duxYbdy4UY7/f7F4u3btNGXKFM2fP1+HDx/W8OHDtWTJEj3yyCO6//771axZM4WFhWndunXatWuXunXrpoceekhPPPFEuvrOnz+vfv36KSYmRl9//bWWLl2qli1bSpLatm2r0aNHa9WqVTp48KD69u2r33//XUWLFlWOHDl0/vx5rVixQrGxsZozZ45mzJjhPHU4OTlZw4YN0/LlyxUbG6uoqCj5+PioSJEirlvc2+Dj46OGjRpr0MD+it6+TatWrtDUyElq2aqNLT3pZWK3ic0S3VYzsdvEZoluq9FtHRObJbqtZmK3ic0S3XZa/ON2BVcspvda11JAwVx6s1kNvdKgqkZPXWl32jVMXG8TmyVzu03icLOPzMSRcvW1sZlQbGysatWqpa5du2rKlClKTk5WixYt1KVLF40ZM0br16/XtGnTJF1+595PPvlER48e1RNPPKGsWbMqV65cGjhwoCRp6tSpioyM1MmTJ/Xoo4+qW7duzkt/4+Pj9cknn2jJkiVKTEzU448/rr59+ypfvnzOhpUrV6pgwYKSpPDwcOdrR0VF6fPPP1e9evU0a9Ys5c6dW++//77q1asn6fJlv59//rm++eYbnTt3TqVLl1afPn1UokQJSZffsXjGjBlKSEhQYGCgmjZtqj59+ujHH39U3rx5NWnSJE2fPl1xcXEqWrSoevTooccffzxd63chyaX/OJxrNXhgf61Yvky+fr5q2+51tWrT1vUv5GImdpvYLNFtNRO7TWyW6LYa3dYxsVmi22omdpvYLNF9MzmrdL71g25D/OYI1XnjM/20aa8kqUHNMgrtWF/FHs6jQ8f+Vv8x32rBqq13/TqnN0Tc9XNczcTvExObJWu6vY3ajM219vx1/tYPMkjxh+6zO8HJqMFf6qHb9ezZs0dJSUkqWbKk81j79u1VpkwZdenSJUMbo6KiFBERoVWrVmXo69yJjBj8AQAAAIAdXD34s0pGDP7gXhj8uY/MNPgz6lLfWzl8+LDatWunX375RUePHtWcOXO0du1aPfPMM3anAQAAAAAAAJZyq3ly7dq1tXfvXvXp00enTp1SQECARo0apaCgILvTAAAAAAAAcD2ZbWM8N2LEpb64O1zqCwAAAMBdcKkv3NU9fanvcTe71Dcvl/oCAAAAAAAAyEAM/gAAAAAAAAA3dA+fSAoAAAAAAAC7OdjkL8Nwxh8AAAAAAADghhj8AQAAAAAAAG6IwR8AAAAAAADghtjjDwAAAAAAALZxsMVfhuGMPwAAAAAAAMANMfgDAAAAAAAA3BCDPwAAAAAAAMANsccfAAAAAAAAbMMWfxmHM/4AAAAAAAAAN8TgDwAAAAAAAHBDDP4AAAAAAAAAGxw/flzvvPOOqlatquDgYA0dOlQJCQkue372+AMAAAAAGOP0hgi7E+5Iziqd7U64I6auNwxzj27yl5KSonfeeUfZs2fXjBkz9M8//6h3797KkiWLevTo4ZLX4Iw/AAAAAAAAwGL79+/Xli1bNHToUBUrVkyVK1fWO++8o0WLFrnsNRj8AQAAAAAAABbLnTu3JkyYoFy5cqU5fu7cOZe9BoM/AAAAAAAAwGLZs2dXcHCw83ZycrKmT5+uxx57zGWvwR5/AAAAAAAAsI3jXt3k7yrDhw/XH3/8oblz57rsORn8AQAAAAAAADYaPny4pkyZolGjRql48eIue14GfwAAAAAAAIBNwsLCNHPmTA0fPlzPPvusS5+bwR8AAAAAAABgg4iICM2aNUsjR45U3bp1Xf78DP4AAAAAAABgG8c9usVfTEyMvvjiC7Vv316VKlVSXFyc877cuXO75DUY/AEAAAAAAAAWW7lypS5duqQvv/xSX375ZZr7du/e7ZLXcKSkpKS45JmQaV1IsrsAAAAAAO5tOat0tjvhjpzeEGF3wj3D+x4+NevAyQt2J7hUQC5vuxOcstgdAAAAAAAAAMD17uF5MgAAAAAAAOx2j27xZwnO+AMAAAAAAADcEIM/AAAAAAAAwA0x+AMAAAAAAADcEHv8AQAAAAAAwD5s8pdhOOMPAAAAAAAAcEMM/jKZU6dOacmSJXZn3FJCQoL6hfZWjccqq9ZTNTQlcpLdSeliYreJzRLdVjOx28RmiW6r0W0dE5sluq1mYreJzRLdVjOtO5unhzbO6a3gSsWcx0Z0e1HxmyPSfLz18pM2Vl6faWt9handAJf6ZjIjRoxQSkqK6tWrZ3fKTY0cMUx/REdr/KQpOnbsmEJ791D+fPn1zLN17U67KRO7TWyW6Laaid0mNkt0W41u65jYLNFtNRO7TWyW6LaaSd1e2Tw0ZUhblXo0f5rjQUXzKfTzBZq28DfnsX//u2B13i2ZtNapmdoNMPjLZFJSUuxOuKXz589r3jdzNGbseJUoWUolSpZSzL69mjVzRqb+oWdit4nNEt1WM7HbxGaJbqvRbR0TmyW6rWZit4nNEt1WM6k7qOhDihzSVo7r7IcWFJBXo6as0PFTZ60PSyeT1jo1U7tN4mCTvwxj26W+sbGxCgwM1Lfffqvg4GBVrlxZgwYNUlJSkiTphx9+0AsvvKCyZcvqueee07Jly5xf27p1a02cOFHt2rVT2bJl1bRpUx06dEihoaGqUKGC6tSpo/Xr1zsfv2fPHrVu3Vply5bVs88+qxkzZqRpWbhwoWrXrq1y5crpgw8+UNeuXRUeHu58rbCwMNWqVUs1a9bUuXPntGnTJrVo0ULlypVT+fLl9eabb+rEiROSpKioKLVu3Vqff/65qlWrpsqVK2vo0KHOgV5iYqKGDh2q4OBglSpVSiEhIZo9e7YkKTw8XPPmzdO8efMUEhIiSfr333/VrVs3VaxYUTVq1FBYWJguXLD3b2327N6lpKQklS9fwXmsQsVK2r5tq5KTk20suzkTu01slui2mondJjZLdFuNbuuY2CzRbTUTu01slui2mkndwZUe1ZoNe1Tz1U/THPe731sF8ubU3kMnbCpLH5PWOjVTuwEpE+zxFxERoVGjRikiIkLLli1TeHi41q5dqy5duqhRo0ZasGCBmjVrpvfff1/R0dHOrxszZoxeeuklRUVF6ezZs2ratKly5cqluXPnqlixYho0aJAk6cKFC3rzzTdVqVIlLVy4UD169NAXX3yh+fPnS5I2btyo3r1764033lBUVJR8fHy0ePHiNI1RUVEaPny4IiIilJKSog4dOuiJJ57QokWLNHHiRB0+fFjjxo1zPn7z5s06cOCAZs6cqdDQUE2dOlW//vqrJGncuHFavXq1wsPDtXTpUjVu3FhhYWE6efKkXnvtNdWrV0/16tXT3LlzJUl9+vTR2bNnNXPmTH3xxRfavn27Bg4cmJH/SG7pZFyccuTIKc9s2ZzH/P1zKSEhQWfOnLEv7BZM7DaxWaLbaiZ2m9gs0W01uq1jYrNEt9VM7DaxWaLbaiZ1j5/zs7p/GqX4CxfTHA8KyKvk5GT1eONZ7VsapnWze+qVhtVsqrwxk9Y6NVO7ASkTXOrbrVs3Va5cWZL07rvvasSIEdq3b5+effZZtW3bVpIUEBCgbdu2adKkSRo5cqQk6emnn3bug1e7dm0tXrxY77zzjhwOh1566SW9/fbbkqRvv/1W/v7+eu+99yRJRYoU0dGjRzV16lQ1btxYM2fO1HPPPafmzZtLkvr376+ff/45TWPNmjVVsWJFSVJcXJw6deqkdu3ayeFwqFChQqpTp462bdvmfPylS5cUFhYmX19fFS1aVJGRkdq+fbueeOIJBQUF6bHHHlP58uUlSW+99ZbGjBmjgwcPqnLlyvL29pYkPfjggzp8+LBWrFih9evXy8/PT5IUFhamxo0bq1evXs5jVou/EK9sqX7gSXLevpiYaEdSupjYbWKzRLfVTOw2sVmi22p0W8fEZoluq5nYbWKzRLfVTO1OrXjAQ0pJkfYcPK4vZ/2o4ErFNKZvc509F6+FP2y79RNYxNS1NrUbkDLB4O/KQE2SSpcurb///lv79+93DuKuqFChgr755hvn7YIFCzo/9/b2Vv78+eX4/xsdeHt76+LFy38Dsn//fu3atUsVKvzfKbmXLl1S1qxZJUm7d+/Wyy+/7LzPw8NDpUuXTvPaBQoUcH6eO3duNW7cWJGRkdq5c6f27dun3bt3p/lz+Pv7y9fX13nb19fXeQlz7dq19csvv+jjjz/W/v379ccffzibrhYTE6Pk5GQ9+WTad2JKTk7WoUOHrum0ipeXlxKv+uF25faVwWVmZGK3ic0S3VYzsdvEZoluq9FtHRObJbqtZmK3ic0S3VYztTu1Gd+u0+Ift+v0v+clSdF7j6lY4Tx6s1lwphr8mbrWpnab5Hr7VsI1bB/8eXp6Oj+/cm18QkLCNY9LTk5Oc+28h0fa9CxZrn/VclJSkqpXr66PPvrouvdnzZr1mjfUuPq2l5eX8/Pjx4/rxRdfVKlSpfT444/rpZde0urVq7V161bnY67+m4DUzzlq1CjNmTNHTZo0UePGjdWvXz/nfn5Xu3Tpkvz8/NIMPK/Imzfvdb/GCnny5NWZM6eVlJTk/Odw8mScvL295Zc9u21dt2Jit4nNEt1WM7HbxGaJbqvRbR0TmyW6rWZit4nNEt1WM7X7aleGflfs2v+XnqpS3Kaa6zN1rU3tBqRMsMffzp07nZ9HR0crT548KleuXJpBmnR537yAgIDbfv6AgAAdOHBABQsWVOHChVW4cGFt2bJF06ZNkyQ9+uij2rFjh/Pxly5dStN0teXLl+uBBx7Q//73P7366quqXLmyjhw5ku534501a5ZCQ0P14Ycf6rnnnlN8fLyk/xsMOlKNuQMCAnT27Fk5HA5n+4ULFzRs2LBr/rbBSoFBJeTh4aFtW7c4j23+fZNKlS5zwwFsZmBit4nNEt1WM7HbxGaJbqvRbR0TmyW6rWZit4nNEt1WM7U7tdCO9fXd2M5pjpUNLKg9B4/bVHR9pq61qd2AlAkGf4MHD9b27dv166+/6rPPPtMrr7yitm3b6vvvv9eUKVN08OBBRUZGavny5WrRosVtP//zzz+vCxcu6KOPPlJMTIx+/PFHDR48WP7+/pKkVq1a6bvvvtOcOXO0f/9+DRkyREePHk0zgEstR44cOnbsmNauXasjR45o3LhxWrZsWboHcTly5NAPP/ygI0eOaOPGjerevbuk/ztN2MfHR0ePHtXx48f1yCOPKDg4WB9++KG2bdumHTt2qFevXjp//ryy2/i3Cj4+PmrYqLEGDeyv6O3btGrlCk2NnKSWrdrY1pQeJnab2CzRbTUTu01slui2Gt3WMbFZottqJnab2CzRbTVTu1Nb/ON2BVcspvda11JAwVx6s1kNvdKgqkZPXWl3WhqmrrWp3YAkOVLSe6qai8XGxqpWrVrq2rWrpkyZouTkZLVo0UJdunRRlixZ9N133yk8PFyxsbEKCAhQly5dVKdOHUlS69atVbVqVXXp0kWSFB4ervXr1zvP4lu3bp3atGmj3bt3S5J27NihIUOGaNu2bcqRI4eaNm3qfB1JmjNnjsaMGaPTp0+rbt26OnTokJ5++ml16NDhmte6dOmSBg4cqMWLF8vhcKhMmTIKDg52vhvxokWLFBERoVWrVjn/rKmfY9OmTerfv78OHTqkvHnzqlmzZlq+fLlq166tDh06aOvWrXr77bd18eJF/fbbbzp9+rQGDRqk1atXy8PDQ8HBwerbt69y5syZ7rW+kHT3/7yuFh8fr8ED+2vF8mXy9fNV23avq1Wbtq5/IRczsdvEZoluq5nYbWKzRLfV6LaOic0S3VYzsdvEZoluq1nRnbNK51s/6DbEb45QnTc+00+b9kqSGtQso9CO9VXs4Tw6dOxv9R/zrRas2nqLZ7m10xsi7vo5UuN75Ma8bd+MzT5H/r52yzeTFXrQ69YPsojtg7+VK1emeaMOq23bts357rtX1K9fX6+//rqaNGliW5crZcTgDwAAAACQfq4e/FnF1YM/3BiDP/eRmQZ/tl/qa7fNmzerQ4cO+v3333XkyBGNHTtWf/75p4KDg+1OAwAAAAAAAO7YPTxPvuyVV15RbGysunTporNnz6pEiRIaP368cufObXcaAAAAAAAAcMdsu9QX1uFSXwAAAACwF5f64lbu5Ut9Y0+716W+BXNyqS8AAAAAAACADMTgDwAAAAAAAHBDDP4AAAAAAAAAN3QPX0EOAAAAAAAA+znsDnBbnPEHAAAAAAAAuCEGfwAAAAAAAIAbYvAHAAAAAAAAuCH2+AMAAAAAAIBtHGzxl2E44w8AAAAAAABwQwz+AAAAAAAAADfE4A8AAAAAAABwQ+zxBwAAAAAAANuwxV/G4Yw/AAAAAAAAwA0x+AMAAAAAAADckCMlJSXF7ghkrAtJdhcAAG7H0dPxdifckQI5fexOAAAALubffLLdCbft1Kx2difcEe97eDO2Y2cS7U5wqfw5stmd4MQZfwAAAAAAAIAbuofnyQAAAAAAALCbg3f3yDCc8QcAAAAAAAC4IQZ/AAAAAAAAgBti8AcAAAAAAAC4Ifb4AwAAAAAAgG0cYpO/jMIZfwAAAAAAAIAbYvAHAAAAAAAAuCEGfwAAAAAAAIAbYo8/AAAAAAAA2Ict/jIMZ/wBAAAAAAAAbojBHwAAAAAAAOCGGPwBAAAAAAAAbog9/gAAAAAAAGAbtvjLOJzxBwAAAAAAALghBn8AAAAAAACAG2LwBwAAAAAAALghBn82CQ8PV+vWre3OuGMJCQnqF9pbNR6rrFpP1dCUyEl2J6WLid0mNkt0W83EbhObJXO7T8Yd1+C+H+qlek+qdeNnNC58hBITEuzOuiVT19vEbhObJbqtZmK3ic0S3VajO+Nl88iiDSMbK7jUQ85jtcvl128jGunkjNb6bUQj1alQwMbCmzNprU3kcLjXR2bCm3vY5LXXXjN68DdyxDD9ER2t8ZOm6NixYwrt3UP58+XXM8/WtTvtpkzsNrFZottqJnab2CyZ2Z2S8v/au/O4GtP/f+CvoxLGmiVbUkSRlOwyRmEmhLGNUMogGTuDQlKyxFiSmSw1iCGRfcs2GGvWSiJFRTOMrCmt5/eHX+fr1Cn5DOe+77yen4fHR9c56tWZc+5zn/d9Xe9LjoVzfkbFSpWwdE0QXr9+hZWL5kGjTBn8+NNUoeMVS4qPNyDN3FLMDDC3ukkxtxQzA8ytbsz9eWlraeD3yV3QrEE1xZhh7UrYNsMG87ddxYHLSbBrq4/tM2xgPjEMSf+mCZhWNak81kQFsfAnkK+++kroCP+z9PR07N4VijUB62HSrDlMmjVH/L04bN+2VdQHPSnmlmJmgLnVTYq5pZgZkG7uh0kPEHsrElv3nUA1neoAgOE/jkPgmuWiLvxJ9fGWYm4pZgaYW92kmFuKmQHmVjfm/ryM61fB75O6QFZgGlS96l/h92N34H8gBgCw+sAtzBjQEq0b1xBd4U8qjzWRKlzqqyYPHz5E06ZNsWbNGrRp0wZeXl5KM/7OnDmD77//Hi1btkSfPn1w4cIFxW3Hjh1Dz5490bJlSwwcOBCXL18W4ldQuHsnFjk5OTA3t1CMWbSyRFTkTeTl5QmYrHhSzC3FzABzq5sUc0sxMyDd3NV0qsP7l18VRb98b96I66S6IKk+3lLMLcXMAHOrmxRzSzEzwNzqxtyfl1Wz2jhz6x90dT+gNH721j+YsfHdZ1tNDRkcrY2grVUGV+49FSJmsaTyWBOpwsKfml27dg27du3Cy5cvFWNxcXFwdXVF9+7dsXfvXvTu3Rvjxo3Dv//+i9jYWMycOROurq7Yt28f+vTpg9GjRyMxMVGw3+Hpv/+iatVq0CpbVjFWvXoNZGZm4sWLF4Ll+hAp5pZiZoC51U2KuaWYGZBu7oqVKsOyXUfF13l5edgfth3mlu0ETPVhUn28pZhbipkB5lY3KeaWYmaAudWNfLtdEwAAcs9JREFUuT+vDeF3MHPjZWRk5aq83bB2JaT+4Yjfxllh8c6bopvtB0jnsZYyWSn7n5hwqa+ajRgxAg0aNEDDhg3x5MkTAMDOnTvRqlUrjBs3DgAwZswYpKen49WrVwgMDMTgwYNhZ2cHAHB0dERERAS2bduGWbNmCfI7ZLzNQNn3DngAFF9nZ2UJEalEpJhbipkB5lY3KeaWYmZAurkLCvp1JeLvxGLlhq1CRymWVB9vKeaWYmaAudVNirmlmBlgbnVjbmE9ffUWX8/aj7ZNamHxiDaI//sV9l4SbqKLKqXlsaYvEwt/alavXuFdiu7fv4/mzZsrjU2ePBkAEB8fj8OHDyMkJERxW3Z2NqysrD5rzuJoa2sjq8DBLf/rcuXKCRGpRKSYW4qZAeZWNynmlmJmQLq53xf060rsCd2KWfOXoKFhY6HjFEuqj7cUc0sxM8Dc6ibF3FLMDDC3ujG3sF6lZ+Pm/We4ef8ZjOtXxdieJqIr/JWWx5q+TCz8qZm2tnahMU3Nov8z5ObmYvTo0ejXr5/SuJAHl1q1dPHixXPk5OQosj99+i/KlSuHSpUrC5brQ6SYW4qZAeZWNynmlmJmQLq58/22YjEO7gnFz3N9YPVNN6HjfJBUH28p5pZiZoC51U2KuaWYGWBudWNuYZjUr4pqlbRx/vZjxVjswxf4unltAVOpJvXHmr5s7PEnAvr6+oiNjVUaGzJkCA4ePAgDAwM8fPgQ+vr6ij8hISE4c+aMQGmBpsYm0NTUROTNG4qx69euorlpC5QpI96nlBRzSzEzwNzqJsXcUswMSDc3AGwNCsChPTsxy3MxunSTxu5zUn28pZhbipkB5lY3KeaWYmaAudWNuYXRs7Ue/Md2VBqzMKyO2EcvhAlUDKk/1pIgK2V/RITPUBGwt7fHlStX8PvvvyMxMRFr165FXFwcWrduDScnJxw6dAibN29GUlISNm7ciI0bN6Jhw4aC5S1fvjzs+vbDAi9PREdF4uSJ49i8MQhDhzsKlqkkpJhbipkB5lY3KeaWYmZAurmTHiRg26b1GDTcGc3MLPAs9anij5hJ9fGWYm4pZgaYW92kmFuKmQHmVjfmFsa2M/GoXbUCvIe3RqPalTHmO2MM+boRfgmLEjpaIVJ/rOnLJpPL5XKhQ3wJHj58CBsbG5w4cQL169fH6tWrcfnyZQQHBwMATp06hV9++QWJiYkwMjKCm5sb2rRpAwA4ePAgVq9ejYcPH6JBgwaYMGECbG1tS/yz3+Z8+t8nIyMDPl6eOH4sHBUrVYST848Y7uj06X/QJybF3FLMDDC3ukkxtxQzA+rJ/eh5xif9fjuCg7BxrZ/K2w79deOT/Zx61cp/su+Vj88T9ZFiZoC51U2KuaWYGWBudWPuolUf8vsn+15vdjrju3mHcfbWPwCANkY14evcFqb6Okh8kgaPrVdw6Eryf/45qdud//P3KEgdj3W5L7gZ279pn6FwIaCaFcXzH5OFvy/A5yj8ERHR5/OpC3/q8jkKf0RERCSsT1n4U5fPUfhTBxb+Sg8xFf7Ek4SIiIiIiIiIiL44ImuLV6qwxx8REREREREREVEpxMIfERERERERERFRKcTCHxERERERERERUSnEHn9ERERERERERCQYGZv8fTac8UdERERERERERFQKsfBHRERERERERERUCrHwR0REREREREREVAqxxx8REREREREREQlGBjb5+1w444+IiIiIiIiIiKgUYuGPiIiIiIiIiIioFGLhj4iIiIiIiIiIqBRijz8iIiIiIiIiIhKMjC3+PhvO+CMiIiIiIiIiIiqFWPgjIiIiIiIiIiIqhVj4IyIiIiIiIiIiKoVY+CMiIiIiIiIiIiqFWPgjIiIiIiIiIiIqhWRyuVwudAj6vN7mCJ2AiIiIiIiISD2qtRkvdIT/ScZ1f6EjCOZ5eq7QET6pahU0hI6gwBl/REREREREREREpZCm0AGIiIiIiIiIiOjLJZMJnaD04ow/IiIiIiIiIiKiUoiFPyIiIiIiIiIiolKIhT8iIiIiIiIiIqJSiD3+iIiIiIiIiIhIMDKwyd/nwhl/REREREREREREpRALf0RERERERERERKUQC39ERERERERERESlEHv8ERERERERERGRYGRs8ffZcMYfERERERERERFRKcTCHxERERERERERUSnEwh8REREREREREVEpxB5/REREREREREQkGLb4+3w444+IiIiIiIiIiKgUYuGPiIiIiIiIiIioFPpiCn+HDx9Gamqq0DEAANbW1ggLCxM6xn+SmZmJeXPdYdW+NWy6WGHTxiChI5WIFHNLMTPA3OomxdxSzAwwt7oxt/pIMTPA3OomxdxSzAwwt7oxt/pILXNZLU1cCXVHZ0sjxdiynwcg47q/0p+xP3wtYEqion0RPf4ePXqEyZMn48SJE0JHKTWWL/NFTHQ01gdtQkpKCua6z0TdOnXR/dvvhI5WLCnmlmJmgLnVTYq5pZgZYG51Y271kWJmgLnVTYq5pZgZYG51Y271kVJm7bKa2LTQCc0b11UaNzasg7l+exG876Ji7NWbt+qOV7qwyd9n80UU/uRyudARSpX09HTs3hWKNQHrYdKsOUyaNUf8vThs37ZVlAfrfFLMLcXMAHOrmxRzSzEzwNzqxtzqI8XMAHOrmxRzSzEzwNzqxtzqI6XMxoa1sXGhE2QqClLGBrpYsek4Hqe+Vn8woo8k+qW+ffr0wZYtWxRfOzs7Y/jw4YqvQ0JCYG9vj6tXr8Le3h4tW7aEubk5Ro8ejSdPngAAbGxsFP+fv8T22LFj6NmzJ1q2bImBAwfi8uXLiu/p4OAAb29v2NjY4JtvvsGdO3fQtGlT/Pnnn7C2toaFhQUWLFiAu3fvon///jA3N4eLiwvS0tIU32P79u2K+zo4OODOnTsqf7+8vDxs2LABNjY2MDMzK3Tfpk2bYu/evejduzdMTU0xdOhQJCcnf4JH9n93904scnJyYG5uoRizaGWJqMibyMvLEzBZ8aSYW4qZAeZWNynmlmJmgLnVjbnVR4qZAeZWNynmlmJmgLnVjbnVR0qZO1s2xpmIu/hmxC9K45W+Kod6utUQl/hEoGREH0f0hT8rKytFUS47Oxs3btxAVFQUsrOzAQDnzp1Dp06d4OLigk6dOuHAgQMIDAxEUlIS1q1bBwAIDQ1V/H/Pnj0RGxuLmTNnwtXVFfv27UOfPn0wevRoJCYmKn5uWFgYli5dCn9/f3z11VcAgHXr1uHXX3+Ft7c3goODMX78eEybNg2BgYG4ceMGdu7cCQA4efIk/P39MXfuXOzevRuWlpZwdHTEy5cvC/1+a9asQVBQENzd3bF7927Uq1cPo0aNQnp6uuI+q1evxuzZsxEWFobnz59j5cqVn/6B/ghP//0XVatWg1bZsoqx6tVrIDMzEy9evBAu2AdIMbcUMwPMrW5SzC3FzABzqxtzq48UMwPMrW5SzC3FzABzqxtzq4+UMq8P/QszfglDxttspXFjA13k5eVh5qhvce+INy6FzMIwu3YCpST6MEkU/iIiIiCXy3Hr1i00aNAAlStXRkxMDPLy8nDp0iW0b98e48aNw08//QQ9PT1YWlqiR48eiIuLAwDo6Ogo/r9cuXIIDAzE4MGDYWdnB319fTg6OuLrr7/Gtm3bFD/3m2++QatWrWBqaqoYGzduHIyNjdG7d29Ur14dvXr1QqdOnWBpaYkOHTogISEBALBhwwa4uLiga9euaNiwISZPnox69eph3759Sr+bXC7Hli1bMGnSJNjY2KBRo0bw9vaGhoaG0n2dnZ3RoUMHNGnSBPb29oiOjv5sj3dJZLzNQNn3DtQAFF9nZ2UJEalEpJhbipkB5lY3KeaWYmaAudWNudVHipkB5lY3KeaWYmaAudWNudVHipkLamJQG3I5cPfBY/Sb8Bs27r6ANXOGoE9XM6GjEakk+h5/rVu3RkZGBuLi4hAREYHWrVvjyZMnuHr1KjQ0NFCmTBlYWlrC0NAQGzduxO3bt3Hv3j3cuXMHrVq1Uvk94+PjcfjwYYSEhCjGsrOzYWVlpfi6Xr16hf6dnp6e4u/lypVTuk+5cuWQ9f8PVPHx8Vi6dCmWL1+uuD0zMxMPHjxQ+n6pqal48eIFWrZsqRjT0tKCqakp4uPjFWP6+vqKv1esWFEx21Eo2trait81X/7X5cqVEyJSiUgxtxQzA8ytblLMLcXMAHOrG3OrjxQzA8ytblLMLcXMAHOrG3OrjxQzF7R1/yUcOh2F56/erdKLjkuBkX4tjB7UGftORQqcTrpk3N3jsxF94a9s2bJo3bo1Ll++jCtXrqBv37548uQJrly5gtzcXHTq1AlPnjzBgAED0Lx5c3Ts2BGDBw/Gn3/+iZs3b6r8nrm5uRg9ejT69eunNP7+gUZbW7vQv9PQ0FD6ukwZ1RMmc3Nz4e7ujg4dOiiNV6xYUelrVT8j/9+/399AS0tL5f2EUquWLl68eI6cnBxoar57Cj19+i/KlSuHSpUrC5yuaFLMLcXMAHOrmxRzSzEzwNzqxtzqI8XMAHOrmxRzSzEzwNzqxtzqI8XMquQX/fLFJvyDLm2aCJSGqHiiX+oL/F+fvxs3bsDS0hKWlpa4du0a/vrrL3Tu3BnHjh1DlSpVsHbtWowYMQKtW7dGcnKyYjdfWYFteAwMDPDw4UPo6+sr/oSEhODMmTOfJK+BgQH++ecfpe8fEBCAGzduKN2vUqVKqFGjhtJ4dnY2bt26BQMDg0+S5XNoamwCTU1NRN68oRi7fu0qmpu2KLIYKgZSzC3FzABzq5sUc0sxM8Dc6sbc6iPFzABzq5sUc0sxM8Dc6sbc6iPFzAXNde2FgwHjlcbMmtbH3QePBUpEVDxJvLKsrKxw8uRJVKxYEbq6umjWrBkyMjIQERGBzp07o2rVqkhJScGFCxeQnJyMdevWITw8XDFluHz58gCA2NhYvHnzBk5OTjh06BA2b96MpKQkbNy4ERs3bkTDhg0/SV5nZ2ds2rQJe/bsQVJSEpYuXYrDhw+jUaNGhe7r5OQEPz8/nDx5EvHx8Zg7dy4yMzPRs2fPT5Llcyhfvjzs+vbDAi9PREdF4uSJ49i8MQhDhzsKHa1YUswtxcwAc6ubFHNLMTPA3OrG3OojxcwAc6ubFHNLMTPA3OrG3OojxcwFHTodhc6tjDDZwQYG9Wtg9CArDOvdFis3nxA6GpFKMnn+tDiR69KlC9q1awdfX18AwMiRI/HixQuEhYUhNzcXXl5eOHToEGQyGVq0aIHOnTtj9erVuHDhAsqWLYuff/4Zhw8fxvTp0+Hk5ISDBw9i9erVePjwIRo0aIAJEybA1tYWAODg4IC2bdtiwoQJAICHDx/CxsYGJ06cQP369QEA1tbWGD9+PPr37w8AmDVrFgBg8eLFAIDNmzdj48aNePr0KRo3boyff/5ZsfT3/X+bm5sLPz8/7Ny5E2lpabCwsIC7uzuaNHk3Tbhp06bYvHkz2rV7t0tQWFgY/P39cfLkyRI/dm9z/tNDr1JGRgZ8vDxx/Fg4KlaqCCfnHzHc0enT/6BPTIq5pZgZYG51k2JuKWYGmFvdmFt9pJgZYG51k2JuKWYGmFvdmFt91JG5WpvxH77TR8i47o8eo1bh7NV3G4j2/qYF5rr2glGDWkhMeQbPNfux96TqVmMf+3O+VG+yJFGaKrGvyoqnZ6FkCn/0v/schT8iIiIiIiIiMfrUhT91YeGv9BBT4U8SS32JiIiIiIiIiIjo47DwR0REREREREREVAppCh2AiIiIiIiIiIi+XOJZGFv6cMYfERERERERERFRKcTCHxERERERERERUSnEwh8REREREREREVEpxB5/REREREREREQkHDb5+2w444+IiIiIiIiIiKgUYuGPiIiIiIiIiIioFGLhj4iIiIiIiIiIqBRijz8iIiIiIiIiIhKMjE3+PhvO+CMiIiIiIiIiIiqFWPgjIiIiIiIiIiIqhVj4IyIiIiIiIiIiEkBmZibc3d3RunVrWFlZISgo6JN+f/b4IyIiIiIiIiIiwci+4BZ/vr6+iI6OxqZNm5CSkoKZM2eibt26+O677z7J92fhj4iIiIiIiIiISM3S09MRGhqK9evXo3nz5mjevDni4uKwdevWT1b441JfIiIiIiIiIiIiNYuNjUVOTg4sLCwUY5aWlrh58yby8vI+yc9g4Y+IiIiIiIiIiEjN/v33X1SrVg1ly5ZVjNWoUQOZmZl48eLFJ/kZXOr7BSjH/8pERERERET0hci47i90BPpIX2rdIiMjQ6noB0DxdVZW1if5GZzxR0REREREREREpGba2tqFCnz5X5crV+6T/AwW/oiIiIiIiIiIiNRMV1cXz58/R05OjmLs33//Rbly5VC5cuVP8jNY+CMiIiIiIiIiIlIzExMTaGpq4saNG4qxq1evokWLFihT5tOU7Fj4IyIiIiIiIiIiUrPy5cujX79+8PT0RGRkJI4fP46goCA4Ojp+sp8hk8vl8k/23YiIiIiIiIiIiKhEMjIy4OnpifDwcFSsWBE//vgjnJycPtn3Z+GPiIiIiIiIiIioFOJSXyIiIiIiIiIiolKIhT8iIiIiIiIiIqJSiIU/IiIiIiIiIiKiUoiFPyIiIiIiIiIiolKIhT8iIiIiIiIiIqJSiIU/+p+kpaUhJiYGWVlZSEtLEzoO0X+2Z88eZGVlFRpPT0/H5s2bBUhUcnw9UnGuXr2KiRMnom/fvvj777+xbt06HDx4UOhYRPT/ZWZmIjIyEq9fvxY6ChFBeq9JngcS0YdoCh2ApCUzMxPe3t4ICwsDABw9ehRLlixBRkYGli9fjipVqgicUDW5XI4TJ04gLi4Oubm5ivGsrCzExMRgw4YNAqYr3tOnT1UWpOrWrStAmg/LyMhASEgI7t27p/KxPnz4sIDplD179gxv374FALi5ucHIyAjVqlVTus/t27exbNkyODo6ChGxWFJ9PT569AgrV65EVFQUcnJyIJfLlW4/ceKEQMmKl5OTg9DQUHTp0gV169bFqlWrEB4ejmbNmmH27NmoWrWq0BELCQ8Ph5ubGwYPHow///wTOTk50NTUxKxZs/Dy5UsMHTpU6IjFio+PR61atVCpUiWcPXsWJ0+eRLNmzTBo0CChoylxcHCATCYr0X3FfCEhLi4ODx48QKdOnZCamor69euX+PcSyvHjx7FhwwYkJCQgNzcXBgYGGD58OPr16yd0tCLdu3cP7u7umDVrFho3bowffvgB9+/fR/ny5fHbb7+hffv2QkdU6dWrVwgKCiry2C3W57ZU33OSk5Pxxx9/IDExEZ6enjhz5gwaNmyI1q1bCx2tSFI935bqa1Kq54FSe25L9RhC9D4W/uijLF26FPfu3cPu3bsxZMgQAMCECRPg5uaGBQsWYOnSpQInVM3b2xs7d+5Es2bNEBkZCQsLCyQlJeHp06ewt7cXOp5KR44cwbx58/Dq1SulcblcDplMhtu3bwuUrHhz5szBhQsX0KFDBxw5cgS2trZITExEdHQ0fvrpJ6HjKbl8+TImT56s+GA7cOBApdvz39j79Omj9mwlIdXX44wZM/D8+XMMGzYMFStWFDpOiS1evBhHjx6FhYUFYmJisH79ekycOBFnzpyBt7c3fvnlF6EjFuLv7w9PT0/Y2dlh+/btAICRI0eiZs2a8PPzE3XhLyQkBF5eXvj9999RsWJFuLq6on379jh27BhSUlIwadIkoSMqtGvXTugI/8nLly8xadIkXL58GcC7D48+Pj5ITk7GunXrUK9ePYETqrZ9+3YsWbIEw4cPx5gxY5CXl4dr165h/vz5yM7OFl2BON/8+fOhp6cHAwMD7Ny5E69fv8Zff/2FXbt2YcmSJdi9e7fQEVWaMWMGoqKiYGdnJ6ljtxTfcyIiIjBmzBh07twZZ8+eRWZmJhISEuDp6Ynly5ejR48eQkdUSYrn24B0X5NSPA+U4nNbiscQokLkRB+hc+fO8sjISLlcLpebm5vLk5KS5HK5XB4dHS1v27atkNGK1a5dO/nRo0flcrlc/u2338rv3r0rz87Olk+bNk0+Z84cgdOp9vXXX8s9PT3l9+7dkz98+LDQH7Fq3bq1/Ny5c3K5XC63s7OTR0VFyeVyuXzRokXyiRMnChlNpUePHsmTk5PlTZs2lUdGRio9xo8ePZI/e/ZM6IhFkurr0dTUVB4XFyd0jI/WoUMH+fXr1+VyuVw+depUuYuLi1wul8vv3r0rb9WqlYDJimZmZqZ4Xrz/HHnw4IG8RYsWQkb7oG7dusn3798vl8vlci8vL/nAgQPlcrlcfvnyZXmnTp2EjFbqTJ8+XT527Fj5y5cvFc+T1NRU+ciRI+Vjx44VOl6RbGxs5Lt37y40HhYWJu/Ro4f6A5XQ+69Le3t7uaenp1wul8sfPnwoNzMzEzJasVq0aCG/efOm0DE+mhTfcwYNGiQPDg6Wy+XKx+7ff/9d3qtXLyGjFUuK59tyuXRfk1I8D5Tic1uKxxCigjjjjz7KmzdvUL58+ULjeXl5SlP6xSYtLQ2mpqYAgCZNmiAyMhJGRkZwcXHBjz/+KHA61dLT0+Ho6AgDAwOho3yUzMxMNGzYEABgZGSE6OhomJqa4ocffsDw4cOFDadC/pLp2NhYgZN8PKm+Hhs2bIhnz54JHeOjZWRkoHr16sjJycGZM2cwffp0AO8eb01Ncb6dNm7cGGfPni00s2/37t1o3LixQKlK5vHjx7C0tAQAnDp1Cj/88AMAoHbt2njz5o2Q0Qpxc3PD7NmzUbFiRbi5uRV730WLFqkpVcmdPXsWwcHBqFy5smJMR0cHbm5uilkkYpSamgpzc/NC4xYWFvj777/VH6iEKlWqhKdPn0JTUxM3btyAi4sLgHetJapXry5wuqLp6uqiTBnptQeX4nvO3bt30aVLl0LjNjY2WL58uQCJSkaK59uAdF+TUjwPlOJzW4rHEKKCxPlJhUTL2toaK1aswJIlSxRjycnJWLBggcqDuFjo6ekhJiYGdevWhZGRESIjIzFgwADI5XLRNu4dOnQofv/9d8yZMwdly5YVOk6JNWrUCOfPn8fAgQNhZGSEq1evYsiQIXj9+jUyMzOFjlekD/XwE2PvIqm+HkePHo05c+bA2dkZ+vr60NLSUrq9TZs2AiUrXqtWrbB06VJUrFgRGRkZ6NatG2JjY+Ht7S3a/j9ubm4YO3YsLl68iOzsbAQEBCiW3v/2229CxyuWoaEh9u/fDx0dHaSkpKBbt27Izs5GUFAQjI2NhY5X6qg6Pj979ky0RW0AMDExwZ49ezB58mSlcbEXtvv37w9XV1eULVsW9evXh5WVFbZt2wZfX19RLWEvaMaMGfD09MTEiRNVHrvF2ntYiu859erVQ1RUFPT09JTG//zzT9EuvQekeb4NSPc1KcXzQCk+t6V4DCEqSCaXF+hOSVSM169fw93dHSdOnEBeXh4qV66M169fw8rKCkuXLhVlc3sACA0NxcKFC+Hj44OmTZuif//+GDhwIK5fvw4dHR1RNhu+ffs2RowYgbdv36JGjRqFGqyLtZHsiRMnMGnSJHh4eKBz587o1asX2rZtizt37sDc3BwrVqwQOqJK/v7+Sl/n5OQgOTkZp0+fhqurqyivVEv19Vhc0UbM/Sv//vtveHl5ISUlBaNGjYKdnR1WrFiB+/fvw9PTEzo6OkJHVOnp06fYunUr4uPjFZsfDB06VLQf0vNduHABkydPVmxC4uHhAS8vL4SHhyMgIEAxq0RsIiIiYG5uXuiDQVZWFs6cOYNu3boJlKxoCxYswK1bt+Dl5YUffvgB27Ztw/PnzzFv3jx06tQJHh4eQkdU6fr163ByckKzZs3QsmVLAMCNGzcQGxuLgIAA0RbkAeDYsWN49OgRevfujRo1auD06dPIy8tD165dhY5WpPeP3e+fk8hF3ntYiu85x44dw6xZszB48GBs3boVo0ePxsOHD3Hw4EH4+vqiZ8+eQkdUKTQ0FD4+Pli4cKFkzrfzSfE1Wdx5oK+vb6EN68RAis9tKR5DiApi4Y/+J8nJyYiPj0dOTg4MDAzQqFEjoSN9UEREBCpUqIDmzZvj7NmzCA0NRdWqVTFhwgTUrFlT6HiF9O7dG1WqVEHv3r1Rrly5Qrd///33AqQqmeTkZOTl5UFfXx+xsbHYu3cvqlWrBgcHB5VLEsQsLCxMUWgQKym+Hkk9/P398eOPPxZ63aWlpcHf3x+zZs0SKFnJ5OXl4fXr14qdCZ8+fYoqVaoUKqqJiYmJCc6dO1eoEBwTE4MhQ4YgMjJSoGRFy8rKwvLly7F161ZkZ2dDJpNBQ0MDAwcOxKxZs1S+B4lFfHw8duzYgYSEBGhraysK23Xq1BE6Wqnz6NGjYm8X62wdqYqNjUVQUJDSRRsnJydFkVuspHa+/SFPnjxBrVq1hI5RrKSkJCQkJEjmPFCqz20iKWPhjz4oJSWlxPcV+wwSKTE3N8f+/fsLTYWXirS0NCQlJaFx48bIysqS7C5YycnJ6N27N27evCl0FJVyc3Nx9uxZPHjwAP3798f9+/dhaGiISpUqCR2tWG/fvsW+ffsUJ32Ghobo2bOnaGcp5rt69So2bdqExMREBAQEYP/+/ahXrx569eoldDSFhIQEpKamAni3hH316tWKwlm+u3fvYunSpbhx44YACUsuLS0N9+7dQ05ODgqerohpac0ff/wBLy8vyGQyxewnVTp27IjAwEA1pyu5t2/fIjk5Gbm5udDT08NXX30ldKRS6dGjR1i5ciWioqJUPrfFOqO/KFlZWbh9+7aoP7RL9T1Hanbt2oUePXqI/hykoISEBCxbtgz37t1T9MaTy+XIysrCs2fPEBMTI3BC1WxsbLBr165Cz+PHjx+jX79+uHDhgjDBPiA1NRWvXr1S9DE/dOgQ2rRpI+rCMI8hJHUs/NEHGRsbF1rS8b73P+iIdarzq1evEBQUVORJthj7t02ZMgXt27dXNLSXiszMTHh7eyMsLAwAcPToUSxZsgQZGRlYvnx5oQKEWKgqcL958waBgYGIjIzEoUOHBEhVvL///hsjR47Ey5cv8fLlSxw5cgS+vr64fv06AgMD0bRpU6EjqnT37l2MGjUKGhoaMDU1RW5uLm7duoWsrCwEBweLtjdXeHg43NzcFMtTDh48iGPHjmHFihVwc3MrtIGGUC5evAgnJ6di71O+fHk4OjpiypQp6gn1P9i7dy88PT2RkZFR6DYxvt9EREQgLy8PI0aMKFRslclkKF++PJo0aSLKnq0jRozAyJEjC/WEevr0KTp37iy6xzqftbW1yiKrTCaDlpYWatasCVtbW9jb2wuQrmjDhg3D8+fPYW9vr/KimFhn9F+7dg3z58/HvXv3kJeXp3SbhoYGoqOjBUpWPCm+5zg4OJTouf31118LkK5odnZ2SExMRKdOndCrVy/Y2NhIYqXHsGHDkJubi++//x4LFy7EjBkz8OjRI/zxxx+YN2+eqF6TR44cwenTpwG862fas2dPaGtrK93n0aNHSEhIwF9//SVExGJduHABP/30E5ycnDBx4kQAwJAhQxAfH4+AgADFpl5iIsVjCFFBLPzRB31oacf7xLrMY+zYsYiKioKdnZ3Kk+zx48cLkKp4y5Ytw+bNm9G8eXPo6elBQ0ND6XYx7gwJvOsVFR0djfnz52PIkCHYt28f0tPT4ebmhkaNGmHp0qVCR1SpYIEbeFfkrlOnDhYuXIgOHToIlKxorq6uqFGjBjw9PdG6dWvs27cPtWvXxuzZs/H3338jODhY6IgqjRgxAnXr1oW3t7di44CcnBzMmTMHT548QVBQkMAJVevTpw9Gjx4NOzs7WFhYYN++fdDT08P+/fvh5+eHY8eOCR2xEGtra+zcuVO0/QeL880336BHjx6YOHGipGYMP3r0CHXr1i1y1p8YGRsbo2zZshgxYgSmTJmi2Ln16dOnsLKyEu2u50FBQfD398fw4cNhbm4OuVyO6OhoBAcHY8CAAahVqxY2bdoEBwcHjB49Wui4Ci1atBD9BiSq9O/fH7Vr14a9vT0mTZoEX19fPH78GP7+/pg7d64oe3MB0nzP8fPzw7p16/Dtt98qPbcPHTqEPn36oFy5cti3bx/c3NwwYMAAoeMqiY+Px+HDh3HkyBE8evQIXbp0Qa9evdClSxdRXvgAADMzM4SEhMDExAT29vaYOHEiOnTogNDQUOzZswdbt24VOqLCs2fPFOfSu3fvhq2tbaF2DBUqVEDfvn1hZmYmRMRi9evXDz179sSYMWOUxteuXYvw8HDs2rVLoGRFk+IxhKgg8W7VRqJRsJiXnZ2N8+fPIz4+HmXKlEHTpk3Rrl07xQcFMTp//jy2bNkiyjfAoqSmpopq+WBJhYeHY82aNUqzzZo2bQpvb2+MHDlSwGTFK7i0Kv+quqqNVcTiypUr2LFjh1JRWEtLC+PGjRPV1emCbty4gXnz5intFqqpqYnRo0dj4MCBAiYrXmJiIszNzQuNm5mZ4fHjx+oPVAInT54UOsL/7MWLF3B0dJRU0Q8AatWqhZ07dxY5w1ysF23Wrl2L+fPn48aNG1ixYgVq1KgBAKI9/gHAnj174O3trfReaWNjg6ZNmyIgIAB79uyBiYkJ5syZI6rCX8OGDfHs2TOhY3y0uLg4LF26FI0aNULz5s2hpaWFYcOGoXr16li/fr1oC39SfM+5cOEC3NzcMGzYMKXx1q1bY+/evdi6dSvat2+P5cuXi67w16hRI4wfPx7jx49HfHw89u7di59//hmampro3r07Bg0ahFatWgkdU4mmpqZiebKhoSFu376NDh06oGPHjko75oqBjo6O4n2kXr16GDlyJCpUqCBwqpJ78OABvvvuu0Ljtra2+PXXXwVI9GFSPIYQFcTCH32UhIQEjBkzBs+ePUPDhg2Rl5eHxMRE1K9fH+vXr0ft2rWFjqiSrq6uqAuTqoj1w+GHvHnzRuWyjry8PEXfFDES62zV4pQrVw6pqamKHin57t+/L+piSc2aNZGUlARDQ0Ol8aSkJFH3FGvcuDHOnj1baEmvmGfuFLUUMp+Ye4l17doV4eHhor5goMrs2bMRHh6Ozp07i/p1WFCTJk2wc+dOuLm5oW/fvli2bJlo2wXkS0pKUrnbopGRERISEgC8K7Ll97wUi9GjR2POnDlwdnaGvr5+oc1qxNS/8n3ly5dXXGgyNDTEnTt30KVLF5iZmeH+/fsCpyuaFN9zYmJiVJ4Htm3bFgsWLAAAmJqa4u+//1Z3tBJ5/Pgxjh49ivDwcNy4cQNmZmbo2bMn/v33X7i6umLw4MGYNm2a0DEVLCwsEBgYiJkzZ8LU1BQHDx6Es7MzoqOjCy2jFZPx48fj2bNnuH37tmL5fX5vwpiYmEKz6sTA0NAQhw8fhouLi9L4yZMn0aBBA4FSFU+KxxCiglj4o4/i4eEBMzMzeHt7Kw50r1+/xuzZs+Hh4YF169YJnFC1GTNmwNPTExMnTlR5ki3GTUnkcjlOnDiBuLg4pYJZ/pv5hg0bBExXNGtra6xYsULpCmlycjIWLFhQqH+UmMTExGDBggWKWToFibHH1ZAhQ+Dh4YEZM2YAeFfwu3z5MlasWIFBgwYJnK5oQ4YMwZw5czBp0iTFLNybN2/Cz89P1Lnd3NwwduxYXLx4EdnZ2QgICEBiYiKio6Px22+/CR1PpQkTJih9nZOTg+TkZISFhWHSpEkCpSoZXV1drFixAocPH1Z53BbrxZFjx45hzZo16NSpk9BRSiy/OFyxYkWsXr0aGzZsgIuLC5ydnQVOVjxzc3OsXr0aCxcuVMx4SU9Px5o1axTHltOnT0NfX1/ImIXkH7Pnz59f6DYx9q/M1759e/zyyy+YM2cOLCwssHHjRgwePBgnT55E5cqVhY5XJCm+5xgbG2PLli2YPXu20sWbP/74Q3GhKSoqSnQX3Ddu3IijR4/i5s2baNKkCXr16oWlS5cq7bLdsGFDeHl5iarw5+bmBldXV+jp6WHIkCHYvHkz2rZti/T0dIwbN07oeEXasWMHvLy8kJOTo+i5Drw7jpiZmYmy8Dd58mSMGzcO586dQ/PmzQEAd+7cwZUrV7B69WqB06kmxWMIUUHs8UcfxdzcHGFhYYWueMTHx2PgwIG4fv26QMmK9/6MgIIblYj1JNvLyws7d+5Es2bNEBkZCQsLCyQlJeHp06ewt7eHh4eH0BFVev36Ndzd3XHixAnk5eWhcuXKeP36NaysrLB06VLR7n7Vr18/VKpUCc7Ozipn6bRt21aAVB8WHByMwMBA/PPPPwCA6tWrw8nJCT/++KNoZ7nK5XL4+/tjy5YtePnyJQCgRo0acHJywsiRI0WbGwD+/fdf/PHHH4pd3QwMDDB06FBRXjwozokTJxAUFCSqvkUFubm5FXu7WAt/X3/9NYKCgkQ7C1QVY2NjnDt3DtWrV1eMXbp0CdOmTUNqaqoo3yOBdxeVxo4di3/++QcNGzaEXC5HYmIi6tSpg9WrV+PRo0dwdXXFqlWrYG1tLXRcyXv8+DF+/vlndO/eHUOGDIGzszOuXLkCDQ0NeHp6ivYDsBTfc6KjozF69Gh89dVXaNasGYB3FyDT0tIQEBCAnJwcODs7w8PDQ1RLDb/99lv06tULvXr1QqNGjVTe586dO4iOjhbdEmW5XI63b9+ifPnySE9Px+XLl1G1alWVLT7EwtraGv3798eYMWNgbW2N0NBQvHnzBjNmzEDPnj0xatQooSOqFBcXh127duH+/fvQ1NSEvr4+7O3toaenJ3Q0laR4DCEqiIU/+ihDhw6Fra0tHBwclMb37NmDkJAQbNu2TaBkxfvQBiViXObZvn17eHl5oUePHvjuu++wevVqGBgYYNasWShfvjy8vb2Fjlis5ORkxMfHIycnBwYGBkWeAIqFmZkZ9u/fL7qZIQW5ublhxowZqFatGiIiImBubg4tLS2kp6cjNzdX0aNGzFJSUlC7dm2UKVMGqamp0NbWRsWKFZGbm4vY2FjFFWCxSktLw4MHD1CmTBkYGBhIYsfCghITE2FnZ4fIyEiho5Q627ZtQ3h4OObMmQN9fX2lnkBiVdSGJE+ePMG5c+dE3TM0NzcXFy5cwN27d6GhoQEjIyN06NABMplM0UdPjJvb5OTkIDU1VTGjP3953u3bt0XbK68guVyOe/fuoXLlytDV1RU6Tom8/54jdmlpaTh48KDSc7tXr16oUKECHj58iLS0NJVL3cUqOzu70MxtMcnNzcXZs2fx4MED9O/fH/fv34ehoaGoz6tMTU1x5MgR1K9fHy4uLujXrx9sbW1x5coVzJ49G0ePHhU6YqkjpWMI0fvEfzZKotKxY0csW7YMly9fRqtWraCpqYnbt2/jwIEDsLOzg7+/v+K+Ytopt6jCXv5JthgLf2lpaTA1NQXwrvdSZGQkjIyM4OLigh9//FHgdMV78eIFzpw5g4SEBMhkMrx48QK6urqifpM0MTFBfHy86At/Bw8ehIuLC6pVqwZHR0ecO3cOOjo6kmrsbGNjo8j9/gyjhw8fYujQobh586aA6YqWnp6OefPm4fDhw4oP61paWvj+++8xZ84cUX6giYiIKDT25s0bBAcHw8jISIBEJSfVdgfr16/HkydP0Lt3b5W3i2X23J49e9CzZ0+ULVtW5fMkn5g39wAADQ0NWFlZwcrKqtBtYiz4AcDx48cxd+5cvHjxotBtNWvWFHXh7/Xr19i3bx/u37+PcePG4eHDh6K8sLdnz54S37dfv36fLcd/UbFiRfzwww8qb6tfv76a05TM06dPsXbtWty7d0+pqJ2dnY34+PhijzVC+vvvvzFy5Ei8fPkSL1++hI2NDTZs2IDr168jMDBQtP1OdXR08OzZM9SvX1+xKYmtrS10dXVFtemYo6Mj/P39UblyZTg4OBT7vrJ582Y1JitaaTiGEL2PhT/6KJcuXYKZmRlevHihtFtky5YtkZSUhKSkJADi+6Bw7do1zJ8/H/fu3VM0v82noaGB6OhogZIVTU9PDzExMahbty6MjIwQGRmJAQMGQC6X4/Xr10LHK9L169cxZswYVKlSBc2aNUNubi5OnjyJFStW4Pfff0eTJk2EjqhS3759MWfOHPTv319lPzGxvKmbmZnB0dER+vr6kMvl+Omnn4osOInl5AkAQkNDERAQAODdh4ABAwYUWhrx6tUrUX6AzOfh4YE7d+4gMDAQpqamyMvLQ1RUFHx8fLBo0SJRLr8vODsbeFesbNGihaJBvFh5e3sX2+5ArBYvXix0hBLx8/NDly5dULZsWfj5+RV5P5lMJprjX0FS7M0KAL/88gu6d+8OJycn2NvbY926dXjx4gW8vb1F3U/s7t27GDFiBOrUqaP4e3h4OI4cOYK1a9eKqiVGcc/p94n1+f3o0SOsXLmyyN3Bxboxk7u7O5KSktCjRw8EBQXB2dkZSUlJOHbsGGbNmiV0vCJ5eXmhdevW8PT0ROvWrQEAy5cvx+zZs7FgwQIEBwcLnFA1W1tbzJw5Ez4+PujcuTNmzJiB5s2b49SpU6K6kN22bVvFuWqTJk3w8uVLNGzYUNhQHyD1YwhRQVzqS1+E/v37o3bt2rC3t8ekSZPg6+uLx48fw9/fH3PnzhXl1fXQ0FD4+Phg4cKFaNq0Kfr376/oo6ijoyPa2S79+/dH69at4ebmpigA5+XlYcGCBYiNjcUff/whcELViuv/JJPJRHOS/fz5c+zbtw+vX7+Gv78/nJ2di9xRTEyzbrOzs3Hw4EHk5eXB3d0d7u7uSstnZDIZypcvj/bt26NKlSoCJi1aq1atEBwcXGgp8s2bNzF69GhcvnxZoGTKUlJSUKdOHchksiLbHOS/NrW0tFC1alVRzlaUeruDtLQ0JCUloXHjxsjKyhL1jGepkmpvVlNTUxw6dAgNGjTAjz/+CHt7e3Tr1g1nz56Fr68v9u/fL3RElRwdHdG6dWtMnDgRFhYW2LdvH/T09LB06VJcunQJO3fuFDpiqTFs2DA8f/4c9vb2Kp/bYl1+b2FhgaCgIFhYWGDAgAFwd3eHpaUl1q1bh8uXL4v23LVNmzbYsWMHDAwMlJ7bDx48wPfffy/aHubZ2dlYu3YtTExMYGNjgxUrViAkJARVq1bFwoUL0apVK6EjFtK2bVvs2rVLtP38iEorzvijj3b+/HmEhIQolnE2bdoUw4YNE3Xz27i4OCxduhSNGjVC8+bNoaWlhWHDhqF69epYv369KAt/gwYNQsOGDVGhQgU0atQI/v7+CA0NhampaaGdOsUkPj4ev/zyi9KszzJlysDBwUG0J6oAlGawitngwYOxZcsWRT+lH3/8URI95rS0tBRXROvXrw8LC4tCxaasrCycOXMG3bp1EyDhh1WvXh2pqamFxsVW1LG2tlZs1GBjY1PsDGy5XI6yZcvip59+gouLixpTfphU2x1kZWXBy8sLYWFhAICjR49iyZIlyMjIwPLly0Vb2H727Bnu37+vmBWf33MuJiZGlDtDAkBCQoIkerMWVLlyZWRkZAAADAwMEBsbi27dusHQ0BAPHz4UOF3RoqKiVM4UHjJkiKg3CgL+b4nygwcP4Orqips3b6Jx48aiLT5ERkZi9+7dktokCHh33Mg/P2ncuDFiYmJgaWkJW1tbBAYGCpyuaOXKlUNqaioMDAyUxu/fvy+q9/eCtLS0lC7yTpkyBVOmTBEw0Yc5OTnBy8sLTk5OqFu3LrS1tZVuF+tmaVI7hhAVxMIffZTQ0FDMnz8fvXv3xg8//IDc3FxER0fD0dERy5YtQ48ePYSOqFL58uWhoaEBADA0NMSdO3fQpUsXmJmZ4f79+wKnU23cuHGYNm2aYulj586d0blzZ4FTfViHDh2wZ8+eQicep0+fRvv27QVKVTJPnjzB1q1bFTu2GhoaKgqwYvH06VPExcVBV1cXa9aswdChQyVR+HvfiBEjFD3+3hcXF4epU6eKdsMJFxcXzJ49Gy4uLrCwsFD0OPXz88P333+v1LuoTZs2guU8ceKE4rH90EzV/GbmK1euFF3hT6rtDnx9fXHv3j3s3r0bQ4YMAQBMmDABbm5uWLBgAZYuXSpwwsJ27NgBLy8v5OTkQCaTKZYVymQymJmZibbwJ5XerAV16dIF8+fPh5eXF9q1awdfX1907doVR48eRa1atYSOVyQdHR3cv38fDRo0UBq/du2aUr9WsSm4RNnR0RHh4eGYOnWq6JYo52vYsKFicxopadasGfbu3QtXV1eYmJjg3LlzcHBwEHVBG3hXvPbw8MCMGTMAvCv4Xb58GStWrBDtbtX5Lly4gKioKGRnZxdaEi6mlR/58pfQnj17FsD/rUCQy+WQyWSibNEgxWMIUUEs/NFH+e233zB//nwMGDBAabxNmzb45ZdfRFv4a9++PX755RfMmTMHFhYW2LhxIwYPHoyTJ0+icuXKQsdT6dq1a5LYDbKg+vXrIzAwEGfPnlXaAOby5cuwtraGm5ub4r6LFi0SMKmyK1euYPTo0WjatCnMzc2Rm5uLiIgIbNmyBUFBQbC0tBQ6IgCgd+/eGDVqlOLDeadOnYq8r5hOnv744w94eXl9MHfHjh3VnKzk5syZAwAqZ7ysWbMGa9asAQDBT1zf36yoJBsXde/eHf/888/njPQ/GTlyJH7++Wf4+PigZ8+e6N+/PzQ1NXH9+nXRvB5VCQ8Px5o1a5SawTdt2hTe3t4YOXKkgMmKFhAQgLFjx2LMmDGwtrZGaGgo3rx5gxkzZqB79+5CxyuSVHqzFjR79mz4+PggOjoaffv2xdGjRzFw4EBUqFBBlIXhfKNHj8acOXMwduxYyOVyXLx4Ebt378amTZtEPctowYIFsLe3VyxRBt6df+jo6MDX11eUS5TzH2tnZ2eVz20hLy4VZ9q0aRg7dizKly+Pvn37YsOGDbCzs0NKSgr69OkjdLwi/fTTT6hcuTI8PT2RkZGBMWPGoHr16nBychL1DPPFixdj8+bNMDY2LtT2RWz91vOJpXXOx5DiMYSoIPb4o4/SqlUr7Nixo9DSg4SEBAwYMEC0PTAeP36Mn3/+Gd27d8eQIUPg7OyMK1euQENDA56enqK8mufn54c///wTQ4YMUTkVXqwnfe8X9j5ETIW/gQMHokOHDpg2bZrS+LJly3DlyhVs375doGSFxcbG4tWrV3B0dMTq1auLXDootiuQERERyMvLw4gRIwrlzu/x16RJE5QtW1bAlCQmERERqFChApo3b46zZ88iNDQUVatWxYQJE1CzZk2h46lkaWmJkJAQNG7cWKlXVFRUlOK9R2xMTU1x5MgR1K9fHy4uLujXrx9sbW1x5coVzJ49G0ePHhU6okpS6c1aEmlpadDW1hZlv833nTx5EoGBgYqZ8QYGBnBychJly5R8FhYW2Lt3Lxo0aKD0mkxOToadnR1u3LghdMRCjI2Ni7xN6ItLH5KWloa3b9+iRo0aePz4MY4fP46qVavC1ta20KZeYpSeno7c3FylPsRi1aZNG8ydO1fURdXSQIrHEKKCpDediAQ1bNgwLFmyBL6+vqhWrRoAICMjAwEBARg6dKjA6Yqmq6urtMNpcHAw7t27h8qVKyt6kYjNr7/+CgAqdwoV80mft7d3kTMVb926VWhjBLGIi4vDsmXLCo0PHDhQdLu55X8g2Lx5s2JWZVpaGnJzc0XbPwz4v2L1iRMnULduXchkMknkfl9OTg5SU1ORm5sL4P/6oN2+fVvUH3ylKv858/LlS3Tq1AlWVlaincWQz9raGitWrMCSJUsUY8nJyViwYAG6dOkiYLKi6ejo4NmzZ6hfvz4MDQ1x+/Zt2NraQldXF48fPxY6XpGk0ptVleTkZPzxxx9ITEyEp6cnzpw5AwMDA1HPZgXePb+LK7iKkRSXKMfGxgod4X+mra2Nq1evIj4+HmXKlEHTpk3Rrl070Rf9pNjDXENDA2ZmZkLHKPWkeAwhKoiFP/ooV69eRWRkJL755hs0aNAAWlpaSExMxJs3b1C3bl0cOXJEcV+xXWlPS0vDvXv3kJOTo+iB8eLFCyQlJYly9pxUT/pcXFzw66+/Ks1QfPXqFZYvX47Q0FDcunVLwHRFq1evHiIjIwv187t58yZq1KghTKgPaNu2LTZt2oQNGzbg6dOnAN6dnNjb24uyr0u+evXqSTL38ePHMXfuXLx48aLQbTVr1mTh7xOTy+UICAjAxo0b8fr1axw9ehSrVq1ChQoVMGfOHNHODPXw8IC7uzvatm2LvLw8DBgwAK9evULnzp0xd+5coeOpZGtri5kzZ8LHxwdWVlaYMWMGmjdvjpMnTxb6oCM2UtyUJCIiAmPGjEHnzp1x9uxZZGZmIiEhAZ6enli+fLlo26YA77Jv374d8fHx0NLSgqGhIZycnGBiYiJ0tCJJdYmyFC80JSQkYMyYMXj27BkaNmyIvLw8JCYmon79+li/fj1q164tdESVpNrDfNiwYVi9ejW8vb1RoUIFoeOUWlI9hhC9j0t96aPs3r27yNuys7OVlqiIaQfXvXv3Kvp2FCTW2XPvbxTwPplMBi0tLdSsWVOUO1/Z29tDLpdj/fr1qFSpEnbu3IlffvkFlStXhru7u2hnvISFhcHb2xsjRoxQXD29efMmgoODMXXqVAwfPlzghIWtWbMGW7ZswaRJk2BhYYG8vDxcu3YN/v7+cHZ2Fu2HXqnmtrW1RZs2beDk5AR7e3usW7cOL168gLe3N8aNG4f+/fsLHbFU8ff3x8GDBzFjxgxMmTIF+/fvR1JSEjw8PNC1a1dFz0UxSElJKTSWm5uLM2fOIDc3F1ZWVihXrhwAce5YmJ2djbVr18LExAQ2NjZYuXIltm/fjmrVqmHhwoWKnkZi86FNSUJCQgROqNrgwYPRp08fDB8+XGnZ2MaNG7Fz504cOHBA6IgqbdmyBUuWLEHv3r3RvHlz5OXlITIyEkePHsXixYvRq1cvoSMWSWpLlD90oenMmTPqD1UCw4cPR61ateDt7a3oOff69WvMnj0bb9++xbp16wROqJq1tTV++umnQj3Md+zYgcDAQNG2O3BwcMD169chl8tRvXr1Qq0CxDYJQ8qkdgwhKoiFP/oo//77L9atW4d79+4pXYHMzs5GfHx8kcUqoX3zzTfo0aMHJk6ciIoVKwodp0R69OiBhw8fIi8vD1WqVIFcLserV68gk8kUH3DMzMywevVqUe0CmJWVhSlTpiApKQlfffUV4uLi4OrqihEjRoi+d1FYWBi2bNmC+Ph4aGtrK97UbW1thY6mUpcuXTBv3rxCy66OHz8OHx8fnDp1SqBkxZNqblNTUxw6dAgNGjTAjz/+CHt7e3Tr1g1nz56Fr68v9u/fL3TEUsXGxgaLFy9GmzZtlIojV65cwaRJk3Du3DmhIyoYGxurXIL8fiFKzDsWpqenIzQ0FAkJCcjKyip0u5j6sb7P2toa/fv3V7kpSc+ePTFq1CihI6pkbm6O/fv3Q09Pr1C/qF69eol2Z/MuXbpgypQphTZN2b59O9avX88iwyck1QtN5ubmCAsLg6GhodJ4fHw8Bg4cKNpe4FLtYV7chAxAXJMwiEhYXOpLH2X27NlISkpCjx49EBQUBGdnZyQnJyM8PByzZs0SOl6RXrx4AUdHR8kU/YB3b9Z//vknFi9eDAMDAwDvegK5u7vD2toaffv2haenJxYsWAA/Pz+B0/6fsmXLwt/fH97e3ti+fTs2b96M1q1bCx2rRPr06YOvv/5asbT3+vXrou1JCLxbvl5waTIAGBgY4NmzZ+oPVEJSzV25cmXFrGEDAwPExsaiW7duMDQ0xMOHDwVOV/qkpqaqvKhRuXJlpKenC5CoaFIveEydOhXXr19Hx44dFTMTpeDJkyfo168fypYti+bNm+PGjRuwtbWFu7s7Zs+eLdrCX7169RAVFQU9PT2l8T///LNEO3ELJS0tDS1atCg03rp1a6WelmIjxcJ2cnIy1q5diwYNGsDU1BT//vsvunXrhjJlysDX11e0hb9mzZrh3LlzhQp/UVFRxW5YIjSp9jAvrrCXnZ2txiSlmxSPIUQFsfBHHyUiIgJBQUGwsLDAuXPn8M0338DS0hLr1q3DmTNn4OjoKHRElbp27Yrw8HCMHDlS6CgltmnTJmzcuFFR9AMAPT09zJ49G05OTnB2dsakSZMwZMgQAVO+4+DgUGi2i1wuh4aGBiZMmAAjIyPF+PubrIjJ7du3MXbsWPTq1QszZswAAEyfPh1yuRxr165V+h3EwsLCAkFBQfDy8lI0zc7NzUVgYKComz1LNXeXLl0wf/58eHl5oV27dvD19UXXrl1x9OhRUc26LS3at2+PwMBAeHl5KcbS0tKwfPlytGvXTsBkhYm5WFMSly5dUry3S4lUNyWZPHkyZs2ahaioKOTm5mLPnj14+PAhDh48CF9fX6HjFWn48OFYunQpfH19UblyZQBAZmYm/P39MXjwYIHTFU2KhW2pXmjq2LEjli1bhsuXLys2H7t9+zYOHDgAOzs7+Pv7K+4rpp6+Uu1h/vTpU6xdu1ZyK7GkRorHEKKCWPijjyKXyxW74DZu3BgxMTGwtLSEra0tAgMDBU5XNF1dXaxYsQKHDx+Gvr5+oSWnYr1S8/z5c5Vj+W/uAESxw2VRH8LF9uG8OF5eXujevbtSk95jx45h4cKF8PLyEt3OvgDg5uaGYcOG4fz584qZibdu3UJWVhY2bNggcLqiSTX37Nmz4ePjg+joaPTt2xdHjx7FwIEDUaFCBSxdulToeKWOp6cnxo8fj06dOiEzMxPjxo1DSkoK6tati99++03oeKWKoaEh3r59K3SMjybVTUm6d+8OPT09BAUFwcjICCdOnICBgQG2bt2Kli1bCh1PibW1teI8Qy6XIyUlBV9//TX09PRQpkwZJCUlITMzU9Sbe0ixsC3VC02XLl2CmZkZXrx4obTrdsuWLZGUlISkpCQA4jh3fd+gQYMwaNAgoWN8NHd390IrsZKSknDs2DFRr8SSGikeQ4gKYuGPPkqzZs2wd+9euLq6wsTEBOfOnYODg4Oorz4CwMuXL9G7d2+hY3yUgQMHYubMmZgyZQpMTU0hl8tx69YtrFq1Ct9//z2eP3+OpUuXom3btkJHLXTVNjU1Fa9evVLMVjx06BDatGmDmjVrChGvRG7fvg1fX1+lonCZMmXg6OiIvn37CpisaI0aNYK7uztevHiBhIQEaGtr49SpU/Dz8xP1khqp5v7zzz8xY8YMxTKgZcuWwdPTE9ra2qLvXylFtWvXxs6dO3HhwgUkJCQgJycHBgYGsLKyUswUpU9j8eLFGD9+POzs7FC3bt1Cj2/Bnm5iMX36dFSqVAnPnz+HjY0NBg0ahHnz5ik2JREzY2NjUc/uyzdhwgShI/xnUixsS/VCkxgvkpaEVHvhSXUlltRI8RhCVBA396CPcvXqVYwdOxY//fQT+vbtCzs7O1SrVg0pKSno06cP5s2bJ3TEUkMulyMgIADbtm3DkydPAAC1atXC8OHD8eOPP+L8+fMICQmBh4eHqK7+XrhwAT/99BOcnJwwceJEAMCQIUMQHx+PgIAAWFpaCpxQNVtbWzg7OxdarrRnzx6sWbMGx44dEyhZ0YKDg7FixQrMnTtXcdK6ZMkShISEYNasWaJdeiXV3G3atEFISEih3kX0ed2/fx/x8fHQ0tKCoaFhob5o9N/5+PggODgY1atXh7a2ttJtMplMVEvb3iflvkt79uzB9u3blZ7bTk5O6Natm9DRSp24uDjJFbYPHDiATp06KS40Ae9aHUjhQtPt27cRFxeHvLw8AO/OZ7OyshATE4P58+cLnE61V69eISgoCFFRUcjJyUHBj8dibVNjbm6OQ4cOoW7dupg5cyZMTU3h4OCA5ORkDBw4EJcuXRI6YqkgxWMIUUEs/NFHS0tLw9u3b1GjRg08fvwYx48fR9WqVWFrayvaWRhyuRwnTpxAXFyc0jLZ/BMRMS8vBN4t79XU1ESlSpUAAI8fP1YsuRabfv36oWfPnhgzZozS+Nq1axEeHo5du3YJlKx4e/fuxezZs2FnZwdTU1MAQGxsLPbt24d58+aJspG2tbU15s6di65duyqNnzhxAosWLcLx48cFSlY8qeYeP348mjRpgrFjx6Js2bJCxyn1/v77b8yYMQMRERGKnc1fv34Na2tr+Pj4oGrVqkJHLDUsLCzg4+ODnj17Ch3lo4wdO7bYvktiLfytXLkSf/zxBxwdHdG8eXPk5eUhMjISwcHBmDhxIpycnISOqNL7y37fJ5PJoKWlhZo1a8LW1hb29vYCpCuaFAvbUr3Q5O/vD39/f9SoUQOpqanQ1dXF06dPkZubi+7du4tqM7r3jR07FlFRUbCzs1O5EaCY+hG+b+jQoejcuTNcXV2xceNGXLx4EQEBAbhw4QImT57Mwt8nIsVjCFFBXOpLH61ixYqKN0VdXV0MGzZM4EQf5u3tjZ07d6JZs2aIjIyEhYUFkpKS8PTpU9GdoOZzd3eHj48PZDKZ4opvbm4ugoKC8Ntvv+HatWsCJ1TtwYMH+O677wqN29ra4tdffxUgUcn07dsXOjo62LFjB7Zt2wZNTU3o6+sjMDBQtLsSP3/+XGUfKwMDAzx9+lSARCUj1dypqan49ddfERAQAB0dnUInfzzx+7TmzJkDDQ0NnDhxQrF5xoMHD+Du7g4PDw/RfoCUomrVqqFx48ZCx/hoUu27FBISgiVLlihd/LCxsYGxsTF8fHxEW/gbPnw4/P39MXz4cJibm0MulyM6OhrBwcEYMGAAatWqhd9++w1paWkYPXq00HEVdu7cieXLl0uqsN2uXTscOHBAcheaQkJCMH/+fPzwww+wtrbGpk2bUKVKFUyZMkXUfTfPnz+PLVu2iHqDMVWmTZuGsWPHonz58ujbty82bNgAOzs7xUos+jSkeAwhKoiFP/oiHDp0CMuWLUOPHj3w3XffwdPTEwYGBpg1a5Zot7u/du0aJk6ciOXLl0NLSwsXLlyAt7c3UlNTMW3aNKHjFcnQ0BCHDx+Gi4uL0rjYm60DQOfOndG5c2ekpaUhNzcXVapUETpSsSwtLbF69WosWrQI5cuXB/Buh8WAgABRfxCWau7BgwcXuQxZbI3KS4OIiAiEhYUp7ZjbsGFDeHh4iGI389Jk3rx58PLywk8//YT69etDQ0ND6fa6desKlKx4Uu27JJfLUadOnULjBgYGyMzMFCBRyezZswfe3t7o1auXYszGxgZNmzZFQEAA9uzZAxMTE8yZM0dUhT8pFraleqHp+fPn6Ny5MwDAxMQE169fR58+fTBlyhRMnDgR06dPFziharq6uqJdtVQcExMTnDp1Cm/fvkW1atWwa9cupZVY9GlI8RhCVBALf/RFSEtLUyzfbNKkCSIjI2FkZAQXFxf8+OOPAqdT7Y8//oCrqytGjRqF6tWr4+jRoxg8eDAmT54s6oLU5MmTMW7cOJw7d06xY+udO3dw5coVrF69WuB0xdu0aRM2bNigmHWmo6MDe3t70S7x8PDwwMiRI2FlZYWGDRsCAJKSklCjRg1Rz66Uau7Vq1cXu8wtLCxMlMvcpKpRo0a4e/duoZPt5ORkpWIg/Xf5F2qcnZ2VnuNyuRwymQy3b98WKlqxpLopyfjx4zFv3jwsXLgQjRo1AvBuabuPjw/Gjh0rcLqiJSUlqdyAycjICAkJCQDeFedTU1PVHa1YUixs519oSk9PV/ScK1OmjKLli1jp6uoiOTkZdevWRaNGjRATE4M+ffqgYsWKePbsmdDxijRjxgx4enpi4sSJ0NfXL9RHUYzPEQDo3bs3/P390axZMwDSWYklNVI8hhAVxMIffRH09PQQExODunXrwsjICJGRkRgwYICiZ5QY6ejoYNOmTZgyZQqOHDmCwMBAdOjQQehYH/T1119j9+7d2LVrFxISEqCpqQljY2PMnz9f1E3516xZgy1btmDSpEmwsLBAXl4erl27Bn9/f5QtW7ZQz0IxaNCgAQ4dOoSzZ8/iwYMH0NTURMOGDWFlZVXopERMpJpbqsvcpGTPnj2Kv7dv3x6zZ89GTEwMWrRoAQ0NDdy5cwcbN26Es7OzcCFLIbHOHvqQHTt2IDExEdu2bVPZd0lMhT9jY+NCRdXevXujfPnyKFOmDN68eQOZTIZ79+6J9oKkubk5Vq9ejYULF6JChQoA3m2wsmbNGsUSydOnT0NfX1/ImIVIsbDdq1cvLF26FNu2bUNOTg4AQFNTE3Z2dqLdIAMABg0ahKlTp2LhwoXo1q0bnJycUKtWLZw7d05l0Vgs8nevfv9cTyaTifo5AgBlypQR7cql0kSKxxCigri5B30RQkND4ePjg4ULF6Jp06bo378/Bg4ciOvXr0NHR0c0m3u4ubkVGsvNzcXhw4dRt25dtGrVSjEu1qblUtWlSxfMmzcP1tbWSuPHjx+Hj48PTp06JVAyEos+ffrAxcVFaZkbABw5ckSxzO38+fOYM2cOTp48KVBKaSv4+isKm2kTIK1NSS5fvlzi+7Zt2/YzJvnfJScnY+zYsfjnn3/QsGFDyOVyJCYmok6dOli9ejUePXoEV1dXrFq1qsSvZXV49OhRsbeLcQbxggULcObMGcydO1dxMfL69etYsGABunXrhpkzZwodsUh79uxRXGjfuXMnjh49iqpVq8Ld3V20m5VI8TkCvHuehIWFoWvXrqhXr16hfpBiXbEiNXfv3sVXX31V5O1ifX4QvY8z/uiLMGjQIDRs2BBfffUVatWqheXLl2Pv3r1o0aKF6N8UNTQ00Lt3b6FjFMvR0RH+/v6oXLkyHBwciu13tnnzZjUmK7m0tDTFstP3GRgYiHp5CqmPVJe5SQkLpvQxpNR3SazFvI+hp6eHffv24cKFC7h79y40NDRgZGSEDh06QCaToUqVKjh9+jR0dHSEjqrkwYMH6NSpU6HxlJQULFiwQJQtJg4cOIBVq1ahXbt2irEuXbpAW1sb06dPF23hLy8vD/fv34evry+eP38OuVwOXV1d9OjRQ7RFP+D/Cjfnzp1DfHw88vLyYGBggI4dOxZa9ismd+7cQfPmzfHkyRM8efJE6Tb2Hv50xo4dq7SkmkiKWPijL0J2djYuXbqE7du3Kz6U165dGyNGjEDNmjUFTvd/Cs7iy8nJwcuXL1G9enUAwPXr19G8eXPR7fDWtm1bxYnR+yepUmJhYYGgoCB4eXkp+kTl5uYiMDBQcru80ech1WVuUmVtbY2ePXuiV69eMDExEToOiZBU+y79/fffWLZsGWJjY5GZmYmCi2/EPJtVQ0MDVlZWsLKyKnSb2Ap++caNGwdfX198++23AN6dE65fvx7r1q1D06ZNBU6nmlwuV5z7vU9HRwdv3rwRIFHJLFq0COHh4Zg+fTpMTU2Rl5eHqKgo+Pn5ISsrS7QX2//55x+MGzcO9+/fh4GBAXJzc5GYmIi6devi999/h66urtARVQoODhY6wheBS6qpNOBSX/oieHh44OzZs5g0aRKaNWuGvLw8REZGws/PD/3798fUqVOFjljI7du3MXbsWPTq1QszZswA8G73PABYu3atZGY5SEV8fDyGDRuGChUqKDYluXXrFrKysrBhwwZR96Yh9ZDqMjepOnr0KI4cOYI///wTurq6iiJg/mYIRO8fl6XUd8nBwQEvX77EwIEDVW7W8P333wuQ6sNiYmKwYMECREVFKfrOvU+sj/fx48fx888/w93dHbVr14a3tzfS09Mxbdo00T7WkyZNQmZmJpYtW4aKFSsCAF69eqU4HwwICBAyXpHatm0Lf3//QjNcz58/j+nTp+P8+fMCJSueq6srcnJysGzZMsUGes+fP8fPP/+MChUqwM/PT+CEqvn7+6scz990rFatWujcubPKIjKVHJdUU2nAwh99ESwtLbF27Vq0bt1aafzcuXOYOnUqLl26JFCyotnb26N58+aYOXOmYjZdXl4eFi5ciDt37oj2Kl92djbCwsKKnMkg5t6E+/btw4sXL5CQkABtbW1s3boVfn5+LOKQQm5ubpHL3PKXhIt11otUvX37FqdOnUJ4eDjOnz8PXV1d9O7dGz179kT9+vWFjkcCkmpfLjMzM+zatQtGRkZCR/ko/fr1Q6VKleDs7KwoRr1PzMuZr127BldXV7x+/RrOzs5wdXVV+TuIxePHj+Ho6IgnT57AwMAAAHD//n3o6enht99+E+1z++uvv8aqVatgYWGhNB4dHQ0XFxecO3dOoGTFs7CwQEhICJo0aaI0Hhsbi2HDhuHq1asCJSvetGnTcOjQIdSuXRumpqaQy+W4ffs2UlJSYG5ujtevX+Off/7Bhg0bYG5uLnRcyXJwcCjyNplMJto2RkTv41Jf+iJUrFgRmpqFn+6VKlVSOS4Gt2/fhq+vr1JvkTJlysDR0RF9+/YVMFnxPDw8cOTIEXTq1EnlTAaxCg4OxooVKzB37lx4enoCePd4T58+HbNmzcLgwYOFDUiiIMVlblJXrlw5fPvtt6hatSp0dHSwc+dObNy4Eb/++itatWqFuXPnKj4Y05dFrMWPD9HX18fLly+FjvHREhISsH//fkm0M4iIiCg0NmXKFPj4+CAnJwexsbGKC5Nt2rRRd7wP0tXVxYEDB3DmzBnFxUgDAwN06tRJ0Y5EjGbMmAF3d3fMmDEDFhYW0NTURGxsLHx8fDBixAikpKQo7iumpfhVqlRR+Zp89eqVqHv8AcDAgQPh6empaHWQl5cHHx8fpKenY9GiRQgICMDixYuxfft2gZNKl1gnWxB9DM74o1Lr/ZOLw4cPY8eOHZg9ezZatGgBDQ0N3L17F15eXhg6dCiGDBkiYFLVbG1t4ezsXKjgtGfPHqxZswbHjh0TKFnxLCws8Ouvv6JDhw5CR/ko1tbWmDt3Lrp27ao0fuLECSxatAjHjx8XKBnRlykvLw8XL17EkSNHcPz4ceTm5qJ79+7o1asX2rVrh/T0dMybNw+3bt3CkSNHhI5LVKz3C1ERERHYuXMnXF1doaenV6g3oRgLUQDwww8/wMXFRRKz4EvankPMS8KlSNXy+/c/aspkMlEuxV+6dCmOHz8OT09PtGjRAgBw8+ZNeHl5oVOnTvDw8BA4oWoWFhYICwsrdPHr/v376N+/P65fv47k5GTY2dnhxo0bwoQsJZKTk/HHH38gMTERnp6eOHPmDAwMDGBpaSl0NKISEedUJ6JPwNrautBJx5gxYwqNzZ8/X5SFv7Fjx2L27Nm4fv06TE1NAbxbcrBv3z7MmzdP4HRFq1SpEmrVqiV0jI/2/PlzNGjQoNC4gYEBnj59KkAioi9bhw4dkJWVhW+++QZeXl74+uuvlfrqVKxYEd27d8fNmzcFTElUMqqWis2dO7fQmNgKIu/r27cv5syZg/79+0NfX7/QTKh+/foJE0yF2NhYoSN8kcS8MU1xJk2ahNTUVPz444+KzwcaGhoYNGiQoq+iGNWoUQNXrlwpVPi7evUqqlatCgB4+vSpqJe1S0FERATGjBmDzp074+zZs8jMzERCQgI8PT2xfPly9OjRQ+iIRB/EGX9Uan2o98/7xLpc6OzZs9ixYwfu378PTU1N6Ovrw8HBoVCvQjHZvn07wsPD4enpCT09PaWG62I2atQoVKxYEYsWLUL58uUBAJmZmZg7dy7+/fdf/P777wInJPqy7N+/HzY2NoodlIlIWMXN9JPJZKIu+sTHx6NWrVqoVKkSzp49i5MnT6JZs2YYNGiQ0NFIRF69eoUHDx6gbNmyaNCggejff/bt24fZs2ejV69eaNGiBeRyOW7duoWDBw/Cw8MDlpaWcHFxQdeuXeHm5iZ0XMkaPHgw+vTpg+HDh8PCwgL79u2Dnp4eNm7ciJ07d+LAgQNCRyT6IBb+iOg/MzY2LjSTsmDBT4xLO96XlJSEkSNH4vnz52jYsKFirEaNGvj111/ZQ4xIAK9fv8a+fftw//59jBs3Djdv3kSjRo1Uzs4lkgobGxvs2rVLMSMn3+PHj9GvXz9cuHBBmGClVEhICLy8vPD777+jYsWKGDx4MNq3b4/Y2FgMGjQIkyZNEjoiiYBU32+uXLmCbdu2KTYda9y4MYYPHw5zc3NERkbixo0bGDZsWKGWAlRy5ubm2L9/P/T09JQKf8nJyejVqxciIyOFjkj0QVzqSyRSGRkZCAkJwb1795Cbm6sYz8rKQkxMDA4fPixgOmUFd7NKT09HmTJlUK5cOcjlcvj4+GDatGmKmXRi1KBBAxw6dAhnz57FgwcPoKmpiYYNG8LKyoonS0QCuHv3LkaMGIE6deoo/h4eHo4jR45g7dq1ot5BlKigI0eO4PTp0wDerUjw8vKCtra20n0ePXok6vcbVRtmvE+svQk3bNiAJUuWoG3btvD29oaJiQk2bNiAiIgITJkyhYU/kvT7TevWrYtcCWRmZgYzMzM1Jyp96tWrh6ioKOjp6SmN//nnn6JdNUZUEAt/RCI1Z84cXLhwAR06dMCRI0dga2uLxMREREVFYfz48ULHU/L+CdH7u+N+//33AIBOnTphypQpmDVrlqhPnsqWLQsbGxuhYxARgAULFsDe3h4TJ06EhYUFAGDRokXQ0dGBr68vdu7cKXBCopJr27atovAHKG94kM/IyAjTp09XZ6yPoqpPIfDuvbNmzZqiXer7+PFjRQP+U6dO4YcffgAA1K5dG2/evBEyGomElN9v9u3bh40bNyIpKQm7d+9GcHAwatSogTFjxggdrdSYPHkyZs2ahaioKOTm5mLPnj14+PAhDh48CF9fX6HjEZUIC39EInXmzBmsWrUKHTt2RFxcHJycnGBqaorFixcjLi5O6HhF+v333/HLL78o7Y47c+ZMtG7dGosWLSq0SzERkSpRUVFYsGBBofEhQ4Zg69atAiQi+t/p6Ohg0aJFAN7NHhk5cqTo+4cVVHDDjNzcXCQlJcHb2xt2dnYCpfowQ0ND7N+/Hzo6OkhJSUG3bt2QnZ2NoKCgEu/+S6WbVN9v/vjjD/z6668YO3Ysli5dCgBo3rw5Fi5ciKysLNFNFJCq7t27Q09PD0FBQTAyMsKJEydgYGCArVu3omXLlkLHIyoRFv6IRCozM1PRa87IyAjR0dEwNTXFDz/8gOHDhwsbrhjcHZeIPgUdHR3cv3+/0PHk2rVrqF69ukCpiP678ePH48mTJ1i7di3i4+ORm5sLQ0NDDBo0SPG+LwUaGhowMDDArFmzMGbMGMUsf7GZOXMmJk+ejJcvX2Lo0KFo1KgRvLy8cOzYMQQEBAgdj0RAqu83wcHBWLBgAb755hv88ssvAN7tvl21alV4eHiw8PeJ7NmzBz179iw0uy89PR2bN2+Go6OjQMmISq6M0AGISLVGjRrh/PnzAN4V/q5evQrgXfPhzMxMIaMVy9LSEqtXr0ZGRoZiLDMzEwEBAYrlE0REHzJ69GjMmTMHW7duhVwux8WLF+Hn5wcvLy84OzsLHY/of3blyhV8++23uHTpEurXr4/69esjIiICffv2VbzXS0lqaipevXoldIwidejQARcuXMClS5fg4eEBABg3bhxOnToFU1NTgdORGEj1/SYlJQWNGjUqNK6np4cXL16oP1Ap8uzZM6SkpCAlJQVubm6Ii4tTfJ3/5+LFi1i2bJnQUYlKhDP+iERq/PjxmDRpEvLy8tC3b1/06tULY8eOxZ07d9C5c2eh4xXJw8MDI0eOhJWVlcrdcYmISmLIkCGoVasWAgMDUa5cOfj6+sLAwADe3t7o2bOn0PGI/meLFy/G8OHDMW3aNKXxZcuWYenSpdi+fbtAyYrn5uZWaOzNmzc4d+4cvv32WwESlYxUNyUh9ZHq+03Lli2xZ88eTJgwQTEml8sRFBTETT3+o8uXL2Py5MmQyWQAgIEDBwJ49/jKZDJFn9Y+ffoIlpHoY8jkqroLE5EoJCcnIy8vD/r6+oiNjcXevXtRrVo1ODg4iHqH3KysLO6OS0REpELLli2xd+/eQst6Hzx4gL59++LmzZvCBPsAVYU/LS0tmJmZoW/fvtDS0hIg1YcV1cdP7JuSEH3I3bt3MWbMGFSvXh2xsbHo0KEDHjx4gLdv32L9+vUwMTEROqKkpaSkIC8vD926dUNoaCh0dHQUt8lkMpQvXx7VqlUTMCFRybHwR0RERKJ04cIFREVFITs7u9AuqOxdRFLVs2dPjB07ttBMkb1798LPz0+0hag3b94gNDQU9+/fR1ZWVqHb8zcvEbuCm5KItTchqY+qojbwrrijpaWFmjVrokePHmjSpImak31YZmYm9u3bh4SEBOTm5sLAwAB9+vTBV199JXS0L0J2drZoL3oQvY9LfYlExNraWjGl/EPE+sGAiOhTWLx4MTZv3gxjY+NCH2BKepwkEqNRo0Zh3rx5iI+PV+wIefPmTWzevLnQ8l8xmTZtGm7cuIEOHTqgXLlyQsf5n0llUxJSn6+++kqxQ6u5uTnkcjlu3bqFK1euoFu3bvjnn3+wfv16rFy5El27dhU6rhJtbW0MGjRI6Bil2tOnT7F27Vrcu3cPubm5AN4t+c3OzkZ8fPwH2wkQiQELf0Qi8n6Pjve9fPkSZcuWFfXyXiKiT2nXrl1YvHgx++dQqdO/f3/IZDIEBwdj06ZN0NbWhoGBARYtWoTvvvtO6HhFunTpEoKCgkrNRl1i35SE1CcxMRGurq6YOHGi0nhAQABu3LiBtWvXIjQ0FKtWrRJV4e/KlStYsGABEhISkJ2dXej227dvC5Cq9HF3d0dSUhJ69OiBoKAgODs7IykpCceOHcOsWbOEjkdUIiz8EYnI+1eds7KysG7dOmzfvh1Pnz6FTCZD7dq14eTkhBEjRgiYkojo89PQ0GBzciqV0tPT8erVK5iamqJp06aK8dOnT+P06dOiXTJraGiIt2/fCh3jo0l1UxJSn4iICMyePbvQ+HfffafYmK5Tp05YsGCBuqMVa/bs2TAyMsLUqVMlPQtX7CIiIhQXPc6dO4dvvvkGlpaWWLduHc6cOQNHR0ehIxJ9EAt/RCK1YMEC/PXXX5g+fTqaNWuGvLw8REZGws/PD6mpqZg6darQEYmIPpthw4Zh9erV8Pb2RoUKFYSOQ/TJTJ06FdevX0fHjh0l9WF98eLFGD9+POzs7FC3bl2UKVNG6fZ+/foJE+x/ULVqVbi5uaFv375CRyER0NPTw9GjR+Hi4qI0fuzYMdSpUwfAu8133t/cQQyePHmCgIAAGBgYCB2lVJPL5dDV1QUANG7cGDExMbC0tIStrS0CAwMFTkdUMtzcg0ikLC0tsXbtWrRu3Vpp/Ny5c5g6dSouXbokUDIios/PwcEB169fh1wuR/Xq1Qs1z2afU5IqCwsLSS6Z9fHxQXBwMKpXrw5tbW2l22QymWhfk6VlUxL6fP766y+MGzcOLVu2hKmpKQAgOjoaN2/ehJ+fH3R1deHk5ISRI0cWKg4KycPDA7Vq1eJmV5/Z0KFD0blzZ7i6umLjxo24ePEiAgICcOHCBUyePJmfyUgSOOOPSKQqVqwITc3CL9FKlSqpHCciKk369++P/v37Cx2D6JOT6pLZnTt3Yvny5ejZs6fQUT5KadmUhD4fKysrHDx4EKGhobh79y40NDTQqlUrLFmyBHXr1kVcXBwWLlwIGxsboaMqGTVqFAYOHIiwsDDUq1ev0MZXmzdvFihZ6TJ9+nS4uLigfPny6Nu3LzZs2AA7OzukpKSwDzFJBmf8EYlISkqK4u+HDx/Gjh07MHv2bLRo0QIaGhq4e/cuvLy8MHToUAwZMkTApERERPS/iIuLk+SSWWtrawQEBKBJkyZCR/koUp1hSfQhgwcPxqtXr9CtWzeVRW3OBPw0hg4dik6dOqFDhw5o1aoVHj9+jOPHj6Nq1aqwtbUtdAwnEiMW/ohExNjYWHG17v2XZsExmUzGnbqIqNRxcHAoNGOhKJzJQFIl1SWzp0+fxvr16/HTTz+hfv360NDQULq9bt26AiUr3oABAzB9+nR06NBB6CgkIo6OjvD390flypU/+N4j1vebli1bIiwsDI0aNRI6Sqm2Y8cOnDt3DpcuXYKGhgY6deqEzp07w8rKCtWqVRM6HlGJcL0gkYiI9WSfiEgd2rVrp/j78+fPERISgm7duqFFixbQ0tLC7du3cejQIQwbNkzAlET/jVSXzOb3NnN2dlYqksjlclFfkCxNm5LQp9O2bVtF79j89x65XI4XL15AJpOhatWqAqYrGUtLS8THx7Pw95kNHjwYgwcPhlwuR0xMDC5cuICwsDC4ubnBxMQEoaGhQkck+iAW/ohEpF69ekJHICISzPvLkpycnODu7o6hQ4cq3adNmzYICQlRdzSiT6ZatWpo3Lix0DE+mlQvTu7YsQOJiYnYtm2byhmWLPx9md5/v3F1dYWfnx9CQ0Px7NkzAICuri6GDRuGMWPGCBXxg6ysrODu7o7w8HDo6ekVmoXLpb6fTm5uLm7duoVr167h5s2buHv3LsqWLYvKlSsLHY2oRLjUl4iIiETH3Nwcu3fvhoGBgdJ4fHw8BgwYgBs3bggTjOg/kuqSWamysLCAj4+P5GZYkvosXLgQ4eHhmDhxIkxNTZGXl4eoqCj4+fnhhx9+EG0BzcHBocjbZDKZaJcoS42DgwOio6NRuXJltGzZEq1atULr1q3RrFkz9vcjyeCMPyIiIhKdZs2aYd26dfD09FTM0klLS4Ofnx/Mzc2FDUf0H0h1yaxUSXWGJanP7t27sWbNGrRt21YxZmxsjHr16mH69OmiLfwFBwcLHeGLoKmpCZlMhmrVqqFWrVrQ1dWFrq4ui34kKZzxR0RERKITHx+PMWPG4OXLl9DX14dcLseDBw9Qt25drF27lq0RSLIePXpU7O18bn9anGFJH/L1119j1apVhXZ+jo6OhouLC86dOydQsg+LiYlBYGAgEhISkJubCwMDAwwbNkypiEn/XU5ODm7duoWIiAhcuXIFN27cQKVKldC6dWssWrRI6HhEH8TCHxEREYlSVlYWzp8/j/j4eACAkZEROnbsCE1NLlggopIxNjZW/J0zLEmVAwcOYM2aNZgxYwYsLCygqamJ2NhY+Pj4wNbWFr1791bcV0yF4mPHjmHKlCno0aMHLCwskJubixs3buD48eNYuXIlunXrJnTEUufevXu4dOkSLl26hFOnTqFGjRo4deqU0LGIPoiFPyIiIiIiKpU4w5I+RFVx+P2PyDKZTJSF4t69e2PgwIFwcnJSGt+4cSN2796NvXv3ChOslNm6dSsuX76MK1euID09HW3atEGnTp1gZWXFHZVJMlj4IyIiIlEwMTHBX3/9herVq8PY2Fhpdk4+uVyOMmXKICYmRoCERERU2nyoOPw+MRWKW7ZsiX379kFfX19pPDExEXZ2doiMjBQoWenSt29fWFlZwcrKCpaWlihbtqzQkYg+GtfKEBERkShs2rQJVapUAQC4ubnBxMSk0H1ev34NPz8/dUcjIqJSSkzFvI/RqFEjnDlzptDuvqdPn5bs7yRGnDlJpQELf0RERCQK7zcjX7NmDQICAtCqVSvF2N69e7F06VJebScioi/ehAkTMGHCBNy8eRMtW7YEANy4cQNHjx6Fr6+vwOmISEy41JeIiIhEJzg4GCtWrMDKlStRp04dzJ8/H9HR0Rg1ahRGjx4NbW1toSMSEREJ6uLFi9iyZQvu37+PzMxMtGzZEiNGjICZmZnQ0YhIRDjjj4iIiETHwcEBNWvWxOTJk5GVlYVu3brh0KFDotpRkYiISCh5eXm4ePEirl27hmfPngEAsrOz0bRpUxb+iEgJC39EREQkCikpKUpfm5mZYd68efDw8ICFhYXSfVgAJCKiL9miRYsQHh6O6dOnw9TUFHl5eYiKioKfnx+ysrIwfvx4oSMSkUhwqS8RERGJQnE7+QKATCaDXC6HTCbD7du31R2PiIhINNq2bQt/f3+l/rgAcP78eUyfPh3nz58XKBkRiQ1n/BEREZEonDhxQugIREREklCuXDloaWkVGq9cubLKi2hE9OXijD8iIiIiIiIiCTlw4ADWrFmDGTNmwMLCApqamoiNjYWPjw9sbW3Ru3dvxX3ZHoPoy8bCHxEREREREZGEGBsbK/6eP8Pv/Y/2bI9BRPlY+CMiIiIiIiKSkEePHpX4vvXq1fuMSYhI7Fj4IyIiIiIiIiIiKoXKCB2AiIiIiIiIiIiIPj0W/oiIiIiIiIiIiEohFv6IiIiIiIiIiIhKIRb+iIiIiIiIiIiISiEW/oiIiOiLYG1tjaZNmyr+GBsbo1WrVhg+fDgiIiI++c+7dOkSmjZtiocPHwIAHBwcMGvWrBL92/T0dGzduvU//fyHDx+iadOmuHTpUony/S9Wr14Na2vr//nff6rvQURERESqaQodgIiIiEhdRo4ciZEjRwIA5HI5Xrx4geXLl2PUqFE4fPgw6tat+9l+9urVq6GhoVGi+wYFBSEsLAzDhg37bHmIiIiIqPTjjD8iIiL6YlSoUAE1a9ZEzZo1UatWLTRp0gTz58/H27dvcezYsc/6s6tWrYpKlSqV6L5yufyzZiEiIiKiLwMLf0RERPRF09R8twCibNmyAN4tCV6yZAl69uyJdu3a4fLly5DL5Vi/fj1sbGzQsmVL9O3bF/v27VP6PleuXMGgQYNgZmaGPn36IDY2Vun2gkt9IyMj4eTkBAsLC3Ts2BHz5s1DRkYGVq9eDX9/fzx69EhpKe6uXbtga2sLMzMz2NraYtOmTcjLy1N8v7t378LR0RHm5ubo3r07Lly48J8fm7t378LFxQVt2rSBqakpbGxsEBQUVOh+a9asQbt27dCqVStMnz4dL168UNz2+vVrzJ07F+3bt4elpSUcHR0RFRX1n7MRERER0YdxqS8RERF9sR4/foyFCxeiQoUK6NKli2J8y5YtWLt2LSpVqoSmTZtixYoVOHDgADw8PGBoaIiIiAh4enri9evXGDZsGJKTkzFy5Ej069cPixcvxr179+Dh4VHkz01OTsaIESPQvXt3hISE4PXr15g5cybmz5+PuXPnIj09HYcOHcLOnTuho6ODkJAQLF++HB4eHjAzM0NMTAy8vb3x+PFjzJgxA69fv1YUEUNDQ/HkyRPMnTv3Pz02GRkZGDlyJDp16oTt27dDQ0MDoaGhWLJkCTp06AATExMAwKNHj3Dx4kX8/vvvSEtLw9y5c+Hm5obffvsNcrkco0ePRrly5bB27VpUrFgRe/fuhb29PXbs2IFmzZr9p4xEREREVDwW/oiIiOiLsXbtWsWMtZycHGRlZaFRo0ZYuXKlUn+/Ll26oGPHjgDebbSxceNGLF++HN988w0AoEGDBnj06BECAwMxbNgw7NixAzVq1MC8efOgoaGBRo0a4e+//8aiRYtU5tixYweqVq2KhQsXKmYcLliwANevX8dXX32FChUqQENDAzVr1gQA/Prrr3B1dUWvXr0AAHp6ekhLS8P8+fMxadIkHDx4EBkZGVi8eDEqVaoEIyMjuLu746effvqfH6uMjAw4Ojpi2LBh+OqrrwAAEydOxIYNG3Dnzh1F4U9bWxsrVqxAjRo1AAAeHh4YOXIkEhMTkZKSghs3buDixYuoWrUqAGDq1Km4du0aNm/ejMWLF//P+YiIiIjow1j4IyIioi/GkCFD4ODgAAAoU6ZMkX339PX1FX+/d+8eMjMzMW3aNJQp839dUvILh2/fvsXdu3fRrFkzpc07WrVqVWSOu3fvonnz5oqiHwC0b98e7du3L3TfZ8+e4Z9//sHy5cuxatUqxXheXh4yMzPx8OFD3L17Fw0bNlT6XSwsLD70cBRLR0cHQ4cOxYEDBxATE4OkpCTF8uX3lxjr6+srin4A0LJlSwBAXFwcHjx4ALlcjq5duyp976ysLGRmZv6nfERERET0YSz8ERER0RejSpUqSkW9opQrV07x9/yNNlauXAlDQ8NC9y1btixkMplSMQyAUlGvoOJuKyj/+7q5uSlmIb6vTp06H/3zS+Lff//FDz/8AB0dHVhbW8PKygotWrRQWhINoNBOxbm5uQAALS0t5OXloWLFiggLCyv0/fN7KhIRERHR58PNPYiIiIiKYWhoCE1NTaSkpEBfX1/x5/Tp0wgMDESZMmVgbGyM6OhoZGVlKf5ddHR0kd+zcePGiImJURTJAODYsWOwtrZGZmYmZDKZYrx69erQ0dFBcnKy0s+/desWVq5cCQAwNjbGgwcP8OzZsxL9/JI4cOAAXrx4gW3btmHcuHHo3r07Xr58CUB51+EHDx4gLS1N8fXVq1chk8nQuHFjNGnSBGlpacjOzlbKvn79epw4ceI/5SMiIiKiD2Phj4iIiKgYlSpVwpAhQ7Bq1Srs3bsXycnJ2LlzJ5YuXYpatWoBAOzt7ZGRkQF3d3fEx8fj1KlTWL16dZHfc+jQoXj+/DnmzZuH+Ph4REREwNfXF+3bt4e2tjYqVKiAly9f4v79+8jJycHo0aMRHByMLVu2ICkpCceOHYOnpyfKlSuHsmXLolevXqhevTqmTZuG2NhYXL58GT4+PiX6/SIiInDmzBmlP4mJiahduzYyMjJw5MgRpKSk4K+//sLUqVMBQKnAmZmZicmTJyMmJgbnzp2Dt7c3+vXrh3r16qFz584wMTHBlClTcPHiRSQmJmLRokUICwtDo0aN/sN/FSIiIiIqCS71JSIiIvoANzc3VKtWDatWrcKTJ09Qp04dTJw4EaNGjQIA6OrqYtOmTVi4cCG+//571KlTB66urpg/f77K76erq4ugoCAsXboU/fr1Q5UqVdCzZ09FYa1Hjx7YsWMH+vTpgy1btmDkyJHQ1tZGcHAwFi9ejBo1amDw4MGYOHEiAKBChQrYtGkTvL29YW9vjypVqmDixIlwc3P74O82a9asQmPjx4/H+PHjcevWLSxevBhpaWmoV68eBg0ahBMnTiAqKgr29vYAAFNTU5iYmMDR0REymQw9e/ZUfE8NDQ3F7zl58mRkZGSgUaNG8Pf3R4cOHT7+PwQRERERfRSZ/P21GkRERERERERERFQqcKkvERERERERERFRKcTCHxERERERERERUSnEwh8REREREREREVEpxMIfERERERERERFRKcTCHxERERERERERUSnEwh8REREREREREVEpxMIfERERERERERFRKcTCHxERERERERERUSnEwh8REREREREREVEpxMIfERERERERERFRKcTCHxERERERERERUSn0/wD5aLgv67StaAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1400x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Confusion matrix saved\n"]}], "source": ["# Confusion Matrix\n", "print(\"\\n🔲 CONFUSION MATRIX\")\n", "print(\"=\" * 50)\n", "\n", "cm = confusion_matrix(y_test, y_test_pred)\n", "\n", "plt.figure(figsize=(14, 12))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=label_encoder.classes_,\n", "            yticklabels=label_encoder.classes_)\n", "plt.title('Confusion Matrix - Logistic Regression')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.tight_layout()\n", "plt.savefig('../results/figures/03_confusion_matrix.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"✅ Confusion matrix saved\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 SAVING MODEL\n", "==================================================\n", "✅ Logistic Regression model saved\n", "\n", "==================================================\n", "✅ LOGISTIC REGRESSION ANALYSIS COMPLETE!\n", "==================================================\n"]}], "source": ["# Save Model\n", "print(\"\\n💾 SAVING MODEL\")\n", "print(\"=\" * 50)\n", "\n", "joblib.dump(lr_model, '../models/saved_models/logistic_regression.pkl')\n", "\n", "print(\"✅ Logistic Regression model saved\")\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"✅ LOGISTIC REGRESSION ANALYSIS COMPLETE!\")\n", "print(\"=\" * 50)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 4}