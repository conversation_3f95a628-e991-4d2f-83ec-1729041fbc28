# ============================================================================
# Python .gitignore for Intelligent Agriculture - Crop Recommendation System
# ============================================================================

# ============================================================================
# Python
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
venv

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ============================================================================
# Machine Learning / Data Science Specific
# ============================================================================

# Trained Models (large .pkl files)
# models/saved_models/*.pkl
# !models/saved_models/.gitkeep

# Large datasets
data/raw/*.csv
data/raw/*.xlsx
data/raw/*.json
data/raw/*.zip
data/raw/*.tar.gz

# Preprocessing objects (large files)
# data/processed/*.pkl
# !data/processed/.gitkeep

# Temporary files
*.tmp
*.temp


# ============================================================================
# Jupyter Notebook Specific
# ============================================================================

# Notebook checkpoints
.ipynb_checkpoints/
*/.ipynb_checkpoints/*

# Notebook backup files
*.ipynb.backup
*.ipynb.corrupted

# ============================================================================
# IDE / Editor Specific
# ============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.iws
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
.\#*

# ============================================================================
# Operating System
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
ehthumbs.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.lnk

# Linux
*~
.directory
.Trash-*

# ============================================================================
# Project Specific
# ============================================================================

# Streamlit cache
.streamlit/

# Logs
logs/
*.log

# Compressed archives
*.zip
*.tar.gz
*.rar

# Ignore the non-related docs folder created during cleanup
docs/non-related/
# ============================================================================
# Keep These Files (Exceptions)
# ============================================================================

# Keep directory structure
!data/raw/.gitkeep
!data/processed/.gitkeep
!models/saved_models/.gitkeep
!results/figures/.gitkeep
!results/metrics/.gitkeep

# Keep important documentation
!README.md
!requirements.txt

# ============================================================================
# End of .gitignore
# ============================================================================
