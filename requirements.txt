# Core Data Science Libraries
pandas==2.0.3
numpy==1.24.3
scipy==1.11.1

# Visualization
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.15.0

# Machine Learning - Core
scikit-learn==1.3.0

# Machine Learning - Ensemble Methods
xgboost==1.7.6
lightgbm==4.0.0

# Dimensionality Reduction
prince==0.7.1

# Imbalanced Data Handling
imbalanced-learn==0.11.0

# Model Interpretation
shap==0.42.1
lime==*******

# Model Persistence
joblib==1.3.1

# Jupyter Environment
jupyter==1.0.0
ipykernel==6.25.0
notebook==7.0.2

# Utilities
tqdm==4.65.0
python-dotenv==1.0.0

# Statistical Analysis
statsmodels==0.14.0

# Additional Utilities
openpyxl==3.1.2
pillow==10.0.0

# Web Application
streamlit==1.28.0
